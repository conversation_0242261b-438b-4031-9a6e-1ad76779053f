{"name": "kashable-portal", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 8092", "build:test": "NODE_ENV=test next build", "build:prod": "NODE_ENV=production next build", "start": "next start", "lint": "next lint", "prettier": "prettier --write src/*"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@google-analytics/data": "^5.1.0", "@googlemaps/js-api-loader": "^1.16.6", "@mona-health/react-input-mask": "^3.0.3", "@mui/icons-material": "^6.4.0", "@mui/material-nextjs": "^6.2.0", "@mui/x-date-pickers": "^7.23.2", "@mui/x-date-pickers-pro": "^7.23.2", "@mui/x-license": "^7.23.2", "@react-google-maps/api": "^2.19.3", "@react-oauth/google": "^0.12.1", "@reduxjs/toolkit": "^2.0.1", "@tanstack/react-table": "^8.21.3", "@vis.gl/react-google-maps": "^1.3.0", "axios": "^1.9.0", "dayjs": "^1.11.10", "diff": "^7.0.0", "exceljs": "^4.4.0", "export-to-csv": "^1.2.3", "formik": "^2.4.6", "html-react-parser": "^5.2.5", "immutability-helper": "^3.1.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "next": "^15.3.3", "papaparse": "^5.4.1", "pdf-lib": "^1.17.1", "pusher": "^5.2.0", "pusher-js": "^8.3.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.2.9", "react-input-mask": "^2.0.4", "react-redux": "^9.1.0", "react-slick": "^0.30.2", "react-to-print": "^3.0.5", "react-zoom-pan-pinch": "^3.6.1", "recharts": "^2.13.0-alpha.3", "slick-carousel": "^1.8.1", "use-places-autocomplete": "^4.0.1", "uuid": "^9.0.0", "yup": "^1.4.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@types/diff": "^7.0.1", "@types/node": "^20", "@types/papaparse": "^5.3.15", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-grid-layout": "^1.3.5", "@types/react-input-mask": "^3.0.5", "@types/react-slick": "^0.23.13", "@types/react-table": "^7.7.20", "@types/sanitize-html": "^2.16.0", "@types/uuid": "^9.0.0", "eslint": "^9.17.0", "eslint-config-next": "^15.3.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "prettier": "^3.2.5", "sass": "^1.69.7", "typescript": "^5"}, "overrides": {"debug": "4.3.6"}}