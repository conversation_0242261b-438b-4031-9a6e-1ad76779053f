# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# JetBrains IDE
.idea/

# VsCode
.vscode/

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# VSCode
.vscode/

# Credentials
/service-accounts

.project

.settings/.jsdtscope

.settings/org.eclipse.wst.jsdt.ui.superType.container

.settings/org.eclipse.wst.jsdt.ui.superType.name
