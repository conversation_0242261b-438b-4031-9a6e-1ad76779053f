import type {NextConfig} from 'next';
import path from 'path';

const nextConfig: NextConfig = {
    reactStrictMode: false,
    sassOptions: {
        includePaths: [path.join(__dirname, 'styles')],
    },
    eslint: {
        dirs: ['src'],
    },
    output: 'standalone',
    webpack: (config, {isServer}) => {
        if (!isServer) {
            config.optimization.splitChunks = {
                cacheGroups: {
                    default: false,
                    vendors: false,
                },
            };

            config.optimization.runtimeChunk = false;
            config.output.filename = 'static/chunks/[name]-[fullhash].js';
        }

        return config;
    },
};

export default nextConfig;
