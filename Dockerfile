## final image
FROM 648912154521.dkr.ecr.us-east-1.amazonaws.com/kashable-docker:nodejs as dockerapp

WORKDIR /app
#USER kashable:kashable

#COPY --from=deps /app/node_modules ./node_modules
COPY --chown=kashable:kashable sbin sbin
COPY --chown=kashable:kashable . .

ENV NEXT_TELEMETRY_DISABLED 1

EXPOSE 8080/tcp

CMD sudo -Eu kashable /bin/bash -c "source sbin/set-environment $PROJECT && sbin/start-app"
