import {NextRequest, NextResponse} from 'next/server';

const isLoggedIn = false;

export function middleware(request: NextRequest) {
    const response = NextResponse.next();

    // const themePreferences = request.cookies.get('theme');
    //
    // if (!themePreferences) {
    //     response.cookies.set('theme', 'dark');
    // }
    //
    // response.headers.set('custom-header', 'custom-value');

    return response;
    // console.log('middleware');

    // if (!isLoggedIn) {
    //     // return NextResponse.rewrite(new URL('/login', request.url));
    //     return NextResponse.redirect(new URL('/login', request.url));
    // }
    //
    // return response;
}

// export const config = {
//     matchers: '/secured',
// };
