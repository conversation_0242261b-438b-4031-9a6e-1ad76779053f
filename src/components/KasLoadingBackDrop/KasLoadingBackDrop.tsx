import './styles.scss';

import React from 'react';
import {CircularProgress} from '@mui/material';
import {Clear} from '@mui/icons-material';
import IconButton from '@mui/material/IconButton';
import Box from '@mui/material/Box';

interface KasLoadingBackDropProps {
    onCancel?: () => void;
}

export const KasLoadingBackDrop = ({onCancel}: KasLoadingBackDropProps) => {
    return (
        <div className='kas-loading-backdrop'>
            <div className='kas-loading-backdrop__content'>
                <CircularProgress size={18} /> Loading...
                {onCancel && (
                    <Box my={-2} mr={-2} ml={2}>
                        <IconButton onClick={onCancel} title='Cancel'>
                            <Clear fontSize='small' />
                        </IconButton>
                    </Box>
                )}
            </div>
        </div>
    );
};
