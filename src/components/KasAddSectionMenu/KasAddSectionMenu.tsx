import React, {useState} from 'react';
import MenuItem from '@mui/material/MenuItem';
import {Button} from '@mui/material';
import {ArrowDropDown, ArrowDropUp} from '@mui/icons-material';
import Menu from '@mui/material/Menu';

export interface KasAddSectionMenuItem<T> {
    title: string;
    data: T;
}

interface KasAddSectionMenuProps<T> {
    items: KasAddSectionMenuItem<T>[];
    onAddItem: (value: T) => void;
}

export const KasAddSectionMenu = <T,>({items, onAddItem}: KasAddSectionMenuProps<T>) => {
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
    };
    const addItem = (value: T) => {
        onAddItem(value);
        setAnchorEl(null);
    };

    return (
        <>
            <Button
                variant='contained'
                disabled={!items.length}
                aria-controls={open ? 'item-menu' : undefined}
                endIcon={open ? <ArrowDropUp /> : <ArrowDropDown />}
                onClick={handleClick}>
                ADD SECTION
            </Button>
            <Menu id='item-menu' anchorEl={anchorEl} open={open} onClose={() => setAnchorEl(null)}>
                {items.map(({title, data}, index) => (
                    <MenuItem key={index} onClick={() => addItem(data)}>
                        {title}
                    </MenuItem>
                ))}
            </Menu>
        </>
    );
};
