import {KasLoadingBackDrop} from '@/components';
import React from 'react';
import {useVerbalACH} from '@/components/KasGlobalModals/components';
import {Typography} from '@mui/material';
import {ErrorView, PreviewAgreementView} from '@/views';
import {VerbalACHModal} from '@/components/KasGlobalModals/components/VerbalACH/interfaces';

export const PaymentAgreement = () => {
    const {agreementState, setOpenModal} = useVerbalACH();

    return (
        <>
            <Typography variant='subtitle1' mb={1}>
                Preview
            </Typography>
            <div style={{position: 'relative'}}>
                {agreementState.loading && <KasLoadingBackDrop />}
                {agreementState.error ? (
                    <ErrorView error={agreementState.error} />
                ) : (
                    <PreviewAgreementView
                        html={agreementState.data?.html}
                        onChangeBank={() => setOpenModal({type: VerbalACHModal.Change_Bank_Account})}
                    />
                )}
            </div>
        </>
    );
};
