import {<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>BackD<PERSON>, Kas<PERSON><PERSON>ding<PERSON>rror, KasS<PERSON>, KasSwitchWhen} from '@/components';
import React, {useEffect} from 'react';
import {useVerbalACH} from '@/components/KasGlobalModals/components';
import {BankForm, PaymentAgreement, VerbalACHForm, VerbalACHModals} from './components';
import {PaymentFrequencyValues, PaymentFrequencyView} from '@/views';
import {PaymentFrequency} from '@/interfaces';
import {Grid2} from '@mui/material';

export const VerbalACH = () => {
    const {banksState, loadBanksData, loan, loadAgreementData, verbalACHPayload} = useVerbalACH();

    const onApplyPaymentFrequency = async (values: PaymentFrequencyValues) => {
        const isSingle = values.frequency === PaymentFrequency.SINGLE;
        const installment = Number(values.installment);
        const payload = {
            frequency: values.frequency,
            ...(isSingle ? {request_date: values.date} : {start_date: values.date}),
            ...(isSingle ? {amount: installment} : {installment}),
            option: isSingle ? 'SINGLE' : 'MULTIPLE',
            payments: values.payments,
        };

        await loadAgreementData(payload);
    };

    useEffect(() => {
        loadBanksData().then();
    }, []);

    return (
        <>
            <KasSwitch>
                <KasSwitchWhen condition={banksState.loading && !banksState.data}>
                    <KasLoading />
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!banksState.error}>
                    <KasLoadingError view='contained' error={banksState.error} />
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!banksState.data}>
                    {banksState.loading && <KasLoadingBackDrop />}
                    <Grid2 container spacing={2}>
                        <Grid2 size={12}>
                            <BankForm />
                        </Grid2>
                        <Grid2 size={12}>
                            <PaymentFrequencyView
                                loanId={loan.gid}
                                payOffAmount={Number(loan.payoff_amount)}
                                installmentAmount={Number(loan.installment_amount)}
                                disabled={!verbalACHPayload.bank_id}
                                onApply={onApplyPaymentFrequency}
                            />
                        </Grid2>
                        <Grid2 size={12}>
                            <PaymentAgreement />
                        </Grid2>
                        <Grid2 size={12}>
                            <VerbalACHForm />
                        </Grid2>
                    </Grid2>
                </KasSwitchWhen>
            </KasSwitch>
            <VerbalACHModals />
        </>
    );
};
