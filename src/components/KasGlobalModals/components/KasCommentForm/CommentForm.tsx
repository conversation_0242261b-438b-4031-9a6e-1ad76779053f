import {useFormik} from 'formik';
import {KasCommentFormValues, validationSchema} from './schema';
import {TextField, Grid2} from '@mui/material';
import {KasModalFooter} from '@/components';
import {useState} from 'react';

interface KasCommentFormProps {
    onSubmit: (values: KasCommentFormValues) => void | Promise<void>;
    onCancel: () => void;
    submitting?: boolean;
}

export const KasCommentForm = ({onSubmit, onCancel}: KasCommentFormProps) => {
    const [submitting, setSubmitting] = useState(false);

    const onSubmitHandler = async (values: KasCommentFormValues) => {
        setSubmitting(true);
        await onSubmit(values);
        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            comment: '',
        },
        onSubmit: onSubmitHandler,
        validationSchema,
    });

    return (
        <form className='kas-comment-form' onSubmit={formik.handleSubmit}>
            <Grid2 container rowSpacing={2} spacing={2}>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        disabled={false}
                        onChange={formik.handleChange('comment')}
                        onBlur={formik.handleBlur('comment')}
                        label='Comment'
                        variant='outlined'
                        error={!!formik.errors.comment && formik.touched.comment}
                        helperText={formik.touched.comment && formik.errors.comment}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter disabled={!formik.isValid} loading={submitting} onCancel={onCancel} />
                </Grid2>
            </Grid2>
        </form>
    );
};
