import React, {useState} from 'react';
import {FormControl, FormControlLabel, Grid2, Radio, RadioGroup} from '@mui/material';
import {CommunicationACHType} from './interfaces';
import {EmailACH, EmailACHProvider} from './components';
import {useCommunicationACH} from './useCommunicationACH';
import {VerbalACH, VerbalACHProvider} from '@/components/KasGlobalModals/components';

export const CommunicationACH = () => {
    const {loan} = useCommunicationACH();
    const [selectedCommunication, setSelectedCommunication] = useState<CommunicationACHType>(
        CommunicationACHType.Email,
    );

    const handleCommunicationTypeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setSelectedCommunication(event.target.value as CommunicationACHType);
    };

    return (
        <Grid2 container spacing={1}>
            <Grid2 size={12}>
                <FormControl disabled={false}>
                    <RadioGroup
                        row
                        defaultValue={CommunicationACHType.Email}
                        onChange={handleCommunicationTypeChange}>
                        <FormControlLabel
                            value={CommunicationACHType.Email}
                            control={<Radio size='small' />}
                            label='Email'
                        />
                        <FormControlLabel
                            value={CommunicationACHType.Verbal}
                            control={<Radio size='small' />}
                            label='Verbal'
                        />
                    </RadioGroup>
                </FormControl>
            </Grid2>
            <Grid2 size={12}>
                <div hidden={selectedCommunication !== CommunicationACHType.Email}>
                    <EmailACHProvider>
                        <EmailACH />
                    </EmailACHProvider>
                </div>
                <div hidden={selectedCommunication !== CommunicationACHType.Verbal}>
                    <VerbalACHProvider loan={loan}>
                        <VerbalACH />
                    </VerbalACHProvider>
                </div>
            </Grid2>
        </Grid2>
    );
};
