import React, {useEffect, useState, useRef, useMemo} from 'react';
import {Ka<PERSON><PERSON><PERSON>dingBackD<PERSON>, Kas<PERSON>odal<PERSON>oot<PERSON>, KasSearchAutocompleteSelect, useGlobalModal} from '@/components';
import {Grid2} from '@mui/material';
import {useFormik} from 'formik';
import {SendCommunicationFormValues, validationSchema} from './schema';
import {useSnackbar} from '@/hooks/useSnackbar';
import {useEmailACH} from './useEmailACH';
import {PreviewEmail} from './components';
import {apiRequest} from '@/utils/AxiosUtils';
import {UnderwritingLoanEmailPreviewModel} from '@/screens/UnderwritingScreen/interfaces';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {SelectModel} from '@/interfaces';
import {useCommunicationACH} from '@/components/KasGlobalModals/components';

export const EmailACH = () => {
    const {hideGlobalModal} = useGlobalModal();
    const {loan} = useCommunicationACH();
    const {handlePrint, handleEmailPrintContent, selectedTypeOption, changeSelectedTypeOption} =
        useEmailACH();
    const {showMessage} = useSnackbar();
    const [submitting, setSubmitting] = useState(false);
    const [submitUrl, setSubmitUrl] = useState('');
    const [contentData, setContentData] = useState<string>('');

    const sendEmailOptions = useMemo(() => {
        const result: SelectModel<string>[] = [];

        if (loan.delinquent) {
            result.unshift({
                id: 'delinquency',
                label: 'Delinquency',
                value: 'delinquency',
            });
        }

        if (!loan.paid) {
            result.unshift(
                {
                    id: 'early-repayment',
                    label: 'Early Repayment',
                    value: 'early-repayment',
                },
                {
                    id: 'early-installment',
                    label: 'Early Installment',
                    value: 'early-installment',
                },
            );
        }

        return result;
    }, [loan]);

    const onSubmit = async (values: SendCommunicationFormValues) => {
        const url = `${submitUrl}preview=false`;

        setSubmitting(true);

        const response = await apiRequest(url, {method: 'post', body: values.payload});

        if (response.code === 200) {
            showMessage('Sent successfully', 'success');
            hideGlobalModal();
        } else {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        }

        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            payload: '',
            recipients: [],
        },
        onSubmit,
        validationSchema,
    });

    const changeEmailDataHandler = (value: UnderwritingLoanEmailPreviewModel | null, content: string) => {
        formik.setFieldValue('payload', value ? JSON.stringify(value) : '');
        formik.setFieldValue('recipients', value ? value.recipients : []);
        setContentData(content);
        updateSubmitUrl(value?.frequency);
    };

    const updateSubmitUrl = (frequency?: string) => {
        setSubmitUrl(
            `/api/secured/underwriting/loans/email-preview/${selectedTypeOption?.value}?${frequency ? `frequency=${frequency}&` : ''}`,
        );
    };

    useEffect(() => {
        handleEmailPrintContent(contentData, formik.values.recipients, '');
    }, [formik.values.recipients, contentData]);

    return (
        <>
            <div style={{position: 'relative'}}>
                {submitting && <KasLoadingBackDrop />}
                <Grid2 container spacing={2} rowSpacing={2}>
                    <Grid2 size={6}>
                        <KasSearchAutocompleteSelect
                            value={selectedTypeOption}
                            label='Send Email'
                            disabled={submitting}
                            autoFocus={true}
                            placeholder='Send Email'
                            options={sendEmailOptions}
                            onSelect={changeSelectedTypeOption}
                        />
                    </Grid2>
                    <Grid2 size={12}>
                        {selectedTypeOption && <PreviewEmail onChange={changeEmailDataHandler} />}
                    </Grid2>
                </Grid2>
            </div>
            <form onSubmit={formik.handleSubmit}>
                <KasModalFooter
                    submitText='Send Email'
                    disabled={submitting || !formik.isValid}
                    customDisable={submitting || !formik.isValid}
                    onCancel={hideGlobalModal}
                    onCustomClick={handlePrint}
                    customButtonText='Print'
                />
            </form>
        </>
    );
};
