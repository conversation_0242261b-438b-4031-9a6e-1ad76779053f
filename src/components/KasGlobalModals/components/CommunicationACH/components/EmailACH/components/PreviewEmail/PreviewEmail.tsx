import React, {useEffect, useMemo, useState} from 'react';
import {Kas<PERSON><PERSON><PERSON>, KasSwitch, KasSwitchWhen} from '@/components';
import {apiRequest} from '@/utils/AxiosUtils';
import {UnderwritingLoanEmailPreviewModel} from '@/screens/UnderwritingScreen/interfaces';
import {Typography, Grid2} from '@mui/material';
import {EmailAttachments} from './components';
import {
    EmailPreview,
    EmailRecipients,
    ErrorView,
    PaymentFrequencyValues,
    PaymentFrequencyView,
} from '@/views';
import {PaymentFrequency, SelectModel} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {Completable} from '@/interfaces';
import {AttachmentDTO} from '@/models/attachmentDTO';
import {useEmailACH} from '@/components/KasGlobalModals/components/CommunicationACH/components';
import {useCommunicationACH} from '@/components/KasGlobalModals/components';

interface PreviewEmailProps {
    onChange: (value: UnderwritingLoanEmailPreviewModel | null, emailContent: string) => void;
}

export const PreviewEmail = ({onChange}: PreviewEmailProps) => {
    const {selectedTypeOption} = useEmailACH();
    const {loan} = useCommunicationACH();
    const [originalPreviewOption, setOriginalPreviewOption] =
        useState<UnderwritingLoanEmailPreviewModel | null>(null);
    const [previewOption, setPreviewOption] = useState<UnderwritingLoanEmailPreviewModel | null>(null);
    const [attachmentPreviewOption, setAttachmentPreviewOption] = useState<AttachmentDTO | null>(null);
    const [attachments, setAttachments] = useState<SelectModel<AttachmentDTO>[] | null | undefined>(null);
    const [emailState, setEmailState] = useState(getDefaultState<UnderwritingLoanEmailPreviewModel[]>());
    const [attachmentPreview, setAttachmentPreview] = useState<boolean>(false);
    const [contentData, setContentData] = useState<string>('');

    const options = useMemo(() => {
        if (emailState.data) {
            return emailState.data.map((item) => ({
                id: item.email_type,
                value: item,
                label: `${item.email_type}${item.sent_date ? ` (Sent: ${item.sent_date})` : ''}`,
            }));
        }
        return [];
    }, [emailState.data]);

    useEffect(() => {
        if (emailState.data) {
            const achTemplate = emailState.data.find(
                (item) => item.email_type === 'alternative_payment_ach_request',
            );

            changeOriginalPreviewOption(
                achTemplate
                    ? {
                          id: '',
                          value: achTemplate,
                          label: '',
                      }
                    : null,
            );
        }
    }, [emailState.data]);

    const url = useMemo(() => {
        return `/api/secured/underwriting/loans/${loan.gid}/email-preview/${selectedTypeOption?.value}`;
    }, [selectedTypeOption, loan.gid]);

    const emailPreviewUrl = useMemo(() => {
        if (previewOption?.frequency) {
            return `/api/secured/underwriting/loans/email-preview/${selectedTypeOption?.value}?frequency=${previewOption.frequency}&preview=true`;
        }
        return `/api/secured/underwriting/loans/email-preview/${selectedTypeOption?.value}?preview=true`;
    }, [selectedTypeOption, previewOption]);

    const attachmentPreviewUrl = useMemo(() => {
        return `/api/secured/underwriting/loans/email-preview/${selectedTypeOption?.value}/attachment`;
    }, [selectedTypeOption]);

    const changeOriginalPreviewOption = (value: SelectModel<UnderwritingLoanEmailPreviewModel> | null) => {
        setOriginalPreviewOption(value?.value || null);
        setPreviewOption(value?.value || null);
        setAttachments(
            value?.value?.attachments?.map((item) => ({
                id: item?.attachment_type,
                label: item?.attachment_type,
                value: item,
            })),
        );
    };

    const onApplyPaymentFrequency = async (values: PaymentFrequencyValues) => {
        const isSingle = values.frequency === PaymentFrequency.SINGLE;
        const installment = values.installment;
        const payload: Partial<UnderwritingLoanEmailPreviewModel> = {
            frequency: values.frequency,
            ...(isSingle ? {request_date: values.date} : {start_date: values.date}),
            ...(isSingle ? {amount: installment} : {installment}),
            option: isSingle ? 'SINGLE' : 'MULTIPLE',
            payments: values.payments,
            strict: values.strict,
        };
        setPreviewOption({...previewOption, ...payload} as UnderwritingLoanEmailPreviewModel);
    };

    const loadData = async () => {
        setEmailState(getLoadingState(emailState));
        changeOriginalPreviewOption(null);

        const response: Completable<UnderwritingLoanEmailPreviewModel[]> = await apiRequest(url);

        if (!response.value) {
            changeOriginalPreviewOption(null);
        }

        setEmailState(getLoadedState(response));
    };

    useEffect(() => {
        loadData().then();
    }, [url]);

    useEffect(() => {
        onChange(previewOption, contentData);
    }, [previewOption, contentData]);

    return (
        <Grid2 container spacing={2} rowSpacing={2}>
            <KasSwitch>
                <KasSwitchWhen condition={emailState.loading}>
                    <Grid2 size={12}>
                        <KasLoading />
                    </Grid2>
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!emailState.error}>
                    <Grid2 size={12}>
                        <ErrorView error={emailState.error} />
                    </Grid2>
                </KasSwitchWhen>
                <KasSwitchWhen condition={!options.length}>
                    <Grid2 size={12}>
                        <Typography variant='body1'>No Available Options</Typography>
                    </Grid2>
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!options.length}>
                    {previewOption && originalPreviewOption && (
                        <>
                            <Grid2 size={12}>
                                <EmailRecipients
                                    recipients={originalPreviewOption.recipients}
                                    onChange={(recipients) => {
                                        setPreviewOption({...previewOption, recipients});
                                    }}
                                />
                            </Grid2>
                            {attachments && (
                                <Grid2 size={12}>
                                    <EmailAttachments
                                        attachments={originalPreviewOption.attachments}
                                        onChange={(attachments) => {
                                            if (attachments.length !== 0) {
                                                setAttachmentPreview(true);
                                                setAttachmentPreviewOption(attachments[0]);
                                            } else {
                                                setAttachmentPreview(false);
                                            }
                                            setPreviewOption({...previewOption, attachments});
                                        }}
                                    />
                                </Grid2>
                            )}
                            <Grid2 size={12}>
                                <PaymentFrequencyView
                                    loanId={loan.gid}
                                    payOffAmount={Number(loan.payoff_amount)}
                                    installmentAmount={Number(loan.installment_amount)}
                                    disabled={emailState.loading}
                                    widthStrictOption
                                    onApply={onApplyPaymentFrequency}
                                />
                            </Grid2>
                            <Grid2 size={12}>
                                <Typography variant='subtitle1'>Preview Email</Typography>
                                <EmailPreview
                                    url={emailPreviewUrl}
                                    payload={JSON.stringify(previewOption)}
                                    onContent={(content) => setContentData(content)}
                                />
                            </Grid2>
                            {attachmentPreview && (
                                <Grid2 size={12}>
                                    <Typography variant='subtitle1'>Preview Attachment</Typography>
                                    <EmailPreview
                                        url={attachmentPreviewUrl}
                                        payload={JSON.stringify(attachmentPreviewOption)}
                                        isLetter={true}
                                    />
                                </Grid2>
                            )}
                        </>
                    )}
                </KasSwitchWhen>
            </KasSwitch>
        </Grid2>
    );
};
