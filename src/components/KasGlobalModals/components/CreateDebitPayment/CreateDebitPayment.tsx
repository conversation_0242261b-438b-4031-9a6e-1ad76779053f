import React, {useState} from 'react';
import {DebitPNCForm, EmailPreviewForm, InitPNCForm} from './components';
import {Stepper, Step, StepLabel, Box} from '@mui/material';
import {CollectionsDebitCardPNCModel} from '@/interfaces';
import {GlobalModalDebitPaymentProps, useGlobalModal} from '@/components';

export const CreateDebitPayment = ({loanId, payoffAmount}: GlobalModalDebitPaymentProps) => {
    const [activeStep, setActiveStep] = useState(0);
    const {hideGlobalModal} = useGlobalModal();
    const [pncData, setPncData] = useState<CollectionsDebitCardPNCModel | undefined>();

    const handleNext = () => setActiveStep((prev) => prev + 1);
    const handlePrev = () => setActiveStep((prev) => prev - 1);

    return (
        <Box>
            <Stepper activeStep={activeStep} alternativeLabel>
                <Step>
                    <StepLabel>Send Email</StepLabel>
                </Step>
                <Step>
                    <StepLabel>PayerExpress Debit Card Payments</StepLabel>
                </Step>
            </Stepper>
            <Box mt={2}>
                <div hidden={activeStep !== 0}>
                    <EmailPreviewForm loanId={loanId} onClose={hideGlobalModal} onSkip={handleNext} />
                </div>
                <div hidden={activeStep !== 1}>
                    <InitPNCForm
                        payoffAmount={payoffAmount}
                        loanId={loanId}
                        onClose={hideGlobalModal}
                        onPrev={handlePrev}
                        onSuccess={setPncData}
                    />
                </div>
                {pncData && <DebitPNCForm data={pncData} onClose={hideGlobalModal} />}
            </Box>
        </Box>
    );
};
