import * as Yup from 'yup';
import {DEFAULT_VALIDATION_MSG} from '@/constants';

export const getValidationSchema = (maxPncAmount: number) => Yup.object().shape({
    amount: Yup.number()
        .typeError('Must be a valid number')
        .min(0, 'Minimum value is 0')
        .max(maxPncAmount, `Maximum value is ${maxPncAmount} given the current delinquency and payment mode`)
        .required(DEFAULT_VALIDATION_MSG),
    settlementEnabled: Yup.boolean().required(),
});

export interface InitPNCFormValues {
    amount: string; // defined as string to allow blank start
    settlementEnabled: boolean;
}
