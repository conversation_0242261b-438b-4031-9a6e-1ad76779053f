import React, {createContext, useContext, useState} from 'react';
import {KasGlobalModals} from '@/components';
import {GlobalModalProps} from './interfaces';

interface GlobalModalContextProps {
    showGlobalModal: (modal: GlobalModalProps) => void;
    hideGlobalModal: () => void;
    modal: GlobalModalProps | null;
}

const GlobalModalContext = createContext<GlobalModalContextProps | undefined>(undefined);

export const GlobalModalProvider = ({children}: {children: React.ReactNode}) => {
    const [modal, setModal] = useState<GlobalModalProps | null>(null);

    const showGlobalModal = (modal: GlobalModalProps) => {
        setModal(modal);
    };

    const hideGlobalModal = () => {
        setModal(null);
    };

    const value: GlobalModalContextProps = {
        showGlobalModal,
        hideGlobalModal,
        modal,
    };

    return (
        <GlobalModalContext.Provider value={value}>
            {children}
            <KasGlobalModals />
        </GlobalModalContext.Provider>
    );
};

export const useGlobalModal = (): GlobalModalContextProps => {
    const context = useContext(GlobalModalContext);
    if (!context) {
        throw new Error('useGlobalModal must be used within a GlobalModalProvider');
    }
    return context;
};
