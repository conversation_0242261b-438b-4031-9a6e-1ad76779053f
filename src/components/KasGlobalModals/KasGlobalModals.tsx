import './styles.scss';

import React, {useMemo} from 'react';
import {GlobalModal, KasModal, useGlobalModal} from '@/components';
import {
    AddEmpolyeeAddressForm,
    EmployeeEmailForm,
    EmployeePhoneForm,
    RemoveUser,
    UnlinkUser,
    UnlockUser,
    MergeEmployeesForm,
    PurgeEmployeesForm,
    ConfirmForm,
    KasCommentForm,
    CreateDebitPayment,
    VerbalACH,
    VerbalACHProvider,
    CommunicationACHProvider,
    CommunicationACH,
    RefundEmailForm,
} from './components';
import {capitalizeWords} from '@/utils/TextUtils';
import {AMLReportTableView} from '@/views';

export const KasGlobalModals = () => {
    const {modal, hideGlobalModal} = useGlobalModal();

    const size = useMemo(() => {
        switch (modal?.type) {
            case GlobalModal.AML_Report:
                return 'large';
            default:
                return 'medium';
        }
    }, [modal?.type]);

    const title = useMemo(() => {
        switch (modal?.type) {
            case GlobalModal.Unlink_User:
                return 'Unlink User';
            case GlobalModal.Remove_User:
                return 'Remove User';
            case GlobalModal.Unlock_User:
                return 'Unlock User';
            case GlobalModal.Employee_Address:
                return `${modal.props.method === 'post' ? 'Add' : 'Edit'} Address`;
            case GlobalModal.Employee_Phone:
                return `${modal.props.method === 'post' ? 'Add' : 'Edit'} Phone`;
            case GlobalModal.Employee_Email:
                return `${modal.props.method === 'post' ? 'Add' : 'Edit'} Email`;
            case GlobalModal.Merge_Employees:
                return 'Merge Employees';
            case GlobalModal.Purge_Employees:
                return 'Purge Employees';
            case GlobalModal.Confirm:
                return modal.props.title;
            case GlobalModal.Comment:
                return modal.props.title;
            case GlobalModal.Debit_Payment:
                return 'PayerExpress Debit Card Payments';
            case GlobalModal.Verbal_ACH:
                return 'Verbal ACH';
            case GlobalModal.Communication_ACH:
                return 'ACH Communication';
            case GlobalModal.AML_Report:
                return `AML Report for ${capitalizeWords(modal.props.type)} #${modal.props.id}`;
            case GlobalModal.Refund_Email:
                return 'Refund Email Preview';
            default:
                return 'Unknown Modal';
        }
    }, [modal?.type]);

    const renderModalContent = useMemo(() => {
        switch (modal?.type) {
            case GlobalModal.Unlink_User:
                return <UnlinkUser {...modal.props} />;
            case GlobalModal.Remove_User:
                return <RemoveUser {...modal.props} />;
            case GlobalModal.Unlock_User:
                return <UnlockUser {...modal.props} />;
            case GlobalModal.Employee_Address:
                return <AddEmpolyeeAddressForm {...modal.props} />;
            case GlobalModal.Employee_Phone:
                return <EmployeePhoneForm {...modal.props} />;
            case GlobalModal.Employee_Email:
                return <EmployeeEmailForm {...modal.props} />;
            case GlobalModal.Merge_Employees:
                return <MergeEmployeesForm {...modal.props} />;
            case GlobalModal.Purge_Employees:
                return <PurgeEmployeesForm {...modal.props} />;
            case GlobalModal.Confirm:
                return <ConfirmForm {...modal.props} />;
            case GlobalModal.Comment:
                return <KasCommentForm onCancel={hideGlobalModal} {...modal.props} />;
            case GlobalModal.Debit_Payment:
                return <CreateDebitPayment {...modal.props} />;
            case GlobalModal.Verbal_ACH:
                return (
                    <VerbalACHProvider {...modal.props}>
                        <VerbalACH />
                    </VerbalACHProvider>
                );
            case GlobalModal.Communication_ACH:
                return (
                    <CommunicationACHProvider {...modal.props}>
                        <CommunicationACH />
                    </CommunicationACHProvider>
                );
            case GlobalModal.AML_Report:
                return <AMLReportTableView {...modal.props} />;
            case GlobalModal.Refund_Email:
                return <RefundEmailForm {...modal.props} />;
            default:
                return null;
        }
    }, [modal?.type]);

    return (
        <KasModal title={title} size={size} open={!!modal} onClose={() => hideGlobalModal()}>
            <div className='kas-global-modal'>{renderModalContent}</div>
        </KasModal>
    );
};
