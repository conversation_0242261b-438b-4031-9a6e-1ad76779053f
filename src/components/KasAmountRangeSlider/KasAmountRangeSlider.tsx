import './styles.scss';

import React from 'react';
import {FormHelperText, Grid2, <PERSON>lide<PERSON>, TextField, Typography} from '@mui/material';
import {FormikProps, FormikTouched} from 'formik';
import {AmountRange} from '@/interfaces';

interface KasAmountRangeSliderProps {
    formik: FormikProps<any>;
    name: string;
    min: number;
    max: number;
    title?: string;
    disabled?: boolean;
}

export const KasAmountRangeSlider = ({
    formik,
    name,
    min,
    max,
    disabled = false,
    title,
}: KasAmountRangeSliderProps) => {
    const value: AmountRange = formik.values[name] || {start: min, end: max};

    const touched = formik.touched[name] as FormikTouched<AmountRange> | undefined;
    const errors = formik.errors[name] as AmountRange | undefined;
    const groupTouched = touched?.start || touched?.end;
    const groupError = (touched?.start && errors?.start) || (touched?.end && errors?.end);

    const handleSliderChange = async (_: Event, newValue: number | number[]) => {
        if (Array.isArray(newValue)) {
            await formik.setFieldValue(name, {start: newValue[0], end: newValue[1]});
        }
    };

    return (
        <div className='kas-amount-range'>
            <Grid2 container>
                {title && (
                    <Grid2 size={12}>
                        <Typography variant='subtitle1'>{title}</Typography>
                    </Grid2>
                )}
                <Grid2 size={12} px={1} pb={2}>
                    <Slider
                        value={[value.start, value.end]}
                        onChange={handleSliderChange}
                        valueLabelDisplay='off'
                        min={min}
                        max={max}
                        disabled={disabled}
                    />
                </Grid2>
                <Grid2 size={12} container spacing={4} className='kas-amount-range__fields'>
                    <Grid2 size={6}>
                        <TextField
                            fullWidth
                            size='small'
                            name={`${name}.start`}
                            value={value.start}
                            disabled={disabled}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            label='From'
                            variant='outlined'
                            type='number'
                            slotProps={{
                                htmlInput: {
                                    min,
                                    max,
                                },
                            }}
                            error={!!errors?.start && !!touched?.start}
                        />
                    </Grid2>
                    <Grid2 size={6}>
                        <TextField
                            fullWidth
                            size='small'
                            name={`${name}.end`}
                            value={value.end}
                            disabled={disabled}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            label='To'
                            variant='outlined'
                            type='number'
                            slotProps={{
                                htmlInput: {
                                    min,
                                    max,
                                },
                            }}
                            error={!!errors?.end && !!touched?.end}
                        />
                    </Grid2>
                </Grid2>
                {groupTouched && groupError && (
                    <Grid2 size={12}>
                        <FormHelperText error={true}>{groupError}</FormHelperText>
                    </Grid2>
                )}
            </Grid2>
        </div>
    );
};
