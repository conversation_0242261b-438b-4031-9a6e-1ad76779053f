import React, {useEffect} from 'react';
import {LocalizationProvider} from '@mui/x-date-pickers/LocalizationProvider';
import {AdapterDayjs} from '@mui/x-date-pickers/AdapterDayjs';
import {DateRangePicker, SingleInputDateRangeField} from '@mui/x-date-pickers-pro';
import {Dayjs} from 'dayjs';
import {PickersShortcutsItem} from '@mui/x-date-pickers/PickersShortcuts/PickersShortcuts';
import {Grid2} from '@mui/material';
import {KasDatePicker} from '@/components';
import {LicenseInfo} from '@mui/x-license';
import {DayJsRangeModel} from '@/interfaces';

interface KasDateRangePickerProps {
    value: DayJsRangeModel;
    disabled?: boolean;
    slotPropsItems?: PickersShortcutsItem<[Dayjs | null, Dayjs | null]>[];
    onChange: (value: DayJsRangeModel) => void;
}

export const KasDateRangePicker = ({
    value,
    disabled = false,
    slotPropsItems = [],
    onChange,
}: KasDateRangePickerProps) => {
    const isLicense = LicenseInfo.getLicenseKey();

    useEffect(() => {
        if (value.start && !value.end && value.start.date() === 1) {
            const endOfMonth = value.start.endOf('month');

            onChange({
                start: value.start,
                end: endOfMonth,
            });
        }
    }, [value.start]);

    return isLicense ? (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DateRangePicker
                disabled={disabled}
                value={[value.start, value.end]}
                onChange={(newValue) =>
                    onChange({
                        start: newValue[0],
                        end: newValue[1],
                    })
                }
                slots={{field: SingleInputDateRangeField}}
                slotProps={{
                    shortcuts: {
                        items: slotPropsItems,
                    },
                }}
                calendars={1}
            />
        </LocalizationProvider>
    ) : (
        <Grid2 container spacing={2}>
            <Grid2 size={6}>
                <KasDatePicker
                    label='Start Date'
                    value={value.start}
                    disabled={disabled}
                    onChange={(data) =>
                        onChange({
                            start: data,
                            end: value.end,
                        })
                    }
                />
            </Grid2>
            <Grid2 size={6}>
                <KasDatePicker
                    label='End Date'
                    value={value.end}
                    disabled={disabled}
                    onChange={(data) =>
                        onChange({
                            start: value.start,
                            end: data,
                        })
                    }
                />
            </Grid2>
        </Grid2>
    );
};
