import React, {useState} from 'react';
import DownloadIcon from '@mui/icons-material/Download';
import {CircularProgress} from '@mui/material';
import IconButton from '@mui/material/IconButton';
import {useDownloadBlob} from '@/hooks/useDownloadBlob';

interface KasDownloadIconButtonProps {
    params: string;
    url?: string;
    disabled?: boolean;
}

export const KasDownloadIconButton = ({params, url, disabled}: KasDownloadIconButtonProps) => {
    const [loadingFile, setLoadingFile] = useState(false);
    const {getBlob} = useDownloadBlob();

    const onClickHandler = async () => {
        setLoadingFile(true);
        await getBlob(params, url);
        setLoadingFile(false);
    };

    return (
        <IconButton disabled={disabled || loadingFile} title='Export' onClick={onClickHandler}>
            {loadingFile ? (
                <CircularProgress size={20} />
            ) : (
                <DownloadIcon color={disabled ? 'disabled' : 'primary'} fontSize='small' />
            )}
        </IconButton>
    );
};
