import React, {useCallback, useEffect, useRef, useState} from 'react';
import {Autocomplete, TextField} from '@mui/material';
import {SelectModel} from '@/interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {debounce} from '@mui/material';

interface KasAsyncSearchAutocompleteFieldProps<T> {
    value: SelectModel<T> | undefined | null;
    label: string;
    searchUrl: string;
    onSelect: (value: SelectModel<T> | null) => void;
    disabled?: boolean;
}

export const KasAsyncSearchAutocompleteField = <T = string,>({
    value,
    label,
    searchUrl,
    onSelect,
    disabled = false,
}: KasAsyncSearchAutocompleteFieldProps<T>) => {
    const [loading, setLoading] = useState(false);
    const [searchText, setSearchText] = useState('');
    const [options, setOptions] = useState<SelectModel<T>[]>([]);
    const abortControllerRef = useRef<AbortController | null>(null);

    const getOptions = useCallback(
        debounce(async (st: string) => {
            setLoading(true);
            try {
                if (abortControllerRef.current) {
                    abortControllerRef.current.abort();
                }

                abortControllerRef.current = new AbortController();

                const response = await apiRequest(`${searchUrl}${st}`, {
                    signal: abortControllerRef.current.signal,
                });

                setOptions(response.value || []);
            } catch (error) {
                if (!(error instanceof DOMException && error.name === 'AbortError')) {
                    throw error;
                }
            }

            setLoading(false);
        }, 300),
        [searchUrl],
    );

    useEffect(() => {
        if (searchText?.length > 2) {
            getOptions(searchText);
        } else {
            setOptions([]);
        }
    }, [searchText]);

    useEffect(() => {
        setOptions([]);
    }, [searchUrl]);

    return (
        <Autocomplete
            disabled={disabled}
            loading={loading}
            options={options}
            getOptionLabel={(option) => option?.label}
            filterOptions={(x) => x}
            size={'small'}
            value={value}
            onChange={(_event, value) => {
                onSelect(value);
            }}
            renderInput={(params) => (
                <TextField
                    {...params}
                    label={label}
                    variant='outlined'
                    onChange={(e) => {
                        setSearchText(e.target.value);
                    }}
                />
            )}
        />
    );
};
