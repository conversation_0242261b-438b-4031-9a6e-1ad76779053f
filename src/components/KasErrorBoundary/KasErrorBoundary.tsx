import React from 'react';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {apiRequest} from '@/utils/AxiosUtils';
import {KasLoadingError} from '@/components';

interface KasErrorBoundaryProps {
    children: React.ReactNode;
}

interface KasErrorBoundaryState {
    hasError: boolean;
    errorMessage: string | null;
}

export class KasErrorBoundary extends React.Component<KasErrorBoundaryProps, KasErrorBoundaryState> {
    constructor(props: KasErrorBoundaryProps) {
        super(props);
        this.state = {hasError: false, errorMessage: null};
    }

    static getDerivedStateFromError(error: Error): KasErrorBoundaryState {
        return {hasError: true, errorMessage: error.message};
    }

    componentDidCatch(error: Error) {
        const body = JSON.stringify({
            message: error.message,
            stack_trace: error.stack,
        });

        apiRequest('/api/error', {
            method: 'post',
            body,
        }).then();
    }

    handleRetry = () => {
        this.setState({hasError: false, errorMessage: null});
    };

    render() {
        if (this.state.hasError) {
            return (
                <KasLoadingError
                    view='contained'
                    error={this.state.errorMessage || DEFAULT_ERROR_MSG}
                    onTryAgain={this.handleRetry}
                />
            );
        }

        return <>{this.props.children}</>;
    }
}
