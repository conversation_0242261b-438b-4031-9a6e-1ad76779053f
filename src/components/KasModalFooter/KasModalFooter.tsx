import './styles.scss';

import React from 'react';
import {Button, CircularProgress, Grid2} from '@mui/material';
import {OverridableStringUnion} from '@mui/types';
import {ButtonPropsColorOverrides} from '@mui/material/Button/Button';
import {KasCancellableButton} from '@/components';

interface KasModalFooterProps {
    disabled?: boolean;
    loading?: boolean;
    submitText?: string;
    customButtonText?: string;
    submitColor?: OverridableStringUnion<
        'inherit' | 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning',
        ButtonPropsColorOverrides
    >;
    onCancel: () => void;
    onCustomClick?: () => void;
    customDisable?: boolean;
    disableCancel?: boolean;
}

interface DefaultModalFooterProps extends KasModalFooterProps {
    type?: 'default';
}

interface CancellableModalFooterProps extends KasModalFooterProps {
    type: 'cancellable';
    delayText: string;
    onSubmit: () => void;
}

type KasModalFooterType = DefaultModalFooterProps | CancellableModalFooterProps;

export const KasModalFooter = (props: KasModalFooterType) => {
    const {
        disabled = false,
        customDisable = false,
        loading = false,
        submitText = 'Save',
        submitColor = 'primary',
        onCancel,
        onCustomClick,
        customButtonText,
        disableCancel = false,
    } = props;

    return (
        <div className='kas-modal-footer'>
            <Grid2 container justifyContent='flex-end' spacing={2}>
                <Grid2 size={3}>
                    <Button
                        variant='outlined'
                        fullWidth
                        size='small'
                        disabled={disableCancel}
                        onClick={onCancel}>
                        Cancel
                    </Button>
                </Grid2>
                {onCustomClick && (
                    <Grid2 size={3}>
                        <Button
                            variant='outlined'
                            fullWidth
                            size='small'
                            disabled={disabled && customDisable}
                            onClick={onCustomClick}>
                            {customButtonText}
                        </Button>
                    </Grid2>
                )}
                <Grid2 size={3}>
                    {props?.type === 'cancellable' ? (
                        <KasCancellableButton
                            variant='contained'
                            fullWidth
                            type='submit'
                            disabled={disabled || loading}
                            color={submitColor}
                            delayText={`${props.delayText}...`}
                            onClick={props.onSubmit}>
                            {submitText}
                        </KasCancellableButton>
                    ) : (
                        <Button
                            variant='contained'
                            fullWidth
                            size='small'
                            type='submit'
                            disabled={disabled || loading}
                            color={submitColor}>
                            {loading ? <CircularProgress size={16} /> : submitText}
                        </Button>
                    )}
                </Grid2>
            </Grid2>
        </div>
    );
};
