.kas-loading-error {
    display: flex;
    flex-direction: row;
    justify-content: center;

    &.contained {
        position: relative;
        padding: 14px;
        border-radius: 4px;
        border: 1px solid var(--color-error);

        &:before {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 4px;
            opacity: .1;
            background: var(--color-error);
            pointer-events: none;
            z-index: 0;
            content: '';
        }
    }

    &__content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: relative;
        z-index: 1;
        padding-left: 16px;
        color: var(--color-error);
        text-align: center;
        font-size: 14px;
        font-weight: 600;
        line-height: 18px;
    }

    .kas-link {
        margin-top: 4px;
    }
}
