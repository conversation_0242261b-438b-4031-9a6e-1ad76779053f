import './styles.scss';

import React, {PropsWithChildren, ReactNode} from 'react';
import {Modal, Paper, Stack, Typography} from '@mui/material';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import {TestableProps} from '@/screens/UnderwritingScreen/interfaces/testable';

interface KasModalProps extends PropsWithChildren, TestableProps {
    title: string | null;
    titleAction?: ReactNode;
    open: boolean;
    disableClose?: boolean;
    className?: string;
    size?: 'small' | 'medium' | 'large';
    onClose: () => void;
}

export const KasModal = ({
    title,
    titleAction,
    className,
    open,
    disableClose = false,
    size = 'medium',
    onClose,
    testid,
    children,
}: KasModalProps) => {
    return (
        <Modal
            open={open}
            onClose={(event, reason) => {
                if (!reason) {
                    onClose();
                }
            }}
            data-testid={`${testid}-modal`}
            aria-labelledby='modal-modal-title'
            aria-describedby='modal-modal-description'>
            <Paper className={`kas-modal ${className ? className : ''} ${size}`} elevation={0}>
                <IconButton
                    className='kas-modal__close'
                    size='small'
                    disabled={disableClose}
                    onClick={onClose}>
                    <CloseIcon />
                </IconButton>
                {title ? (
                    <div className='kas-modal__wrap'>
                        <Stack direction='row' alignItems='center' className='kas-modal__head' spacing={1}>
                            <Typography variant='h3'>{title}</Typography>
                            {titleAction}
                        </Stack>
                        <div className='kas-modal__content'>{children}</div>
                    </div>
                ) : (
                    children
                )}
            </Paper>
        </Modal>
    );
};
