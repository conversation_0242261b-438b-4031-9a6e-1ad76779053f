import React, {ReactElement} from 'react';
import {Button, Stack} from '@mui/material';
import ViewHeadlineIcon from '@mui/icons-material/ViewHeadline';
import VerticalSplitIcon from '@mui/icons-material/VerticalSplit';

export type LayoutModeType = 'list' | 'grid';

interface LayoutModeModel {
    key: LayoutModeType;
    icon: ReactElement;
}

interface KasLayoutModeControlProps {
    value: LayoutModeType;
    onChange: (mode: LayoutModeType) => void;
}

export const KasLayoutModeControl = ({value, onChange}: KasLayoutModeControlProps) => {
    const modes: LayoutModeModel[] = [
        {key: 'list', icon: <ViewHeadlineIcon fontSize='small' />},
        {key: 'grid', icon: <VerticalSplitIcon fontSize='small' />},
    ];

    return (
        <Stack direction='row' spacing={1}>
            {modes.map(({key, icon}) => (
                <Button
                    key={key}
                    variant={value === key ? 'contained' : 'text'}
                    onClick={() => onChange(key)}
                    aria-label={`Switch to ${key} view`}>
                    {icon}
                </Button>
            ))}
        </Stack>
    );
};
