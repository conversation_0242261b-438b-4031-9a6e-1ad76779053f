.kas-screen-head-box {
  position: relative;
  background-color: var(--background-paper);
  border-bottom: 1px solid var(--color-divider);

  &:before,
  &:after {
    position: absolute;
    top: 0;
    width: 20px;
    height: 100%;
    background-color: var(--background-paper);
    border-bottom: 1px solid var(--color-divider);
    transition: border-bottom 0.3s ease;
    content: '';
  }

  &:before {
    right: 100%;
  }

  &:after {
    left: 100%;
  }
}
