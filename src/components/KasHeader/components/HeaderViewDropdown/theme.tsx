import {createTheme} from '@mui/material';

export const theme = createTheme({
    components: {
        MuiSelect: {
            styleOverrides: {
                root: {
                    fontSize: 'var(--small-text-size)',
                    color: '#ffffff',
                },
                iconOutlined: {
                    color: '#ffffff',
                },
            },
        },
        MuiOutlinedInput: {
            styleOverrides: {
                root: {
                    '& fieldset': {
                        borderColor: 'rgba(255, 255, 255, .1)',
                    },
                },
            },
        },
        MuiMenu: {
            styleOverrides: {
                paper: {
                    background: '#28262c',
                },
            },
        },
        MuiMenuItem: {
            styleOverrides: {
                root: {
                    fontSize: 'var(--small-text-size)',
                    color: '#ffffff',
                    '&.Mui-selected': {
                        background: 'none',
                        color: 'var(--color-primary)',
                    },
                    '&.Mui-selected:hover': {
                        background: 'none',
                    },
                    '&:hover': {
                        backgroundColor: 'rgba(29, 158, 213, 0.1)',
                    },
                },
            },
        },
    },
});
