import React, {useState} from 'react';
import {LocalizationProvider} from '@mui/x-date-pickers/LocalizationProvider';
import {AdapterDayjs} from '@mui/x-date-pickers/AdapterDayjs';
import {FormikValues} from 'formik';
import {FormControl} from '@mui/material';
import {DateTimePicker} from '@mui/x-date-pickers';

interface KasTimePickerFormFieldProps {
    name: string;
    formik: FormikValues;
    disabled?: boolean;
    label?: string;
    showValidation?: boolean;
}

export const KasDateTimePickerFormField = ({
    name,
    formik,
    disabled = false,
    label = 'Date',
    showValidation = true,
}: KasTimePickerFormFieldProps) => {
    const [open, setOpen] = useState(false);
    const [openEver, setOpenEver] = useState(false);

    return (
        <FormControl fullWidth variant='outlined'>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
                <DateTimePicker
                    open={open}
                    name={name}
                    label={label}
                    disabled={disabled}
                    onClose={() => {
                        setOpen(false);
                        setOpenEver(true);
                    }}
                    onChange={(value) => {
                        formik.setFieldValue(name, value);
                    }}
                    slotProps={{
                        textField: {
                            InputProps: {endAdornment: <></>},
                            variant: 'outlined',
                            size: 'small',
                            name,
                            label,
                            disabled,
                            error: showValidation && openEver && !!formik.errors[name],
                            helperText: showValidation && openEver && formik.errors[name],
                            onClick: () => setOpen(true),
                        },
                    }}
                />
            </LocalizationProvider>
        </FormControl>
    );
};
