.kas-preview-agreement {
  .document .preview-only {
    display: none;
  }

  .document .document-hide {
    display: none;
  }

  .preview .document-only {
    display: none;
  }

  .preview .preview-hide {
    display: none;
  }

  .ws-break-spaces {
    white-space: break-spaces;
  }

  .ws-nowrap {
    white-space: nowrap;
  }

  @page {
    size: legal;
    margin: 0.5in 0.5in 1.25in;
    @bottom-left {
      content: element(footer);
    }
  }

  .no-bottom {
    border-bottom-style: none;
  }

  .no-border {
    border: none;
  }

  .agreement-wrapper {

    table {
      margin-left: auto;
      margin-right: auto;
      border: 0px;
      border-collapse: collapse;
      width: 500px;
    }

    td {
      vertical-align: top;
      padding: 1em;
    }

    h1 {
      text-align: center;
      text-transform: uppercase;
      font-size: 130%;
      font-weight: bold;
    }

    h2 {
      text-align: center;
      font-size: 115%;
      font-weight: bold;
    }

    p {
      font-size: 100%;
      margin: 1em;
    }

    p span.nr {
      white-space: nowrap;
    }

    li {
      margin: 1em;
    }

    ol {
      margin: 1em;
    }

    form {
      margin: 0in 0.25in;
    }

    .field-wrapper {
      padding: 0px 20px;
      text-align: justify;
      text-justify: distribute-all-lines;

      &:after {
        content: '';
        display: inline-block;
        width: 100%;
      }
    }

    .field {
      display: inline-block;
      font-size: 1rem;
      text-align: center;
    }

    .field__data {
      padding: 5px 30px;
      border-bottom: 1px solid #9c9c9c;
      margin: 5px 0;
      display: inline-block;
    }

    .field__data--narrow {
      padding: 5px 10px;
      font-size: .9em;
    }

    .field__data--wide {
      padding: 5px 50px;
      font-size: .9em;
    }

    .field__label {
      font-size: .9em;
    }

    .new-request-check {
      margin: 20px 10px 40px;
      line-height: 1;
    }

    .schedule-table {
      margin-top: 32px;

      .schedule-row {
        display: inline-block;
        width: 100%;
        background-color: #fafafa;
        font-size: 14px;
        line-height: 1.20;
        overflow: hidden;
        color: #393939;
        position: relative;
        z-index: 1;
        @media screen and (max-width: 360px) {
          padding-right: 74px;
        }

        &:nth-child(2n+0) {
          background-color: #ffffff;
        }

        .schedule-col-heading {
          padding: 12px 0;
          float: left;
          width: 25%;
          @media screen and (max-width: 1000px) {
            float: none;
            width: auto;
            color: #a8a8a8;
          }

          span {
            padding: 0 10px 0 50px;
          }
        }

        .schedule-col-items {
          margin-left: 25%;
          @media screen and (max-width: 1000px) {
            margin-left: 50px !important;
          }

          @media screen and (max-width: 360px) {
            margin-top: -4px;
          }
        }

        .col {
          display: inline-block;
          vertical-align: middle;
          width: 20%;
          padding: 12px 0;
          min-width: 120px;
          margin-left: -4px;
          @media screen and (max-width: 360px) {
            padding: 3px 0;
          }
        }

        .col_date {
          width: 40px;
          display: inline-block;
          color: #979797;
        }
      }
    }

    .global-info-panel {
      padding: 30px;
      line-height: 1.33;
      background-color: #fffedb;
      color: #393939;
      position: relative;
      margin-bottom: 20px;
    }

    .orange-info {
      font-size: 14px;
      margin-left: 33px;
      position: relative;

      &:after {
        content: '';
        display: block;
        position: absolute;
        top: -10px;
        left: -45px;
        background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGUvPjxnIGZpbGw9IiNGRkIxMzIiPjxwYXRoIGQ9Ik0yMCAxMGMtNS41MTQgMC0xMCA0LjQ4Ni0xMCAxMHM0LjQ4NiAxMCAxMCAxMCAxMC00LjQ4NiAxMC0xMC00LjQ4Ni0xMC0xMC0xMHptMCAxOGMtNC40MTEgMC04LTMuNTg5LTgtOHMzLjU4OS04IDgtOCA4IDMuNTg5IDggOC0zLjU4OSA4LTggOHoiLz48cGF0aCBkPSJNMjAgMTQuMjQyYTEuMjEzIDEuMjEzIDAgMSAwIDEuMjEyIDEuMjEzYzAtLjY2OS0uNTQ0LTEuMjEzLTEuMjEyLTEuMjEzem0wIDQuMjQzYS45MS45MSAwIDAgMC0uOTEuOTA5djUuNDU0YS45MS45MSAwIDAgMCAxLjgyIDB2LTUuNDU0YS45MS45MSAwIDAgMC0uOTEtLjkxeiIvPjwvZz48L3N2Zz4=) no-repeat center;
        width: 40px;
        height: 40px;
      }
    }
  }

  a {
    color: var(--color-primary);

    &:hover {
      text-decoration: underline;
    }
  }

  .agreement-wrapper {

    .ssi {
      display: none;
    }

    .c-check {
      display: none !important;
    }

    span + .ssi,
    span + .ssi {
      display: inline !important;
    }

    p {
      margin-bottom: 8px;
    }

    ul {
      margin-left: 20px;

      li {
        margin-bottom: 10px;
      }
    }

    h1,
    h2,
    h3 {
      padding-bottom: 20px;
    }

    table {
      border-color: #595959 !important;
    }

    .tabs {
      position: relative;
      min-height: 200px;
      clear: both;
      margin: 30px 30px 50px 30px;

      .tab {
        float: left;

        label {
          background: #eee;
          padding: 10px;
          border: 1px solid #ccc;
          left: 1px;
        }

        .active {
          background: white;
          z-index: 3;
          font-weight: bold;
          padding: 10px;
          border: 1px solid #ccc;
          margin-left: -1px;
          position: relative;
          left: 1px;
          border-bottom: 0 solid white;

          .tab-content {
            z-index: 2;
          }
        }

        .tab-content {
          position: absolute;
          top: 28px;
          left: 0;
          background: white;
          right: 0;
          bottom: 0;
          padding: 10px 20px;
          border: 1px solid #ccc;
          overflow-y: auto;
        }
      }
    }

    .global-info-panel {
      padding: 30px;
      line-height: 1.33;
      background-color: #fffedb;
      color: #393939;
      position: relative;
      margin: 0 0 20px;
    }

    .c-agreement__body {
      line-height: 1.2;
      color: #595959;

      ul {
        margin-left: 20px;

        li {
          margin-bottom: 10px;
        }
      }

      h1,
      h2,
      h3 {
        padding-bottom: 20px;
      }

      table {
        border-color: #595959 !important;
      }
    }

    .schedule-table .schedule-row {
      display: block !important;
    }
  }
}
