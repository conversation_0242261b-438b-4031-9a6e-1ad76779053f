'use client';
import './styles.scss';

import React from 'react';
import {parseSafeHtml} from '@/utils/ParseSafeHtml';
import {Paper} from '@mui/material';
import Box from '@mui/material/Box';

interface EmailPreviewProps {
    html?: string | null;
}

export const HTMLEmailPreview = ({html}: EmailPreviewProps) => {
    if (!html) {
        return null;
    }

    return (
        <Paper elevation={0} className='kas-html-preview'>
            <Box py={3} px={2}>
                {parseSafeHtml(html)}
            </Box>
        </Paper>
    );
};
