import React, {useEffect, useRef, useState} from 'react';
import Box from '@mui/material/Box';
import {KasLoading, KasLoadingBackDrop, KasSwitch, KasSwitchWhen} from '@/components';
import {ErrorView, HTMLEmailPreview, LetterPreview} from '@/views';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {DEFAULT_ERROR_MSG} from '@/constants';

interface TemplatePreviewProps {
    url: string;
    showLoader?: boolean;
    isLetter?: boolean;
}

export const TemplatePreview = ({url, showLoader = false, isLetter = false}: TemplatePreviewProps) => {
    const [htmlState, setHtmlState] = useState(getDefaultState<string>());
    const abortControllerRef = useRef<AbortController | null>(null);

    const loadHtml = async () => {
        abortControllerRef.current?.abort();
        const controller = new AbortController();
        abortControllerRef.current = controller;
        setHtmlState(getLoadingState(htmlState));

        try {
            const response = await apiRequest(url, {
                signal: controller.signal,
            });

            setHtmlState(getLoadedState(response));
        } catch (error: any) {
            if (error.name === 'AbortError' || error.message?.includes('aborted')) {
                return;
            }

            setHtmlState({
                data: null,
                error: error.message ?? DEFAULT_ERROR_MSG,
                loading: false,
            });
        }
    };

    useEffect(() => {
        loadHtml().then();

        return () => {
            abortControllerRef.current?.abort();
        };
    }, [url]);

    if (showLoader && htmlState.loading && !htmlState.data && !htmlState.error) {
        return <KasLoading />;
    }

    return (
        <div style={{position: showLoader ? 'relative' : 'inherit'}}>
            {htmlState.loading && <KasLoadingBackDrop />}
            <KasSwitch>
                <KasSwitchWhen condition={!!htmlState.error}>
                    <Box py={2}>
                        <ErrorView error={htmlState.error} onTryAgain={loadHtml} />
                    </Box>
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!htmlState.data}>
                    {isLetter ? (
                        <LetterPreview html={htmlState.data} />
                    ) : (
                        <HTMLEmailPreview html={htmlState.data} />
                    )}
                </KasSwitchWhen>
            </KasSwitch>
        </div>
    );
};
