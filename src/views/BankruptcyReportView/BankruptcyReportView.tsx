'use client';
import React from 'react';
import {KasInfo} from '@/components';
import {Grid2, Paper, Stack} from '@mui/material';
import {BankruptcyReportRecordModel} from '@/interfaces';

interface BankruptcyReportViewProps {
    records: BankruptcyReportRecordModel[];
}

export const BankruptcyReportView = ({records}: BankruptcyReportViewProps) => {
    return (
        <Stack spacing={2}>
            {records.map((record) => (
                <Paper key={record.case_id} elevation={0}>
                    <Grid2 container spacing={2} p={1.5}>
                        <Grid2 size={4}>
                            <KasInfo label='Case'>
                                {record.case_name ? (<abbr title={`ID:  ${record.case_id}`}>{record.case_name}</abbr>) : record.case_id}
                            </KasInfo>
                        </Grid2>
                        <Grid2 size={1.5}>
                            <KasInfo label='Chapter'>{record.bk_chapter}</KasInfo>
                        </Grid2>
                        <Grid2 size={3.5}>
                            <KasInfo label='Disposition'>{record.case_disposition}</KasInfo>
                        </Grid2>
                        <Grid2 size={1.5}>
                            <KasInfo label='Start'>{record.case_start_date}</KasInfo>
                        </Grid2>
                        <Grid2 size={1.5}>
                            <KasInfo label='End'>{record.case_end_date}</KasInfo>
                        </Grid2>
                    </Grid2>
                </Paper>
            ))}
        </Stack>
    );
};
