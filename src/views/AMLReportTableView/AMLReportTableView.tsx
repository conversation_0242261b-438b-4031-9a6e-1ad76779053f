import React, {useEffect, useRef, useState} from 'react';
import {AmlReportColumns} from './tables';
import {ComplianceAmlModel} from '@/screens/ComplianceScreen/interfaces';
import {TableView} from '@/views';
import {apiRequest} from '@/utils/AxiosUtils';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {Completable} from '@/interfaces';

export interface AMLReportTableViewProps {
    id: number;
    type: 'loan' | 'employee';
}

export const AMLReportTableView = ({id, type}: AMLReportTableViewProps) => {
    const abortControllerRef = useRef<AbortController | null>(null);
    const [state, setState] = useState(getDefaultState<ComplianceAmlModel[]>());

    const loadDetails = async () => {
        const url = `/api/secured/compliance/aml/${type}/${id}/report`;

        try {
            const controller = new AbortController();

            abortControllerRef.current = controller;
            setState(getLoadingState(state));
            const response: Completable<ComplianceAmlModel[]> = await apiRequest(url, {
                signal: controller.signal,
            });
            setState(getLoadedState(response));
        } catch (error) {
            if (!(error instanceof DOMException && error.name === 'AbortError')) {
                throw error;
            }
        }
    };

    useEffect(() => {
        loadDetails().then();

        return () => {
            if (abortControllerRef.current) {
                abortControllerRef.current.abort();
            }
        };
    }, []);

    return (
        <TableView
            withTableActions={true}
            loading={state.loading}
            error={state.error}
            data={state.data}
            columns={AmlReportColumns}
            tableName='AML Report'
            sortingColumns={[{id: 'transaction_effective_date', desc: true}]}
        />
    );
};
