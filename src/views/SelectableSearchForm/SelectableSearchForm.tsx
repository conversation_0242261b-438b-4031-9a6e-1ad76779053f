import React, {useEffect} from 'react';
import {<PERSON>ton, Grid2} from '@mui/material';
import {useFormik} from 'formik';
import {validationSchema} from './schema';
import {SelectableSearchField} from './components';
import {SelectableSearchValueModel, SelectModel} from '@/interfaces';
import {prepareSearchValue} from '@/utils/SearchUtils';

interface SelectableSearchFormProps {
    options: SelectModel<string>[];
    placeholder: string;
    loading: boolean;
    selectedValue?: string | null;
    onSubmit: (values: SelectableSearchValueModel) => Promise<void | boolean>;
}

export const SelectableSearchForm = ({
    options,
    placeholder,
    loading,
    selectedValue = null,
    onSubmit,
}: SelectableSearchFormProps) => {
    const onSelectHandler = async (selectedValue: string | null) => {
        const {param, value, text} = prepareSearchValue(selectedValue);

        await formik.setFieldValue('param', param);
        await formik.setFieldValue('value', value);
        await formik.setFieldValue('text', text);
    };

    const onSubmitHandler = async (values: SelectableSearchValueModel) => {
        const result = await onSubmit(values);

        if (result === true) {
            formik.resetForm();
        }
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            param: 'al',
            value: '',
            text: '',
        },
        onSubmit: onSubmitHandler,
        validationSchema,
    });

    useEffect(() => {
        if (selectedValue != null) {
            onSelectHandler(selectedValue).then();
        }
    }, [selectedValue]);

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={1} rowSpacing={2}>
                <Grid2 size={9}>
                    <SelectableSearchField
                        options={options}
                        formik={formik}
                        placeholder={placeholder}
                        searchLoading={loading}
                        onSelectHandler={onSelectHandler}
                    />
                </Grid2>
                <Grid2 size={3}>
                    <Button type='submit' fullWidth variant='contained' disabled={!formik.isValid || loading}>
                        Search
                    </Button>
                </Grid2>
            </Grid2>
        </form>
    );
};
