import React, {useState} from 'react';
import {CustomPaymentsSchedule, PaymentFrequencyForm} from './components';
import {KasModal} from '@/components';
import {PaymentFrequencyViewProvider} from '@/views/PaymentFrequencyView/usePaymentFrequencyView';
import {PaymentFrequencyValues} from '@/views';

interface PaymentFrequencyViewProps {
    loanId: number;
    payOffAmount: number;
    installmentAmount: number;
    disabled?: boolean;
    widthStrictOption?: boolean;
    onApply: (values: PaymentFrequencyValues) => Promise<void>;
}

export const PaymentFrequencyView = ({
    loanId,
    payOffAmount,
    installmentAmount,
    disabled = false,
    widthStrictOption = false,
    onApply,
}: PaymentFrequencyViewProps) => {
    const [openCustomPaymentModal, setOpenCustomPaymentModal] = useState(false);

    const onClose = () => setOpenCustomPaymentModal(false);

    return (
        <PaymentFrequencyViewProvider
            loanId={loanId}
            payOffAmount={payOffAmount}
            installmentAmount={installmentAmount}
            onApply={onApply}>
            <PaymentFrequencyForm
                disabled={disabled}
                widthStrictOption={widthStrictOption}
                onCustomPayments={() => setOpenCustomPaymentModal(true)}
            />
            <KasModal title='Custom Payment Schedule' open={openCustomPaymentModal} onClose={onClose}>
                <CustomPaymentsSchedule onClose={onClose} />
            </KasModal>
        </PaymentFrequencyViewProvider>
    );
};
