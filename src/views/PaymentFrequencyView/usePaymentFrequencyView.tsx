import React, {createContext, useContext, useEffect, useMemo, useState} from 'react';
import {FormikValues, useFormik} from 'formik';
import {validationSchema} from './schema';
import {PaymentFrequency} from '@/interfaces';
import {PaymentFrequencyValues} from '@/views';

interface PaymentFrequencyViewContextModel {
    loanId: number;
    payOffAmount: number;
    installmentAmount: number;
    formik: FormikValues;
    isMultiplePayment: boolean;
    submitting: boolean;
}

const PaymentFrequencyViewContext = createContext<PaymentFrequencyViewContextModel | undefined>(undefined);

interface PaymentFrequencyViewProviderProps {
    loanId: number;
    payOffAmount: number;
    installmentAmount: number;
    children: React.ReactNode;
    onApply: (values: PaymentFrequencyValues) => Promise<void>;
}

export const PaymentFrequencyViewProvider = ({
    loanId,
    payOffAmount,
    installmentAmount,
    onApply,
    children,
}: PaymentFrequencyViewProviderProps) => {
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async (values: PaymentFrequencyValues) => {
        setSubmitting(true);
        await onApply(values);
        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            frequency: '',
            date: '',
            installment: '',
            payments: [],
            strict: false,
        },
        onSubmit,
        validationSchema,
    });

    const isMultiplePayment = useMemo(
        () => !!formik.values.frequency && formik.values.frequency !== PaymentFrequency.SINGLE,
        [formik.values.frequency],
    );

    useEffect(() => {
        formik.setFieldValue('installment', isMultiplePayment ? '' : payOffAmount);
    }, [isMultiplePayment]);

    const value: PaymentFrequencyViewContextModel = {
        loanId,
        payOffAmount,
        installmentAmount,
        formik,
        isMultiplePayment,
        submitting,
    };

    return (
        <PaymentFrequencyViewContext.Provider value={value}>{children}</PaymentFrequencyViewContext.Provider>
    );
};

export function usePaymentFrequencyView() {
    const context = useContext(PaymentFrequencyViewContext);
    if (!context) {
        throw new Error('usePaymentFrequencyView must be used within PaymentFrequencyViewProvider');
    }
    return context;
}
