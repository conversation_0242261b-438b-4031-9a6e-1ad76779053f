import React from 'react';
import {KasDatePickerFormField} from '@/components';
import {TextField, Grid2, InputAdornment, Button} from '@mui/material';
import {useFormik} from 'formik';
import {AddPaymentFormValues, validationSchema} from './schema';
import dayjs from 'dayjs';
import {PaymentDTO} from '@/models';
import {Add} from '@mui/icons-material';

interface AddPaymentFormProps {
    onAddPayment: (value: PaymentDTO) => void;
}

export const AddPaymentForm = ({onAddPayment}: AddPaymentFormProps) => {
    const onSubmit = async (values: AddPaymentFormValues) => {
        onAddPayment({
            date: dayjs(values.date).format('YYYYMMDD'),
            amount: Number(values.amount).toFixed(2),
            dirty: true,
        });
        formik.resetForm();
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            date: '',
            amount: '',
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={3}>
                    <KasDatePickerFormField
                        formik={formik}
                        name='date'
                        disablePast
                        shouldDisableDate={(date) => dayjs(date).isSame(dayjs(), 'day')}
                        label='Date'
                    />
                </Grid2>
                <Grid2 size={3}>
                    <TextField
                        fullWidth
                        size='small'
                        name='amount'
                        value={formik.values.amount}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Amount'
                        variant='outlined'
                        type='number'
                        slotProps={{
                            input: {
                                startAdornment: <InputAdornment position='start'>$</InputAdornment>,
                            },
                            htmlInput: {
                                step: '0.01',
                            },
                        }}
                        error={!!formik.errors.amount && formik.touched.amount}
                        helperText={formik.touched.amount && formik.errors.amount}
                    />
                </Grid2>
                <Grid2 size={6}>
                    <Button
                        variant='outlined'
                        size='small'
                        type='submit'
                        title='Add Payment'
                        disabled={!(formik.isValid && formik.dirty)}
                        color='primary'>
                        <Add fontSize='small' />
                    </Button>
                </Grid2>
            </Grid2>
        </form>
    );
};
