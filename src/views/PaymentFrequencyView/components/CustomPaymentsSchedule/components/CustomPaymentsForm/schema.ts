import * as Yup from 'yup';
import {DEFAULT_VALIDATION_MSG} from '@/constants';

export const validationSchema = Yup.object().shape({
    frequency: Yup.string().required(DEFAULT_VALIDATION_MSG),
    date: Yup.string().required(DEFAULT_VALIDATION_MSG).nullable(),
    installment: Yup.string().required(DEFAULT_VALIDATION_MSG),
    payoff: Yup.string().required(DEFAULT_VALIDATION_MSG),
});

export type CustomPaymentsFormValues = Yup.Asserts<typeof validationSchema>;
