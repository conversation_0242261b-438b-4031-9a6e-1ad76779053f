import React from 'react';
import {Kas<PERSON><PERSON>completeField, KasDatePickerFormField} from '@/components';
import {
    Button,
    Checkbox,
    CircularProgress,
    FormControlLabel,
    Grid2,
    InputAdornment,
    TextField,
} from '@mui/material';
import dayjs from 'dayjs';
import {PAYMENT_FREQUENCY_OPTIONS} from '@/constants';
import {usePaymentFrequencyView} from '@/views/PaymentFrequencyView/usePaymentFrequencyView';

interface PaymentFrequencyFormProps {
    disabled: boolean;
    widthStrictOption: boolean;
    onCustomPayments: () => void;
}

export const PaymentFrequencyForm = ({
    disabled,
    widthStrictOption,
    onCustomPayments,
}: PaymentFrequencyFormProps) => {
    const {formik, submitting, isMultiplePayment} = usePaymentFrequencyView();

    const handleSubmit = async () => {
        const date = dayjs(formik.values.date).format('YYYYMMDD');

        await formik.setValues({...formik.values, date, payments: []});
        await formik.submitForm();
    };

    return (
        <Grid2 container spacing={2}>
            <Grid2 size={4}>
                <KasAutocompleteField
                    name='frequency'
                    label='Frequency'
                    disabled={submitting}
                    options={PAYMENT_FREQUENCY_OPTIONS}
                    formik={formik}
                />
            </Grid2>
            {formik.values.frequency && (
                <>
                    <Grid2 size={4}>
                        <KasDatePickerFormField
                            formik={formik}
                            name='date'
                            disablePast
                            shouldDisableDate={(date) => dayjs(date).isSame(dayjs(), 'day')}
                            label={isMultiplePayment ? 'Start Date' : 'Request Date'}
                            disabled={submitting}
                        />
                    </Grid2>
                    <Grid2 size={4}>
                        <TextField
                            fullWidth
                            size='small'
                            name='installment'
                            value={formik.values.installment}
                            disabled={submitting}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            label={isMultiplePayment ? 'Installment Amount' : 'Amount'}
                            variant='outlined'
                            type='number'
                            slotProps={{
                                input: {
                                    startAdornment: <InputAdornment position='start'>$</InputAdornment>,
                                },
                                htmlInput: {
                                    step: '0.01',
                                },
                            }}
                            error={!!formik.errors.installment && formik.touched.installment}
                            helperText={formik.touched.installment && formik.errors.installment}
                        />
                    </Grid2>
                </>
            )}
            <Grid2 size={12}>
                <Grid2 container spacing={2}>
                    <Grid2 size={6}>
                        {widthStrictOption && isMultiplePayment && (
                            <FormControlLabel
                                label={
                                    <abbr title='When selected, the link will only be valid until the first payment date'>
                                        Strict Payment Schedule
                                    </abbr>
                                }
                                control={
                                    <Checkbox
                                        size='small'
                                        name='strict'
                                        onChange={formik.handleChange}
                                        onBlur={formik.handleBlur}
                                    />
                                }
                            />
                        )}
                    </Grid2>
                    <Grid2 size={3} ml='auto'>
                        <Button
                            variant='contained'
                            fullWidth
                            size='small'
                            onClick={handleSubmit}
                            disabled={!formik.isValid || submitting || disabled}
                            color='primary'>
                            {submitting ? <CircularProgress size={16} /> : 'Apply'}
                        </Button>
                    </Grid2>
                    {isMultiplePayment && (
                        <Grid2 size={3}>
                            <Button
                                variant='outlined'
                                fullWidth
                                size='small'
                                disabled={disabled || submitting}
                                onClick={onCustomPayments}>
                                Custom Payments
                            </Button>
                        </Grid2>
                    )}
                </Grid2>
            </Grid2>
        </Grid2>
    );
};
