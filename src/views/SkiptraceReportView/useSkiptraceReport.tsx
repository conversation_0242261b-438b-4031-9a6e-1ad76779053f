'use client';
import React, {createContext, useContext} from 'react';
import {SkiptraceReportDetailsTabType} from '@/interfaces';

interface SkiptraceReportContextModel {
    employeeId: number;
    defaultTab: SkiptraceReportDetailsTabType;
}

const SkiptraceReportContext = createContext<SkiptraceReportContextModel | undefined>(undefined);

interface SkiptraceReportProviderProps {
    children: React.ReactNode;
    employeeId: number;
    defaultTab?: SkiptraceReportDetailsTabType;
}

export const SkiptraceReportProvider: React.FC<SkiptraceReportProviderProps> = ({
    children,
    employeeId,
    defaultTab = SkiptraceReportDetailsTabType.Names,
}) => {
    const value: SkiptraceReportContextModel = {
        employeeId,
        defaultTab,
    };

    return <SkiptraceReportContext.Provider value={value}>{children}</SkiptraceReportContext.Provider>;
};

export function useSkiptraceReport() {
    const context = useContext(SkiptraceReportContext);
    if (!context) {
        throw new Error('useSkiptraceReport must be used within SkiptraceReportProvider');
    }
    return context;
}
