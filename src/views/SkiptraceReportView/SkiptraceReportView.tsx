import React, {useEffect, useState} from 'react';
import {KasSwitch, KasSwitchWhen} from '@/components';
import {apiRequest} from '@/utils/AxiosUtils';
import {SkiptraceReportPreviewTabs} from './components';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {SkiptracePreviewReportModel, SkiptraceReportDetailsTabType, SkiptraceReportModel} from '@/interfaces';
import {SkiptraceReportProvider} from '@/views/SkiptraceReportView';
import {Grid2, Skeleton} from '@mui/material';
import {NoResultsView, ErrorView} from '@/views';

interface SkiptraceReportViewProps {
    skiptrace: SkiptraceReportModel;
    employeeId: number;
    defaultTab?: SkiptraceReportDetailsTabType;
    onSkiptraceReportLoaded?: (value: SkiptracePreviewReportModel) => void;
}

export const SkiptraceReportView = ({
    skiptrace,
    employeeId,
    defaultTab,
    onSkiptraceReportLoaded,
}: SkiptraceReportViewProps) => {
    const [state, setState] = useState(getDefaultState<SkiptracePreviewReportModel>());

    const loadData = async () => {
        setState(getLoadingState(state));
        const response = await apiRequest(
            `/api/secured/underwriting/reports/${employeeId}/skiptrace/${skiptrace.gid}`,
        );
        setState(getLoadedState(response));

        if (onSkiptraceReportLoaded && response.value) {
            onSkiptraceReportLoaded({...response.value, gid: skiptrace.gid});
        }
    };

    useEffect(() => {
        loadData().then();
    }, []);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={state.loading}>
                <Grid2 container spacing={2}>
                    <Grid2 size={12}>
                        <Skeleton variant='rounded' animation='wave' height={40} width='40%' />
                    </Grid2>
                    <Grid2 size={6}>
                        <Skeleton variant='rounded' animation='wave' height={30} />
                    </Grid2>
                    <Grid2 size={6}>
                        <Skeleton variant='rounded' animation='wave' height={30} />
                    </Grid2>
                </Grid2>
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!state.error}>
                <ErrorView error={state.error} />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!state.data}>
                {!!state.data?.records.length ? (
                    <SkiptraceReportProvider employeeId={employeeId} defaultTab={defaultTab}>
                        <SkiptraceReportPreviewTabs data={state.data} />
                    </SkiptraceReportProvider>
                ) : (
                    <NoResultsView
                        text={`No Records Found for Transaction ID#: ${state.data?.transaction_id}`}
                    />
                )}
            </KasSwitchWhen>
        </KasSwitch>
    );
};
