import {defaultInfoColumn} from '@/utils/TableUtils';
import {createColumnHelper} from '@tanstack/react-table';
import {SkiptraceRecordNameModel} from '@/interfaces';

const columnHelper = createColumnHelper<SkiptraceRecordNameModel>();

const _defaultInfoColumn = defaultInfoColumn<SkiptraceRecordNameModel>;

export const NamesSkiptraceReportTableColumns = [
    _defaultInfoColumn('name', 'Name'),
    _defaultInfoColumn('gender', 'Gender'),
    columnHelper.accessor('match', {
        id: 'match',
        header: 'Match',
        cell: (props) => Math.round((props.getValue() || 0) * 100) + '%',
    }),
];
