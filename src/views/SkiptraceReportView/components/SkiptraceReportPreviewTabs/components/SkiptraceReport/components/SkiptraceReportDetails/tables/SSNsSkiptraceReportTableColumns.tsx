import {defaultInfoColumn, expandCell} from '@/utils/TableUtils';
import {CellContext, createColumnHelper} from '@tanstack/react-table';
import {KasFlaggedIcon} from '@/components';
import React from 'react';
import {SkiptraceRecordSSNModel} from '@/interfaces';

const columnHelper = createColumnHelper<SkiptraceRecordSSNModel>();

const _defaultInfoColumn = defaultInfoColumn<SkiptraceRecordSSNModel>;

export const SSNsSkiptraceReportTableColumns = [
    {
        id: 'expander',
        header: '',
        cell: (props: CellContext<SkiptraceRecordSSNModel, string>) => expandCell(props),
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
    _defaultInfoColumn('ssn', 'SSN'),
    _defaultInfoColumn('start', 'Issued Start Date'),
    _defaultInfoColumn('end', 'Issued End Date'),
    columnHelper.accessor('valid', {
        id: 'valid',
        header: 'Valid',
        cell: (props) => <KasFlaggedIcon flagged={props.getValue()} />,
    }),
];
