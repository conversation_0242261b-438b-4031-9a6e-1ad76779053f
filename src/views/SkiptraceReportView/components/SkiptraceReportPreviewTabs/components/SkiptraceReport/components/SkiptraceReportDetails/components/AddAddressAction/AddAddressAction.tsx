import React from 'react';
import AddIcon from '@mui/icons-material/Add';
import {ActionCell} from '@/components/table/cells';
import {AddressDTO} from '@/models';
import {useSkiptraceReport} from '@/views/SkiptraceReportView';
import {GlobalModal, useGlobalModal} from '@/components';

export const AddAddressAction = ({address}: {address: AddressDTO}) => {
    const {employeeId} = useSkiptraceReport();
    const {showGlobalModal} = useGlobalModal();

    return (
        <ActionCell
            Icon={<AddIcon fontSize='small' titleAccess='Add Address' />}
            onClick={() => {
                showGlobalModal({
                    type: GlobalModal.Employee_Address,
                    props: {
                        method: 'post',
                        employeeId,
                        address,
                        source: 'ACCURINT',
                    },
                });
            }}
        />
    );
};
