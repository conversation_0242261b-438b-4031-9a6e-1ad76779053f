import './styles.scss';

import React, {useState} from 'react';
import {Alert, Button, ButtonGroup} from '@mui/material';
import {SkiptraceReport} from './components';
import Box from '@mui/material/Box';
import {SkiptracePreviewReportModel} from '@/interfaces';

interface SkiptraceReportPreviewTabsProps {
    data: SkiptracePreviewReportModel;
}

export const SkiptraceReportPreviewTabs = ({data}: SkiptraceReportPreviewTabsProps) => {
    const [activeRecord, setActiveRecord] = useState(data.records[0]);

    return (
        <div className='kas-skiptrace-report-view-tabs'>
            {data.records.some((record) => !!record.dods.length) && (
                <Box mb={2}>
                    <Alert severity='error'>Date of Death record found!</Alert>
                </Box>
            )}
            <ButtonGroup>
                {data.records.map((record) => (
                    <Button
                        key={record.id}
                        variant={activeRecord.id === record.id ? 'contained' : 'text'}
                        onClick={() => setActiveRecord(record)}>
                        Record {record.id} ({Math.round((record.match || 0) * 100)}% Match)
                    </Button>
                ))}
            </ButtonGroup>

            <div className='kas-skiptrace-report-view-tabs__content'>
                <SkiptraceReport
                    record={activeRecord}
                    reportDate={data.report_date}
                    transactionId={data.transaction_id}
                />
            </div>
        </div>
    );
};
