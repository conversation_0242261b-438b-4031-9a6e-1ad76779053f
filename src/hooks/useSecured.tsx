'use client';
import React, {createContext, useContext, useMemo, useState} from 'react';
import {selectUser} from '@/lib/slices/userSlice';
import {useAppSelector} from '@/lib/hooks';
import {ExtendedRoleEnum, KasDesignation, UsersWebDTO} from '@/models';
import {DashboardScreenType} from '@/interfaces';
import RoleEnum = UsersWebDTO.RoleEnum;

interface RouteModel {
    roles: ExtendedRoleEnum[];
    routes: string[];
    requireAll?: boolean;
}

interface SecuredContextModel {
    routes: string[];
    activeRoute: string;
    setActiveRoute: (value: string) => void;
    isAvailableRoute: (path: string) => boolean;
    hasAnyRole: (roles: ExtendedRoleEnum[], ignoreAdmin?: boolean) => boolean;
    hasRoles: (roles: RoleEnum[], ignoreAdmin?: boolean) => boolean;
    hasAnyDesignation: (designations: KasDesignation[], ignoreAdmin?: boolean) => boolean;
    isUserAdmin: boolean;
}

const SecuredContext = createContext<SecuredContextModel | undefined>(undefined);

export const ADMIN_ROUTES = Object.entries(DashboardScreenType).map(([_, value]) => value);

const ROUTES: RouteModel[] = [
    {
        roles: ['KASH_ADMIN'],
        routes: ADMIN_ROUTES,
    },
    {
        roles: ['KASH_SYSTEM'],
        routes: [DashboardScreenType.System],
    },
    {
        roles: ['KASH_POWERUSER'],
        routes: [
            DashboardScreenType.Underwriting,
            DashboardScreenType.Verification,
            DashboardScreenType.Monitor,
        ],
    },
    {
        roles: ['KASH_ACCOUNTS'],
        routes: [DashboardScreenType.Accounts],
    },
    {
        roles: ['KASH_OPERATOR'],
        routes: [DashboardScreenType.Operator],
    },
    {
        roles: ['KASH_MONITOR'],
        routes: [DashboardScreenType.Monitor],
    },
    {
        roles: ['KASH_ACCOUNTING'],
        routes: [DashboardScreenType.Accounting],
    },
    {
        roles: ['KASH_VERIFICATION'],
        routes: [DashboardScreenType.Verification],
    },
    {
        roles: ['KASH_COLLECTIONS'],
        routes: [DashboardScreenType.Collections],
    },
    {
        roles: ['KASH_COMPLIANCE'],
        routes: [DashboardScreenType.Compliance],
    },
    {
        roles: ['KASH_UNDERWRITING', 'KASH_SUPPORT'],
        routes: [DashboardScreenType.Underwriting],
    },
    {
        roles: ['KASH_COLLECTIONS:MANAGER'],
        routes: [DashboardScreenType.Accounts, DashboardScreenType.Manager],
    },
    {
        roles: ['KASH_VERIFICATION:MANAGER'],
        routes: [DashboardScreenType.Manager],
    },
    {
        roles: ['KASH_SUPPORT:MANAGER'],
        routes: [DashboardScreenType.Manager],
    },
    {
        roles: ['KASH_ONBOARDING'],
        routes: [DashboardScreenType.Onboarding2],
    },
    {
        roles: ['KASH_ONBOARDING_QA'],
        routes: [DashboardScreenType.Onboarding2],
    },
    {
        roles: ['KASH_POWERUSER', 'KASH_OPERATOR'],
        routes: [DashboardScreenType.Origination],
        requireAll: true,
    },
];

export const SecuredProvider = ({children}: {children: React.ReactNode}) => {
    const user = useAppSelector(selectUser);
    const [activeRoute, setActiveRoute] = useState('');

    const userRoles = useMemo(() => user.value?.roles || [], [user.value?.roles]);

    const routes = useMemo(() => {
        let routesArr: string[] = [];

        ROUTES.forEach(({roles, routes, requireAll}) => {
            const hasAccess = requireAll
                ? roles.every((role) => userRoles.includes(role))
                : roles.some((role) => userRoles.includes(role));

            if (hasAccess) {
                routesArr = [...routesArr, ...routes];
            }
        });

        routesArr = ADMIN_ROUTES.filter((item) => routesArr.includes(item));

        return routesArr;
    }, [userRoles]);

    const isAvailableRoute = (path: string) => {
        if (routes) {
            return routes.includes(path);
        }

        return false;
    };

    const isUserAdmin = useMemo(() => {
        const adminRoles = ['KASH_ADMIN'];

        return userRoles.some((role) => adminRoles.includes(role));
    }, [userRoles]);

    const hasAnyRole = (roles: ExtendedRoleEnum[], ignoreAdmin = false) => {
        return (!ignoreAdmin && isUserAdmin) || userRoles.some((role) => roles.includes(role));
    };

    const hasRoles = (roles: RoleEnum[], ignoreAdmin = false) => {
        if (!ignoreAdmin && isUserAdmin) {
            return true;
        }
        return roles.every((role) => userRoles.includes(role));
    };

    const hasAnyDesignation = (designations: KasDesignation[], ignoreAdmin = false) => {
        return (
            (!ignoreAdmin && isUserAdmin) ||
            userRoles.some((role) => designations.some((designation) => role.endsWith(`:${designation}`)))
        );
    };

    const value: SecuredContextModel = {
        routes,
        activeRoute,
        setActiveRoute,
        isAvailableRoute,
        hasAnyRole,
        hasRoles,
        hasAnyDesignation,
        isUserAdmin,
    };

    return <SecuredContext.Provider value={value}>{children}</SecuredContext.Provider>;
};

export function useSecured() {
    const context = useContext(SecuredContext);
    if (!context) {
        throw new Error('useSecured must be used within SecuredProvider');
    }
    return context;
}
