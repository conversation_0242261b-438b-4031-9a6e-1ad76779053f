export interface EmployeeUnderwritingDTO {
    gid: number;
    first_name: string;
    last_name: string;
    ssn: string;
    dob: string;
    status: string;
    initial_hire_date: string;
    last_hire_date: string;
    termination_date: string;
    payroll_group_id: number;
    id_at_employer: number;
    email_at_employer: string;
    job_code: string;
    position: string;
    department: string;
    full_part_time: string;
    pay_type: string;
    pay_rate: string;
    estimated_annual_hours_worked: string;
    commission: string;
    benefit_eligible_employee: boolean;
    income_flagged: boolean;
    phone1: string;
    phone1_type: string;
    phone2: string;
    phone2_type: string;
    payroll_deduction_status: string;
    payroll_deduction_ever_revoked: boolean;
    arbitration_agreement_status: string;
    arbitration_agreement_ever_revoked: boolean;
    current_loan_id: number;
    paper_notices: boolean;
    privacy_agreement_sent: boolean;
    merge_id: number;
    manual_review_date: string;
    bankruptcy_date: string;
    deceased_date: string;
    holiday_calendar_mode: string;
    user_id: number;
    guarantee: boolean;
    bank_data_source: string;
    bank_aba: string;
    bank_account_number: string;
    bank_account_type: string;
    primary_email: string;
    primary_phone: string;
    primary_address: string;
    identity_verified: boolean;
    benefit_ineligible_rule: string;
    employer_id: number;
    employer_name: string;
    primary_bank_id: number;
    account_balance: string;
    loan_balance: number;
    refund: number;
    workflow: string;
    employer_locked: boolean;
    ssn_locked: boolean;
    dob_locked: boolean;
    direct_deposit_account_number: string;
    direct_deposit_reported_date: string;
    application_status: string;
    merged_date: string;
    merged_from: number[] | null;
    bankruptcy_chapter: string;
    open_bankruptcy_petition: boolean;
    deceased: boolean;
    politically_exposed: string;
    politically_exposed_verify_time: string;
    phone_cnam: string;
    hardship: string;
    hardship_expiry_date: string;
    referral_code: string;
    review_matches_by_ip: {
        ip_address: string;
        last_seen: string;
        match_id: number;
        match_last_seen: string;
        link_comment: string;
    }[];
    refinance_eligible: boolean;
    refinance_eligible_date: string;
    collection_agency: string;
    deposit_confirmation_required: boolean;
    recent_account_change: boolean;
    scra_open_date: string;
    scra_close_date: string;
    application_precheck: boolean;
    application_precheck_rule: string;
}