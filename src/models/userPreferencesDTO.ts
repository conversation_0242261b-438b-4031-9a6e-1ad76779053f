import {UnderwritingProfileItemModel} from '@/models/underwritingDTO';
import {ConsumersApplicationItemModel} from '@/screens/ComplianceScreen/components/ComplianceContent/components/ComplianceConsumers/components/ConsumersApplication/interfaces';
import {ThemeMode} from '@/interfaces';
import {ComplianceBanksItemModel} from '@/screens/ComplianceScreen/components/ComplianceContent/components/ComplianceBanks/interfaces';
import {ConsumersLoanItemModel} from '@/screens/ComplianceScreen/components/ComplianceContent/components/ComplianceConsumers/components/ConsumersLoan/interfaces';
import {ComplianceLoansItemModel} from '@/screens/ComplianceScreen/components/ComplianceContent/components/ComplianceLoans/interfaces';
import {ComplianceAMLItemModel} from '@/screens/ComplianceScreen/components/ComplianceContent/components/ComplianceAML/interfaces';

export interface UserPreferencesSettingsDTO {
    cancelTimeout?: boolean;
    comfortMode?: boolean;
    themeMode?: ThemeMode;
    snackbarHideDuration?: number;
}

export interface UserOnboardingPreferencesDTO {
    credential?: {
        key: string;
        username: string;
        type: string;
    };
}

export interface UserPreferencesDTO {
    underwriting?: {
        sections: UnderwritingProfileItemModel[];
    };
    compliance?: {
        consumers?: {
            application?: {
                sections: ConsumersApplicationItemModel[];
            };
            loan?: {
                sections: ConsumersLoanItemModel[];
            };
        };
        banks?: {
            sections: ComplianceBanksItemModel[];
        };
        loans?: {
            sections: ComplianceLoansItemModel[];
        };
        aml?: {
            sections: ComplianceAMLItemModel[];
        };
    };
    settings?: UserPreferencesSettingsDTO;
    onboarding?: UserOnboardingPreferencesDTO;
}
