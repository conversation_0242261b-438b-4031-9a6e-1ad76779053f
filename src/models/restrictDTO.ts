export interface CollectionAttemptsDTO {
    loan_id: number;
    notification_type: string;
    notification_template: string;
    communication_time: string;
}

export interface CommentDTO {
    gid: number;
    entity_class: string;
    entity_id: number;
    text: string;
    user_name: string;
    timestamp: string;
    priority: number;
    context: string;
}

export enum RestrictPermissionKey {
    PHONE = 'PHONE',
    EMAIL = 'EMAIL',
    LETTER = 'LETTER',
    SOCIAL = 'SOCIAL',
    MARKETING = 'MARKETING',
    SETTLEMENT = 'SETTLEMENT',
    SMS = 'SMS',
}

export type RestrictPermissionType = typeof RestrictPermissionKey;

export interface RestrictDTO {
    permissions: {
        [key in keyof RestrictPermissionType]: boolean;
    };
    last_updated_by: string;
    comments: CommentDTO[] | null;
    recent_collection_attempts: CollectionAttemptsDTO[];
}
