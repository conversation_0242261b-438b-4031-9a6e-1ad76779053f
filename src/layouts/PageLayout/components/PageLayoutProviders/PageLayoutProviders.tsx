import React from 'react';
import {SecuredProvider} from '@/hooks/useSecured';
import {SnackbarProvider} from '@/hooks/useSnackbar';
import {DownloadBlobProvider} from '@/hooks/useDownloadBlob';
import {PusherProvider} from '@/hooks/usePusher';

export const PageLayoutProviders = ({children}: {children: React.ReactNode}) => {
    return (
        <PusherProvider>
            <SnackbarProvider>
                <SecuredProvider>
                    <DownloadBlobProvider>{children}</DownloadBlobProvider>
                </SecuredProvider>
            </SnackbarProvider>
        </PusherProvider>
    );
};
