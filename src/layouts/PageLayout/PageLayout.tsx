'use client';
import styles from './styles.module.scss';
import React, {useCallback, useEffect, useMemo} from 'react';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>} from '@/components';
import {useAppDispatch, useAppSelector} from '@/lib/hooks';
import {fetchProfile, selectUser} from '@/lib/slices/userSlice';
import {usePathname, useRouter} from 'next/navigation';
import {SAVED_PATH_KEY} from '@/constants';
import {fetchUserPreferences, selectUserPreferences} from '@/lib/slices/userPreferencesSlice';
import {PageLayoutLoading, PageLayoutProviders} from './components';

export default function PageLayout({children}: {children: React.ReactNode}) {
    const user = useAppSelector(selectUser);
    const userPreferences = useAppSelector(selectUserPreferences);
    const dispatch = useAppDispatch();
    const router = useRouter();
    const pathname = usePathname();

    const isLoading = useMemo(
        () =>
            user.pending ||
            !user.value ||
            (userPreferences.pending && !userPreferences.value && !userPreferences.error),
        [user, userPreferences],
    );

    const handleRedirect = useCallback(() => {
        if (!user.pending) {
            if (!user.value) {
                const hash = new URL(window.location.href).hash;

                localStorage.setItem(SAVED_PATH_KEY, pathname + hash);
                router.replace('/login');
            } else {
                dispatch(fetchUserPreferences()).unwrap();
            }
        }
    }, [user]);

    useEffect(() => {
        if (!user.value) {
            dispatch(fetchProfile());
        }
    }, []);

    useEffect(handleRedirect, [handleRedirect]);

    if (isLoading) {
        return <PageLayoutLoading />;
    }

    return (
        <PageLayoutProviders>
            <div className={styles.kasPageLayout}>
                <div className={styles.kasPageLayout__wrap}>
                    <KasHeader />
                    <div className={styles.kasPageLayout__content}>{children}</div>
                    <KasFooter />
                </div>
            </div>
        </PageLayoutProviders>
    );
}
