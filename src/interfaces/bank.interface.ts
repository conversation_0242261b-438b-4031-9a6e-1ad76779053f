export interface BankModel {
    gid: number;
    employee_id: number;
    primary_account: boolean;
    aba_number: string;
    account_number: string;
    account_type: string;
    name: string;
    status: string;
    active: boolean;
    validated: boolean;
    validation_date: string;
    validation_provider: string;
    override: boolean;
    override_user_name: string;
    date_received: string;
    entry_source: string;
    create_time: string;
    last_update_time: string;
    confirm_requested: boolean;
    confirmed: boolean;
    confirm_date?: string;
    report_exists: boolean;
}

export interface BankDetailsModel {
    gid: number;
    employee_id: number;
    account_type: string;
    name: string;
    status: string;
    active: boolean;
    validated: boolean;
    validation_date: string;
    validation_provider: string;
    override: boolean;
    date_received: string;
    entry_source: string;
    create_time: string;
    last_update_time: string;
    confirm_requested: boolean;
    confirmed: boolean;
    duplicates: number[];
    comments: string[];
    report_exists: boolean;
}
