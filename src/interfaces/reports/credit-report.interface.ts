export interface CreditReportModel {
    gid: number;
    employee_id: number;
    report_date: string;
    reporting_agency: string;
    override: boolean;
    override_user_name: string;
    active: boolean;
    comment: string;
    credit_approved: boolean;
    hard_inquiry: boolean;
    fico_score: number;
    vantage_score: number;
    statements: string[];
}

export interface CreditPreviewReportModel {
    gid: number;
    employee_id: number;
    report_date: string;
    reporting_agency: string;
    active: boolean;
    report: string;
    credit_approved: boolean;
    override: boolean;
    hard_inquiry: boolean;
    override_user_name: string;
    frozen: boolean;
    fico_score: number;
    vantage_score: number;
    monthly_gross_pay: string;
    statements: string[];
}

export interface CreditReportConfigModel {
    experian?: {
        shortView?: boolean;
        modalView?: boolean;
    };
}
