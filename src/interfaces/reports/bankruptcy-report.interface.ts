export interface BankruptcyReportRecordModel {
    bk_party: number | null;
    bk_chapter: number | null;
    case_name: string;
    case_id: string;
    case_start_date: string;
    case_end_date: string;
    case_disposition: string | null;
}

export interface BankruptcyReportModel {
    gid: number;
    employee_id: number;
    report_date: string;
    reporting_agency: string;
    records: number;
    errors: number;
    override: boolean;
    override_user_name: string;
    active: boolean;
    comment: string;
    report: {
        errors?: string[];
        records?: BankruptcyReportRecordModel[];
    };
    error_message: string;
}
