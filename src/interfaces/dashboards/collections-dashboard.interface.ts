import {NotificationModel, TransactionModel} from '@/interfaces';
import {RestrictDTO} from '@/models/restrictDTO';
import {PaymentDTO, PhoneDTO, RecipientDTO} from '@/models';
import {AttachmentDTO} from '@/models/attachmentDTO';

export interface CollectionsLoanModel {
    loan_id: number;
    assigned_user_id: string | null;
    queue_time: string | null;
    followup_time: string | null;
    employee_id: number;
    state_code: string;
    employer_id: number;
    employer_name: string;
    loan_amount: number;
    loan_balance: number;
    delinquent_amount: number;
    ach_signed: boolean;
    delinquent_days: number;
    employee_first_name: string | null;
    employee_last_name: string | null;
    timezone: string;
    queue_id: number;
    reminder_code: string | null;
    reminder_date: string | null;
    reminder_time: string | null;
    collection_agency: string | null;
    retained: boolean;
    reminder_completed: string | null;
    reminder_text: string | null;
}

export interface CollectionsEmployeeModel {
    gid: number;
    first_name: string;
    last_name: string;
    ssn: string;
    dob: string;
    status: string;
    initial_hire_date: string;
    last_hire_date: string;
    termination_date: string | null;
    payroll_group_id: number;
    id_at_employer: string | null;
    email_at_employer: string | null;
    job_code: string | null;
    position: string | null;
    department: string | null;
    full_part_time: string;
    pay_type: string;
    pay_rate: string;
    estimated_annual_hours_worked: string | null;
    commission: string | null;
    benefit_eligible_employee: boolean;
    income_flagged: boolean;
    phone1: string;
    phone1_type: string;
    phone2: string | null;
    phone2_type: string | null;
    payroll_deduction_status: string;
    payroll_deduction_ever_revoked: boolean;
    arbitration_agreement_status: string;
    arbitration_agreement_ever_revoked: boolean;
    current_loan_id: number | null;
    paper_notices: boolean;
    privacy_agreement_sent: boolean;
    merge_id: string | null;
    manual_review_date: string | null;
    bankruptcy_date: string | null;
    deceased_date: string | null;
    holiday_calendar_mode: string;
    user_id: number;
    guarantee: boolean;
    bank_data_source: string | null;
    bank_aba: string | null;
    bank_account_number: string | null;
    bank_account_type: string | null;
    primary_email: string;
    primary_phone: string;
    primary_address: string;
    identity_verified: string | null;
    benefit_ineligible_rule: string | null;
    application_precheck: boolean;
    application_precheck_rule: string | null;
    employer_id: number;
    employer_name: string;
    primary_bank_id: number;
    account_balance: string;
    loan_balance: string;
    refund: number;
    workflow: string;
    employer_locked: boolean;
    ssn_locked: boolean;
    dob_locked: boolean;
    direct_deposit_account_number: string;
    direct_deposit_reported_date: string | null;
    application_status: string;
    merged_date: string | null;
    merged_from: [];
    bankruptcy_chapter: string | null;
    open_bankruptcy_petition: boolean;
    deceased: boolean;
    politically_exposed: boolean;
    politically_exposed_verify_time: string;
    phone_cnam: string | null;
    hardship: string | null;
    hardship_expiry_date: string | null;
    referral_code: string;
    review_matches_by_ip: [];
    refinance_eligible: boolean;
    refinance_eligible_date: string;
    collection_agency: string | null;
    deposit_confirmation_required: boolean;
    recent_account_change: boolean;
    scra_open_date: string | null;
    scra_close_date: string | null;
    integrations: [];
    restrictions: {
        PHONE: string | null;
        EMAIL: string | null;
        LETTER: string | null;
        SOCIAL: string | null;
        MARKETING: string | null;
        SETTLEMENT: string | null;
        SMS: string | null;
    };
}

export type CollectionsEmployeeWithLoan = Omit<CollectionsEmployeeModel, 'current_loan_id'> & {
    current_loan_id: number;
};

export interface CollectionsLoanAttemptModel {
    loan_id: number;
    notification_type: string;
    notification_template: string | undefined;
    communication_time: string;
}

export interface CollectionsLoanDelinquencyDetailsModel {
    last_payment_date?: string;
    last_payment_amount?: number;
    phone_added: number;
    phones?: PhoneDTO[];
    restrictions?: RestrictDTO;
    employer_name: string;
    open_bankruptcy: string;
    emails?: Record<string, string>[];
    email_added: number;
    retained: boolean;
    phone: string;
    recent_collection_attempts: CollectionsLoanAttemptModel[];
    employee_id: number;
    state: string;
    employer_id: number;
    email: string;
    loan_id: string;
}

export interface CollectionsLoanDelinquencySkiptraceModel {
    gid: number;
    agency: string;
    product: string;
    report_date: string;
    search_params: string[];
}

export interface CollectionsLoanDelinquencyCommentModel {
    gid: number;
    entity_id: number;
    entity_class: string;
    text: string;
    user_name: string;
    timestamp: string;
    priority: number;
    context: string;
}

export interface CollectionsLoanDelinquencyTagModel {
    tag_type: string;
    tag_value: string;
}

export interface CollectionsLoanDelinquencyModel {
    details: CollectionsLoanDelinquencyDetailsModel;
    comments: CollectionsLoanDelinquencyCommentModel[];
    skiptraces: CollectionsLoanDelinquencySkiptraceModel[];
    notifications: NotificationModel[];
    tags: CollectionsLoanDelinquencyTagModel[];
    transactions: TransactionModel[];
}

export interface CollectionsLoanPhoneReportModel {
    gid: number;
    phone: string;
    provider: 'OPEN_CNAM' | 'PINPOINT';
    report: string;
    report_date: string;
    active: boolean;
}

export interface CollectionsLoanPhonePinpointReportModel {
    countryCodeIso2?: string;
    countryCodeNumeric?: string;
    country?: string;
    city?: string;
    zipCode?: string;
    county?: string;
    timezone?: string;
    cleansedPhoneNumberNational?: string;
    cleansedPhoneNumberE164?: string;
    carrier?: string;
    phoneTypeCode?: number;
    phoneType?: string;
    originalPhoneNumber?: string;
    originalCountryCodeIso2?: string;
}

export type CollectionsPhoneOption =
    | 'contact_made'
    | 'no_answer'
    | 'no_contact_list'
    | 'left_voicemail'
    | 'etc';

export type CollectionsDelinquencyReason =
    | 'advised_filing_bankruptcy'
    | 'allotment_issue'
    | 'deceased'
    | 'disability'
    | 'on_temporary_leave'
    | 'out_of_work'
    | 'settlement'
    | 'etc';

export enum CollectionsContactPhoneEnum {
    ANSWERED = 'ANSWERED',
    VOICEMAIL = 'VOICEMAIL',
    NA = 'NA',
}

export interface CollectionsDebitEmailModel {
    email_name: string | null;
    email_type: string;
    sent_date: string | null;
    recipients: RecipientDTO[];
    attachments: AttachmentDTO[] | null;
    loanId: number;
}

export interface CollectionsDebitCardPNCModel {
    amount: string;
    city: string;
    email: string;
    first_name: string;
    id: string;
    key: string;
    last_name: string;
    loan_id: number;
    ssn4: string;
    state_cd: string;
    street1: string;
    street2: string;
    zip: string;
}

export interface CollectionsEmailModel {
    email_name: string;
    email_type: string;
    sent_date: string;
    recipients: RecipientDTO[];
    employee_id: number;
    attachments: AttachmentDTO[] | null;
    gid: string | null;
    loan_id: number;
    source: string | null;
    active: boolean;
    current: boolean;
    history: boolean;
    html: string | null;
    last_updated: string | null;
    start_date: string | null;
    frequency: string | null;
    installment: string | null;
    amount: string | null;
    option: string | null;
    payment_type: string;
    partial_payment: boolean;
    request_date: string | null;
    bank_id: string | null;
    payments: PaymentDTO[];
    strict: boolean;
}
