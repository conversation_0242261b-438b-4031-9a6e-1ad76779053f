import {RecipientDTO} from '@/models';
import {AttachmentDTO} from '@/models/attachmentDTO';

export interface OperatorStatisticModel {
    date: string;
    declined: number;
    bank: number;
    verification: number;
    loans: number;
    amount: number;
    approved_count: number;
    approved_amount: number;
    approved_count_refi: number;
    approved_amount_refi: number;
}

export interface OperatorStatisticChartModel extends OperatorStatisticModel {
    approved: number;
    prequal_approved_count: number;
    prequal_approved_amount: number;
}

export interface OperatorPaymentModel {
    employee_id: number;
    employee_name: string;
    schedule_date: string;
    schedule_type: string;
    payment_mode: string;
    employer_name: string | null;
    phone: string;
    email: string;
    loan_id: number;
    loan_balance: string | null;
    aba_number: string;
    account_number: string;
    payoff_amount: string;
    payment_amount: string;
    refund_amount: string | null;
    process_date: string | null;
    cleared_date: string | null;
    entity_class: string;
    entity_id: number;
    dlq_days: number | null;
    backup: boolean;
    duplicate: boolean;
}

export interface OperatorPaymentDetailsModel {
    employer_name: string;
    loan_balance: string;
    account_balance: string;
    aba_number: string;
    account_number: string;
    processing_fee: string;
}

export interface OperatorRefundModel {
    employee_id: number;
    employee_name: string;
    status: string;
    employer_name: string;
    phone: string;
    email: null | string;
    account_balance: null | string;
    loan_id: null | number;
    loan_balance: null | string;
    loan_appliable: boolean;
    refund_owed: string;
    tx_date: null | string;
    tx_type: null | string;
    tx_amount: null | string;
    bank_id: null | number;
    bank_confirm_request_date: null | string;
    bank_confirm_date: null | string;
    delinq_days: null | number;
    delinq_amount: null | number;
    max_delinq_days: null | number;
    max_delinq_amount: null | number;
    state: string;
    last_payment_date: string;
}

export interface OperatorRefundDetailsModel {
    employee_id: null | number;
    employee_name: null | string;
    status: null | string;
    employer_name: null | string;
    phone: null | string;
    email: null | string;
    account_balance: string;
    loan_id: null | number;
    loan_balance: string;
    loan_appliable: null | string;
    refund_owed: null | string;
    tx_date: string;
    tx_type: string;
    tx_amount: string;
    bank_id: null | number;
    bank_confirm_request_date: null | string;
    bank_confirm_date: null | string;
    delinq_days: number;
    delinq_amount: string;
    max_delinq_days: number;
    max_delinq_amount: string;
    state: null | string;
    last_payment_date: null | string;
}

export interface OperatorRefundEmailModel {
    email_name: string;
    email_type: string;
    sent_date: null | string;
    recipients: RecipientDTO[];
    attachments: AttachmentDTO[] | null;
    employee_id: number;
    bank_id: number;
}

export interface OperatorAgreementModel {
    loan_id: number;
    loan_date: string;
    loan_amount: string;
    application_id: number;
    pre_qualify: null | string;
    employer_id: number;
    employer_name: string;
    employer_mnemonic: string;
    employee_id: number;
    employee_name: string;
    employee_state: string;
    origination_source: string;
    approved: boolean;
    payroll_frequency: string;
    payroll_mode: string;
    payroll_day_of_week: string;
    comment: null | string;
    rules: null | string;
    queues: null | string;
    session_id: null | string;
    context: string;
}

export interface OperatorTransactionModel {
    gid: number;
    employee_id: number;
    transaction_type: string;
    loan_id: number | null;
    document_id: number | null;
    clearing_id: number | null;
    bank_id: number | null;
    voided: boolean;
    last_update_user: string | null;
    source: string | null;
    entry_date: string;
    effective_date: string;
    amount: string;
    loan_balance: string;
    account_balance: string;
    has_audit: boolean;
}

export interface OperatorTransactionAuditModel {
    gid: number;
    user_id: string;
    create_time: string;
}
