import {LoanStatusType, RecipientDTO} from '@/models';
import {AttachmentDTO} from '@/models/attachmentDTO';

export interface AccountsBankModel {
    employer_id: number;
    employer_name: string;
    aba_number: string;
    account_number: string;
}

export interface AccountsTransactionModel {
    gid: number;
    employer_id: number;
    entry_date: string;
    effective_date: string;
    intended_date: string;
    amount: string;
    transaction_type: string;
    batch_job_id: number | null;
    voided: boolean;
}

export interface AccountsDebitModel {
    gid: number | null;
    date: string;
    employer_id: number;
    employer_name: string;
    amount: string;
    debit_id: number;
    legal_name: string | null;
    aba_number: string;
    account_number: string;
    process_date: string | null;
    clearing_date: string | null;
    cleared: string | null;
    debit_type: string;
}

export interface AccountsDebitEmployerEmailModel {
    email_name: string;
    email_type: string;
    sent_date: string | null;
    recipients: RecipientDTO[];
}

export interface AccountsPaymentModel {
    date: string;
    employer_id: number;
    employer_name: string;
    amount: string;
    aba_number: string | null;
    account_number: string | null;
    type: string;
    payment_type: string;
    billed: boolean;
}

export interface AccountsPaymentDetailsModel {
    deduction_billed: string;
    deduction_received: string;
    pepm_billed: string;
    pepm_received: string;
    guarantee_billed: string;
    guarantee_received: string;
    refunds_disbursed: string;
    payments_disbursed: string;
    balance: string;
}

export interface AccountsClearingModel {
    gid: number;
    date: string;
    employer_id: number;
    employer_name: string;
    amount: string;
    debit_id: string | null;
    legal_name: string | null;
    aba_number: string | null;
    account_number: string | null;
    process_date: string;
    clearing_date: string;
    cleared: boolean;
    debit_type: string;
}

export interface AccountsCensusEmployerModel {
    gid: number;
    mnemonic: string;
    name: string;
    tax_id: number | null;
    state_of_incorporation: string | null;
    industry: string | null;
    street1: string | null;
    street2: string | null;
    city: string | null;
    state: string | null;
    zip: string | null;
    phone1: string | null;
    phone2: string | null;
    website: string | null;
    fax: string | null;
    primary_contact_id: number | null;
    backup_contact_id: number | null;
    hr_contact_id: number | null;
    payroll_contact_id: number | null;
    it_contact_id: number | null;
    active: string | null;
    start_date: string | null;
    primary_employer_id: number | null;
    primary_employer_name: null;
    primary_employer_mnemonic: null;
    workflow: string | null;
    deduction_mode: string;
    consolidated: boolean;
    automated_census: boolean | null;
    automated_reconciliation: boolean | null;
}

export interface AccountsCensusModel {
    employer_id: number;
    employer_name: string;
    employer_mnemonic: string;
    primary_client_id: number;
    primary_client_name: string;
    root_id: number;
    root_name: string;
    last_census_job_id: number | null;
    last_census_job_start_time: string | null;
    last_census_job_status: string | null;
    last_census_job_detail: string | null;
}

export interface AccountsCensusExpandedModel extends AccountsCensusModel {
    isOldCensus: boolean;
}

export interface AccountsCensusDetailsModel {
    name: string;
    required: boolean;
    headers: string[];
    values: string[] | null;
}

export interface AccountsPayrollEmployerGroupModel {
    frequency: string;
    mode: string;
    alias: string | null;
    last_payroll_run_date: string;
    next_payroll_run_date: string;
}

export interface AccountsPayrollEmployerModel {
    consolidate: boolean;
    last_payroll_run_date?: string;
    next_payroll_run_date?: string;
    groups?: {
        [key: string]: AccountsPayrollEmployerGroupModel;
    };
}

export interface AccountsDeductionEmailModel {
    email_name: string;
    email_type: string;
    sent_date: string | null;
    recipients: RecipientDTO[];
    attachments: AttachmentDTO[] | null;
    payroll_group_id: number;
    date: string;
}

export enum AccountsJobUploadService {
    Census = 'CensusUploadService',
    Payroll = 'PayrollUploadService',
    Deduction = 'DeductionUploadService',
    Deposit = 'DepositUploadService',
}

export interface AccountsJobLogModel {
    gid: number;
    job_type: AccountsJobUploadService;
    job_status: string;
    job_detail: string;
    job_context: string;
    job_start_time: string;
    job_end_time: string;
    job_document_id: number;
    job_user_id: number;
    job_user_name: string;
    employer_gid: number;
    employer_name: string;
    rollback: boolean;
    source: string | null;
}

export interface AccountsJobLogDetailModel {
    key: string;
    completed: boolean | null;
    message: string;
}

export interface AccountsEmployerPayrollDetailsPayrollDateModel {
    payroll_start: string;
    payroll_end: string;
    payroll_date: string;
    billing_date: string;
    active: boolean;
}

export interface AccountsEmployerPayrollDetailsModel {
    gid: null | number;
    employer_id: null | number;
    annual_periods: null | string;
    payroll_frequency: string;
    sample_date: string;
    sample_dates: null | string;
    deduction_lead_days: number;
    payroll_dates: AccountsEmployerPayrollDetailsPayrollDateModel[];
}

export interface AccountsEmployerSamplesModel {
    employer_id: number;
    mnemonic: string;
    payroll_group_id: number;
    payroll_frequency: string;
    payroll_mode: string;
    payroll_alias: string | null;
    employee_id: number;
    first_name: string;
    last_name: string;
    dob: string;
    doh: string;
    id_at_employer: string;
    email_at_employer: string | null;
    full_part_time: string;
    pay_type: string;
    pay_rate: number;
    address_state: string;
    address_zip: string;
    state_mode: string;
}

export interface AccountsDeductionModel {
    employee_id: number;
    first_name: string;
    last_name: string;
    id_at_employer: string;
    loan_id: number;
    loan_status: LoanStatusType;
    close_date: string;
    request_date: string | null;
}
