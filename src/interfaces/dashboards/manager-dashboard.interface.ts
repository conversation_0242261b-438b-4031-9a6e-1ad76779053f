import {google} from '@google-analytics/data/build/protos/protos';
type IGoogleAnalyticsRow = google.analytics.data.v1beta.IRow;

export type {IGoogleAnalyticsRow};

export interface CommunicationHistoryBucketModel {
    type: string | null;
    template: string | null;
    start_time: string;
    end_time: string;
    count: number;
}

export interface CommunicationHistoryModel {
    buckets: CommunicationHistoryBucketModel[];
}

export interface CommunicationHistoryChartDataModel {
    date: string;
    types: {
        [uniqType: string]: number;
    };
    template: {
        [uniqTemplate: string]: {
            [uniqType: string]: number;
        };
    };
    count: number;
}

export interface CollectionSettlementLoanModel {
    loan_id: number;
    employee_id: number;
    expiry_date: string;
    balance: number;
    curr_dlq_amount: number;
    curr_dlq_days: number;
    assigned_user: string | null;
    employee_first_name: string;
    employee_last_name: string;
    settlement_status: string;
    last_payment_date: string;
    last_payment_amount: number;
    settlement_amount: number;
    settlement_payments: number;
    total_payments: number;
}

export interface OutcomeCollectionStatisticsModel {
    transaction_date: string;
    ach_rcvd_count: number;
    ach_rcvd_total_amount: string | null;
    debit_rcvd_count: number;
    debit_rcvd_total_amount: string;
    checks_money_orders_rcvd_count: number;
    checks_money_orders_rcvd_total_amount: string;
    ach_return_count: number;
    ach_return_total_amount: string | null;
    checks_return_count: number;
    checks_return_total_amount: string | null;
    collection_rcvd_count: number;
    collection_rcvd_total_amount: string | null;
}

export interface OutcomeSettlementReceivableStatisticsModel {
    report_date: string | null;
    settlement_receivable_total_amount: string;
}

export interface OutcomeSettlementReceivedStatisticsModel {
    report_date: string | null;
    settlement_received_total_amount: number;
}

export interface OutcomeStatisticsModel {
    collection_stats: OutcomeCollectionStatisticsModel[];
    settlement_receivable_stats: OutcomeSettlementReceivableStatisticsModel[];
    settlement_received_stats: OutcomeSettlementReceivedStatisticsModel[];
}

export interface MergeEmployeesTableModel {
    gid: number;
    first_name: string;
    last_name: string;
    employer_name: string;
    primary_email: string;
    application_status: string;
    merge_id: number;
}
