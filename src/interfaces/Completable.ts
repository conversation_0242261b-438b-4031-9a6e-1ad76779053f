/**
 * 3 possible states
 * 1) has a value
 * 2) has an error
 * 3) pending state (neither of the above)
 *
 *  Example Usage
 *
 *  const SomeComponent = () => {
 *
 *       // initial state will have pending = true and no data
 *       const someData:Completable<SomeData> = useAppSelector(someDataSelector)
 *
 *       if(someData.pending){
 *          return <div>Loading</div>;
 *       } else if (someData.value){
 *          return <div>I have data</div>
 *       } else if (someData.error){
 *           return
 *       }
 *
 * }
 *
 */
export interface Completable<T> {
    value?: T;
    error?: string;

    code?: number;
    headers?: any;

    // not strictly necessary, but a convenience
    pending: boolean;
}

interface ResponseBase {
    code: number;
    headers: any;
}

export interface ApiError extends ResponseBase {
    message: string;
}

/**
 * Example usage :
 *
 * const fetchSomeData = () => Promise<Completable<SomeDTO>> = () => {
 *     return axios.get<KashableResponseDTO<SomeDTO>>('/some/data')
 *          .then(res => completed(res.data.data))
 * }
 *
 *

 */
export function completed<T>(t: T | undefined): Completable<T> {
    return {
        value: t,
        pending: false,
    };
}

export function pending<T>(): Completable<T> {
    return {
        pending: true,
    };
}

export function pristine<T>(): Completable<T> {
    return {
        pending: false,
    };
}

export function errored<T>(msg:string) : Completable<T> {
    return {
        pending: false,
        error: msg
    }
}

/**
 * Example usage : convertToApiError is some function that returns a { message:string, code:number }
 *
 * const fetchSomeData = () => Promise<Completable<SomeDTO>> = () => {
 *     return axios.get<KashableResponseDTO<SomeDTO>>('/some/data')
 *          .then(res => completed(res.data.data))
 *          .catch((error) => failed(convertToApiError(error)))
 * }
 *
 */
export function failed<T>(e: ApiError): Completable<T> {
    return {
        error: e.message,
        code: e.code,
        headers: e.headers,
        pending: false,
    };
}
