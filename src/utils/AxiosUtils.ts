import {Completable, failed} from '@/interfaces';
import {AxiosError, AxiosInstance, AxiosResponse} from 'axios';
import {SAVED_PATH_KEY} from '@/constants';
import {NextRequest, NextResponse} from 'next/server';

export function errored<T>(error: AxiosError<string>): Completable<T> {
    if (error.response) {
        const {response} = error;
        const {headers, status} = response;

        return failed({
            message: JSON.stringify(response.data),
            code: status,
            headers,
        });
    } else if (error.code === 'ECONNABORTED') {
        return failed({
            message: error.message,
            code: 408,
            headers: {},
        });
    } else {
        return failed({
            message: 'There was an error',
            code: 500,
            headers: {},
        });
    }
}

export function succeeded<T>(response: AxiosResponse<T>): Completable<T> {
    return mapSucceeded<T, T>(response, (d) => d);
}

export function mapSucceeded<T, U = T>(
    response: AxiosResponse<T>,
    mapper: (t: T) => U | undefined,
): Completable<U> {
    const {data, headers, status} = response;

    return {
        value: mapper(data),
        code: status,
        headers,
        pending: false,
    };
}

export async function apiRequest(input: string | URL | globalThis.Request, init?: RequestInit) {
    const response = await fetch(input, init);
    const result = await response.json();
    const isAuthError = response.status === 403 || response.status === 401;

    if (window.location.pathname !== '/login' && isAuthError) {
        localStorage.setItem(SAVED_PATH_KEY, window.location.pathname + window.location.hash);
        window.location.href = '/login';
        return {...result, error: 'Unauthorized: Redirecting to login'};
    } else {
        return result;
    }
}

export const mergeResponses = <T>(
    responseFirst: Completable<T[] | unknown>,
    responseSecond: Completable<T[] | unknown>,
): Completable<T[] | unknown> => {
    const mergedValues: T[] = [];
    const mergedResponse: Completable<T[]> = {
        ...responseFirst,
        value: [],
    };

    if (responseFirst.error) {
        return responseFirst;
    }

    if (responseSecond.error) {
        return responseSecond;
    }

    if (!responseFirst.error) {
        const values = responseFirst.value as T[];

        mergedValues.push(...values);
    }

    if (!responseSecond.error) {
        const values = responseSecond.value as T[];

        mergedValues.push(...values);
    }

    mergedResponse.value = mergedValues;

    return mergedResponse;
};

export const getParamValueAsBinary = (value: string | null) => (value === 'true' ? '1' : '0');

const buildUrl = (data: Record<string, string>) => {
    const path = data?.path || '';
    const paramsArr = Object.entries(data?.params || {});
    const paramsStr = paramsArr.map(([key, value]) => `${key}=${value}`).join('&');

    return `${path}${paramsStr ? '?' + paramsStr : ''}`;
};

const getBlobFromUrl = (axiosInstance: AxiosInstance, url: string): Promise<Completable<Blob | string>> => {
    return axiosInstance
        .get<Blob>(url, {responseType: 'arraybuffer'})
        .then(async (res) => mapSucceeded(res, (data) => data))
        .catch((error: AxiosError<string>) => errored(error));
};

export const handleBlobRoute = async (
    request: NextRequest,
    axiosInstance: AxiosInstance,
): Promise<NextResponse> => {
    const params = JSON.parse(request.nextUrl.searchParams.get('params') || '{}');
    const url = buildUrl(params);
    const response: Completable<Blob | string> = await getBlobFromUrl(axiosInstance, url);

    if (response.error) {
        return NextResponse.json(response, {status: response.code});
    }

    const headers = new Headers();
    Object.entries(response.headers || {}).forEach(([key, value]) => {
        headers.set(key, value as string);
    });

    return new NextResponse(response.value, {
        status: response.code,
        headers,
    });
};
