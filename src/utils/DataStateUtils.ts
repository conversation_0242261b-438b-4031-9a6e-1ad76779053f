import {DataStateInterface} from '@/interfaces';
import {Completable} from '@/interfaces';
import {DEFAULT_ERROR_MSG} from '@/constants';

export const getDefaultState = <T>(): DataStateInterface<T> => ({
    loading: false,
    error: '',
    data: null,
});

export const getLoadingState = <T>(value: DataStateInterface<T>): DataStateInterface<T> => ({
    ...value,
    loading: true,
});

export const getLoadedState = <T>(response: Completable<T>): DataStateInterface<T> => ({
    loading: false,
    error: response.error ? response.error || DEFAULT_ERROR_MSG : '',
    data: response.value || null,
});
