import {createSlice, PayloadAction, createAsyncThunk} from '@reduxjs/toolkit';
import {apiRequest} from '@/utils/AxiosUtils';
import {UnderwritingDTO, UnderwritingSearchDTO} from '@/models';
import {AppThunk, RootState} from '@/lib/store';
import {
    DEFAULT_ERROR_MSG,
    PROFILES_KEY,
    UNDERWRITING_EMPL_HASH,
    UNDERWRITING_LOAN_HASH,
    UNDERWRITING_USER_HASH,
} from '@/constants';
import {INDEPENDENT_SEARCH_PARAMS} from '@/screens/UnderwritingScreen/data';
import {SelectableSearchValueModel} from '@/interfaces';
import {prepareSearchValue} from '@/utils/SearchUtils';

interface UnderwritingState {
    isWelcomeScreen: boolean;
    searchValues: SelectableSearchValueModel[];
    searchResult: UnderwritingDTO[];
    searchLoading: boolean;
    searchError: string;
    profiles: UnderwritingSearchDTO[];
    activeProfile: UnderwritingSearchDTO | null;
    abortController: AbortController | null;
}

const initialState: UnderwritingState = {
    isWelcomeScreen: true,
    searchValues: [],
    searchResult: [],
    searchLoading: false,
    searchError: '',
    profiles: [],
    activeProfile: null,
    abortController: null,
};

const ABORT_ERROR = 'AbortError';
let currentAbortController: AbortController | null = null;

export const fetchAccounts = createAsyncThunk(
    'underwriting/fetch',
    async (values: SelectableSearchValueModel[], {rejectWithValue}) => {
        if (currentAbortController) {
            currentAbortController.abort();
        }

        currentAbortController = new AbortController();

        try {
            const query = values.map(({param, value}) => `${param}=${encodeURIComponent(value)}`).join('&');
            const response = await apiRequest(`/api/secured/underwriting/search?${query}`, {
                signal: currentAbortController.signal,
            });

            if (response.error) {
                return rejectWithValue(response);
            }

            return {response, searchValues: values};
        } catch (error: any) {
            if (error.name === ABORT_ERROR) {
                return rejectWithValue({error: ABORT_ERROR});
            }
            return rejectWithValue({error: DEFAULT_ERROR_MSG});
        }
    },
);

const updateLocalStorageProfiles = (profiles: UnderwritingSearchDTO[]) => {
    localStorage.setItem(PROFILES_KEY, JSON.stringify(profiles));
};

const underwritingSlice = createSlice({
    name: 'underwriting',
    initialState,
    reducers: {
        resetUnderwritingState: () => {
            localStorage.removeItem(PROFILES_KEY);
            return initialState;
        },
        addProfile: (state, action: PayloadAction<UnderwritingSearchDTO>) => {
            const existedProfile = state.profiles.find(({uid}) => uid === action.payload.uid);

            if (!existedProfile) {
                state.profiles.push(action.payload);
                updateLocalStorageProfiles(state.profiles);
            }
        },
        updateProfile: (state, action: PayloadAction<UnderwritingSearchDTO>) => {
            state.profiles = state.profiles.map((item) =>
                item.uid === action.payload.uid ? action.payload : item,
            );
            updateLocalStorageProfiles(state.profiles);
        },
        removeProfile: (state, action: PayloadAction<string>) => {
            if (state.activeProfile?.uid === action.payload) {
                state.activeProfile = null;
            }
            state.profiles = state.profiles.filter((profile) => profile.uid !== action.payload);
            updateLocalStorageProfiles(state.profiles);
        },
        clearProfiles: (state) => {
            state.profiles = [];
            state.activeProfile = null;
            updateLocalStorageProfiles(state.profiles);
        },
        setActiveProfile: (state, action: PayloadAction<UnderwritingSearchDTO | null>) => {
            state.activeProfile = action.payload;
        },
        resetSearch: (state) => {
            state.searchResult = [];
            state.searchValues = [];
        },
        cancelSearch: (state) => {
            if (currentAbortController) {
                currentAbortController.abort();
                currentAbortController = null;
            }
            state.searchLoading = false;
            state.searchResult = [];
            state.searchValues = [];
        },
        showProfileDetails: (state, action: PayloadAction<UnderwritingDTO>) => {
            const openedProfile = state.profiles.find(
                (item) =>
                    item.user_id === action.payload.user_id &&
                    item.employee_id === action.payload.employee_id,
            );

            if (!openedProfile) {
                const profileData: UnderwritingSearchDTO = {
                    uid: action.payload.uid,
                    application_status: action.payload.application_status,
                    employee_first_name: action.payload.employee_first_name,
                    employee_id: action.payload.employee_id,
                    employee_last_name: action.payload.employee_last_name,
                    open_bankruptcy_petition: action.payload.open_bankruptcy_petition,
                    user_first_name: action.payload.user_first_name,
                    user_id: action.payload.user_id,
                    user_last_name: action.payload.user_last_name,
                    current_loan_id: action.payload.current_loan_id,
                    employer_name: action.payload.employer_name,
                };

                state.activeProfile = profileData;
                state.profiles.push(profileData);
                updateLocalStorageProfiles(state.profiles);
            } else {
                state.activeProfile = openedProfile;
            }
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchAccounts.pending, (state) => {
                state.searchLoading = true;
                state.searchError = '';
            })
            .addCase(fetchAccounts.fulfilled, (state, action) => {
                state.searchLoading = false;
                state.searchError = '';
                state.searchResult = action.payload.response.value;
                state.searchValues = action.payload.searchValues;
                state.isWelcomeScreen = false;

                if (action.payload.response.value.length === 1) {
                    underwritingSlice.caseReducers.showProfileDetails(state, {
                        payload: action.payload.response.value[0],
                        type: 'showProfileDetails',
                    });
                }
            })
            .addCase(fetchAccounts.rejected, (state, action: PayloadAction<any>) => {
                state.searchLoading = false;
                state.searchError =
                    action.payload.error === ABORT_ERROR ? '' : action.payload.error || DEFAULT_ERROR_MSG;
                state.searchResult = [];
                state.searchValues = [];
                state.isWelcomeScreen = false;
            });
    },
});

export const {
    resetUnderwritingState,
    addProfile,
    updateProfile,
    removeProfile,
    clearProfiles,
    setActiveProfile,
    resetSearch,
    cancelSearch,
    showProfileDetails,
} = underwritingSlice.actions;

export default underwritingSlice.reducer;

export const selectUnderwritingState = (state: RootState) => state.underwriting;

export const handleSearch =
    (data: SelectableSearchValueModel): AppThunk =>
    async (dispatch, getState) => {
        const {searchValues} = getState().underwriting;
        const isIndependentSearchParam = INDEPENDENT_SEARCH_PARAMS.includes(data.param);

        if (isIndependentSearchParam) {
            return dispatch(fetchAccounts([data]));
        }

        const filteredParams = searchValues.filter((item) => !INDEPENDENT_SEARCH_PARAMS.includes(item.param));
        const existedParamIndex = filteredParams.findIndex(({param}) => param === data.param);

        if (existedParamIndex !== -1) {
            filteredParams[existedParamIndex] = data;
        } else {
            filteredParams.push(data);
        }

        return dispatch(fetchAccounts(filteredParams));
    };

export const handleSharedProfile =
    (profileType: string, profileId: string): AppThunk =>
    async (dispatch, getState) => {
        const {searchValues, profiles} = getState().underwriting;

        const profile = profiles.find((item) => {
            const id = Number(profileId);

            if (profileType === UNDERWRITING_EMPL_HASH) {
                return item.employee_id === id;
            }
            if (profileType === UNDERWRITING_USER_HASH) {
                return item.user_id === id;
            }
            if (profileType === UNDERWRITING_LOAN_HASH) {
                return item.current_loan_id === id;
            }
            return false;
        });

        if (profile) {
            dispatch(setActiveProfile(profile));
        } else {
            const valuesArr = [profileId];

            switch (profileType) {
                case UNDERWRITING_EMPL_HASH:
                    valuesArr.unshift('employee');
                    break;
                case UNDERWRITING_USER_HASH:
                    valuesArr.unshift('user');
                    break;
                case UNDERWRITING_LOAN_HASH:
                    valuesArr.unshift('loan');
                    break;
            }

            const value = valuesArr.join(':');
            const values = searchValues.filter(({text}) => text === value);

            if (!values.length) {
                const newSearchValue = prepareSearchValue(value);

                dispatch(resetSearch());
                dispatch(handleSearch(newSearchValue));
            }

            dispatch(setActiveProfile(null));
        }
    };

export const removeSearchValue =
    (data: SelectableSearchValueModel): AppThunk =>
    async (dispatch, getState) => {
        const {searchValues} = getState().underwriting;
        const values = searchValues.filter(({text}) => text !== data.text);

        if (values.length) {
            await dispatch(fetchAccounts(values));
        } else {
            dispatch(resetSearch());
        }
    };

export const updateSearch = (): AppThunk => async (dispatch, getState) => {
    const {searchValues} = getState().underwriting;
    await dispatch(fetchAccounts(searchValues));
};
