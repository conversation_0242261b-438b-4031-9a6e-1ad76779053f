import React, {createContext, useContext, useEffect, useMemo, useState} from 'react';
import {apiRequest} from '@/utils/AxiosUtils';
import {getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {
    VerificationQueueCountStateType,
    VerificationQueueItemType,
    VerificationQueueLookingUserModel,
    VerificationQueueOutdatedStateType,
    VerificationQueueParams,
    VerificationQueueParamsModel,
    VerificationQueueStateType,
    VerificationQueueSuccessActionModel,
    VerificationQueueTableStateType,
} from './interfaces';
import {
    DEFAULT_VERIFICATION_QUEUE_PARAMS,
    VERIFICATION_INITIAL_QUEUE_COUNT_STATE,
    VERIFICATION_INITIAL_QUEUE_OUT_OF_DATE_STATE,
    VERIFICATION_INITIAL_QUEUE_STATE,
    VERIFICATION_INITIAL_QUEUE_TABLE_STATE,
} from '@/screens/VerificationScreen/data';
import {VERIFICATION_HASH} from '@/constants';
import {Completable, VerificationQueueModel} from '@/interfaces';
import dayjs, {Dayjs} from 'dayjs';
import {usePusher} from '@/hooks/usePusher';
import {useAppSelector} from '@/lib/hooks';
import {selectUser} from '@/lib/slices/userSlice';
import {useHashHandler} from '@/hooks/useHashHandler';
import {VerificationSnackbar} from '@/screens/VerificationScreen/components';
import {getAdjacentUser} from '@/utils/VerificationUtils';

interface VerificationContextModel {
    selectedProfileId: number | null;
    queueParams: VerificationQueueParams;
    changeSelectedProfileId: (value: number | null) => void;
    activeQueue: VerificationQueueItemType;
    rawActiveQueue: VerificationQueueItemType;
    changeActiveQueue: (value: VerificationQueueItemType, clear?: boolean) => void;
    queueState: VerificationQueueStateType;
    loadQueue: (
        type?: VerificationQueueItemType,
        params?: VerificationQueueParamsModel,
    ) => Promise<Completable<VerificationQueueModel[]>>;
    queueCountState: VerificationQueueCountStateType;
    loadQueueCount: (type: VerificationQueueItemType, params?: VerificationQueueParamsModel) => Promise<void>;
    lookingUsers: VerificationQueueLookingUserModel[];
    addLookingUsers: (data: VerificationQueueLookingUserModel) => Promise<void>;
    queueTableState: VerificationQueueTableStateType;
    changeQueueTableState: (value: VerificationQueueModel[]) => void;
    queueOutdatedState: VerificationQueueOutdatedStateType;
    handleSuccessAction: (data: VerificationQueueSuccessActionModel) => void;
    prevUser: VerificationQueueModel | null;
    nextUser: VerificationQueueModel | null;
    profileLoading: boolean;
    setProfileLoading: (value: boolean) => void;
}

const VerificationContext = createContext<VerificationContextModel | undefined>(undefined);

const VERIFICATION_CHANNEL = 'kas-verification-dashboard-channel';
const VERIFICATION_USER_EVENT = 'kas-verification-user-entered';
const VERIFICATION_SUCCESS_ACTION_EVENT = 'kas-verification-success-action';
const ACTIVE_USER_THRESHOLD_MS = 15000;

export const VerificationProvider = ({children}: {children: React.ReactNode}) => {
    const {pusher, pushEvent, connectPusher, disconnectPusher} = usePusher();
    const {hashMatch, updateRoute} = useHashHandler();
    const user = useAppSelector(selectUser);
    const [queueParams, setQueueParams] = useState<VerificationQueueParams>(
        DEFAULT_VERIFICATION_QUEUE_PARAMS,
    );
    const [selectedProfileId, setSelectedProfileId] = useState<number | null>(null);
    const [activeQueue, setActiveQueue] = useState<VerificationQueueItemType>(
        VerificationQueueItemType.Income,
    );
    const [rawActiveQueue, setRawActiveQueue] = useState<VerificationQueueItemType>(
        VerificationQueueItemType.Income,
    );

    const [queueState, setQueueState] = useState(VERIFICATION_INITIAL_QUEUE_STATE);
    const [queueOutdatedState, setQueueOutdatedState] = useState(
        VERIFICATION_INITIAL_QUEUE_OUT_OF_DATE_STATE,
    );
    const [queueCountState, setQueueCountState] = useState(VERIFICATION_INITIAL_QUEUE_COUNT_STATE);
    const [queueTableState, setQueueTableState] = useState(VERIFICATION_INITIAL_QUEUE_TABLE_STATE);
    const [snackbarQueue, setSnackbarQueue] = useState<VerificationQueueSuccessActionModel[]>([]);
    const [profileLoading, setProfileLoading] = useState(false);

    const handleSnackbarClose = (id: string) => {
        setSnackbarQueue((prev) => prev.filter((snackbar) => snackbar.id !== id));
    };

    const updateSnackbarAlerts = (data: VerificationQueueSuccessActionModel) => {
        setSnackbarQueue((prev) => [data, ...prev]);
    };

    const changeQueueTableState = (value: VerificationQueueModel[]) => {
        setQueueTableState((prevState) => ({...prevState, [activeQueue]: [...value]}));
    };

    const loadQueue = async (
        type = activeQueue,
        params?: VerificationQueueParamsModel,
    ): Promise<Completable<VerificationQueueModel[]>> => {
        const curParams = params || queueParams[type];
        const urlParams = new URLSearchParams({
            verified: String(curParams.verified),
            denied: String(curParams.denied),
            followup: String(curParams.followup),
            docs: String(curParams.docs),
            lookup: curParams.lookup,
            ...(curParams.strict && {strict: String(curParams.strict)}),
        });
        const queue =
            type === VerificationQueueItemType.Bank && curParams.strict
                ? VerificationQueueItemType.Primary_Bank
                : type;
        const url = `/api/secured/verification/queue/${queue}?${urlParams.toString()}`;

        setQueueParams((prevState) => ({
            ...prevState,
            [type]: curParams,
        }));

        setQueueState((prevState) => ({
            ...prevState,
            [type]: getLoadingState(prevState[type]),
        }));

        const response = await apiRequest(url);

        setQueueState((prevState) => ({
            ...prevState,
            [type]: getLoadedState(response),
        }));

        setQueueOutdatedState((prevState) => ({
            ...prevState,
            [type]: [],
        }));

        return response;
    };

    const loadQueueCount = async (type: VerificationQueueItemType, params?: VerificationQueueParamsModel) => {
        const curParams = params || queueParams[type];
        const urlParams = new URLSearchParams({
            verified: String(curParams.verified),
            denied: String(curParams.denied),
            followup: String(curParams.followup),
            docs: String(curParams.docs),
            lookup: curParams.lookup,
        });
        const url = `/api/secured/verification/queue/${type}/count?${urlParams.toString()}`;

        setQueueCountState((prevState) => ({...prevState, [type]: getLoadingState(prevState[type])}));
        const response = await apiRequest(url);
        setQueueCountState((prevState) => ({...prevState, [type]: getLoadedState(response)}));
    };

    const changeActiveQueue = (value: VerificationQueueItemType, clearProfile: boolean = true) => {
        if (clearProfile) {
            changeSelectedProfileId(null);
        }

        updateQueue(value);
    };

    const changeSelectedProfileId = (id: number | null) => {
        setQueueOutdatedState((prevState) => ({
            ...prevState,
            [activeQueue]: [],
        }));
        if (id) {
            updateRoute({
                hash: VERIFICATION_HASH,
                type: activeQueue,
                value: id.toString(),
            });
        } else {
            updateRoute();
        }
    };

    const updateQueue = (code: VerificationQueueItemType) => {
        setRawActiveQueue(code);
        if (code === VerificationQueueItemType.Primary_Bank) {
            // alias for
            code = VerificationQueueItemType.Bank;
        }

        const isValidQueueItemType = Object.values(VerificationQueueItemType).includes(code);
        if (isValidQueueItemType) {
            setActiveQueue(code);
        }
    };

    const handleRouteChange = () => {
        let code = hashMatch.type as VerificationQueueItemType;
        updateQueue(code);

        const gid = Number(hashMatch.value) || null;
        setSelectedProfileId(gid);
    };

    useEffect(() => {
        handleRouteChange();
    }, [hashMatch]);

    const handleSuccessAction = (data: VerificationQueueSuccessActionModel) => {
        pushEvent<VerificationQueueSuccessActionModel>(
            VERIFICATION_CHANNEL,
            VERIFICATION_SUCCESS_ACTION_EVENT,
            data,
        ).then();
    };

    const [lookingUsers, setLookingUsers] = useState<VerificationQueueLookingUserModel[]>([]);

    const addLookingUsers = async (data: VerificationQueueLookingUserModel) => {
        await pushEvent<VerificationQueueLookingUserModel>(
            VERIFICATION_CHANNEL,
            VERIFICATION_USER_EVENT,
            data,
        );
    };

    const isActiveUser = (updatedTime: Dayjs) => {
        const now = dayjs();

        return now.diff(dayjs(updatedTime), 'milliseconds') < ACTIVE_USER_THRESHOLD_MS;
    };

    const updateLookingUsers = (data: VerificationQueueLookingUserModel) => {
        setLookingUsers((prevUsers) => {
            const activeUsers = prevUsers.filter(({updatedTime}) => isActiveUser(updatedTime));

            const existingUserIndex = activeUsers.findIndex(
                ({userId, queue, profileId}) =>
                    userId === data.userId && queue === data.queue && profileId === data.profileId,
            );

            if (existingUserIndex !== -1) {
                activeUsers[existingUserIndex] = data;
            } else {
                activeUsers.push(data);
            }

            return activeUsers;
        });
    };

    const changeLookingUsers = () => {
        setLookingUsers((prevUsers) => prevUsers.filter(({updatedTime}) => isActiveUser(updatedTime)));
    };

    const prevUser = useMemo(() => {
        return getAdjacentUser(queueTableState[activeQueue], selectedProfileId, 'prev');
    }, [queueTableState[activeQueue], selectedProfileId]);

    const nextUser = useMemo(() => {
        return getAdjacentUser(queueTableState[activeQueue], selectedProfileId, 'next');
    }, [queueTableState[activeQueue], selectedProfileId]);

    useEffect(() => {
        connectPusher();

        return () => {
            disconnectPusher();
        };
    }, []);

    useEffect(() => {
        if (pusher) {
            const channel = pusher.subscribe(VERIFICATION_CHANNEL);
            const interval = setInterval(() => {
                changeLookingUsers();
            }, ACTIVE_USER_THRESHOLD_MS);

            channel.bind(VERIFICATION_USER_EVENT, (data: VerificationQueueLookingUserModel) => {
                if (data.userId !== user.value?.gid) {
                    updateLookingUsers(data);
                }
            });

            channel.bind(VERIFICATION_SUCCESS_ACTION_EVENT, (data: VerificationQueueSuccessActionModel) => {
                if (data.userId !== user.value?.gid) {
                    updateSnackbarAlerts(data);
                }

                loadQueueCount(data.queue).then();

                setQueueOutdatedState((prevState) => ({
                    ...prevState,
                    [data.queue]: [...prevState[data.queue], data.message],
                }));
            });

            return () => {
                clearInterval(interval);
                channel.unbind_all();
                channel.unsubscribe();
            };
        }
    }, [pusher]);

    const value: VerificationContextModel = {
        selectedProfileId,
        queueParams,
        changeSelectedProfileId,
        activeQueue,
        rawActiveQueue,
        changeActiveQueue,
        queueState,
        loadQueue,
        queueCountState,
        loadQueueCount,
        lookingUsers,
        addLookingUsers,
        queueTableState,
        changeQueueTableState,
        queueOutdatedState,
        handleSuccessAction,
        prevUser,
        nextUser,
        profileLoading,
        setProfileLoading,
    };

    return (
        <VerificationContext.Provider value={value}>
            {children}
            <VerificationSnackbar data={snackbarQueue} handleClose={handleSnackbarClose} />
        </VerificationContext.Provider>
    );
};

export function useVerification() {
    const context = useContext(VerificationContext);
    if (!context) {
        throw new Error('useVerification must be used within VerificationProvider');
    }
    return context;
}
