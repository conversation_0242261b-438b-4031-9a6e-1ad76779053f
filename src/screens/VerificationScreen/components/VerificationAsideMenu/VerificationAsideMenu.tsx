import React, {useMemo} from 'react';
import {Button, Tab, Tabs, useMediaQuery, useTheme} from '@mui/material';
import {
    VerificationQueueCountStateType,
    VerificationQueueItemType,
    VerificationQueueParams,
} from '@/screens/VerificationScreen/interfaces';
import {useVerification} from '@/screens/VerificationScreen/useVerification';
import {VerificationAsideMenuItem} from './components';
import Box from '@mui/material/Box';

export const VerificationAsideMenu = () => {
    const theme = useTheme();
    const isSmallScreen = useMediaQuery(theme.breakpoints.down('lg'));
    const {
        activeQueue,
        changeActiveQueue,
        changeSelectedProfileId,
        queueParams,
        loadQueue,
        loadQueueCount,
        queueCountState,
    } = useVerification();

    const handleChange = (_event: React.SyntheticEvent, newValue: VerificationQueueItemType) => {
        changeActiveQueue(newValue);
    };

    const handleTabClick = (value: VerificationQueueItemType) => {
        if (value === activeQueue) {
            changeSelectedProfileId(null);
        }
    };

    const disableRefresh = useMemo(() => {
        let result = false;

        Object.keys(queueCountState).forEach((key) => {
            const value = queueCountState[key as keyof VerificationQueueCountStateType];

            if (value.loading) {
                result = true;
            }
        });
        return result;
    }, [queueCountState]);

    const onRefresh = async () => {
        loadQueue().then();

        Object.keys(queueParams).forEach((key) => {
            const value = queueParams[key as keyof VerificationQueueParams];

            loadQueueCount(key as VerificationQueueItemType, value).then();
        });
    };

    return (
        <Box py={2.5}>
            <Tabs
                orientation={isSmallScreen ? 'horizontal' : 'vertical'}
                variant='scrollable'
                scrollButtons={false}
                value={activeQueue}
                onChange={handleChange}
                sx={
                    !isSmallScreen
                        ? {
                              borderLeft: 1,
                              borderColor: 'divider',
                              '.MuiTabs-indicator': {
                                  left: 0,
                              },
                          }
                        : {}
                }>
                {Object.entries(VerificationQueueItemType)
                    .filter(([k, v], _) => v !== VerificationQueueItemType.Primary_Bank)
                    .map(([key, value]) => (
                        <Tab
                            key={key}
                            label={<VerificationAsideMenuItem item={value} />}
                            value={value}
                            onClick={() => handleTabClick(value)}
                            sx={{
                                '&:focus': {
                                    outline: 'none',
                                },
                            }}
                        />
                    ))}
            </Tabs>
            <Box pt={2}>
                <Button variant='outlined' disabled={disableRefresh} onClick={onRefresh}>
                    REFRESH QUEUES
                </Button>
            </Box>
        </Box>
    );
};
