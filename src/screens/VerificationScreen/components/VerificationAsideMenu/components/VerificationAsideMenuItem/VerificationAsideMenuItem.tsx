import './styles.scss';

import React, {useEffect, useMemo} from 'react';
import {VerificationQueueItemType} from '@/screens/VerificationScreen/interfaces';
import {useVerification} from '@/screens/VerificationScreen/useVerification';
import {Badge, Chip, Skeleton, Stack, Tooltip} from '@mui/material';
import Box from '@mui/material/Box';
import {InfoOutlined, Refresh} from '@mui/icons-material';
import {KasSwitch, KasSwitchWhen} from '@/components';
import IconButton from '@mui/material/IconButton';
import {uniqElements} from '@/utils/ArrayUtils';
import {getQueueTitle} from '@/utils/VerificationUtils';

const QUEUE_WARNING_THRESHOLD = 10;

export const VerificationAsideMenuItem = ({item}: {item: VerificationQueueItemType}) => {
    const {queueState, loadQueueCount, queueCountState, lookingUsers} = useVerification();

    const countLookingUsers = useMemo(() => {
        const activeQueueLookingUsers = lookingUsers.filter(({queue}) => queue === item);
        const uniqueKeys = uniqElements(activeQueueLookingUsers, ({userId}) => userId);

        return uniqueKeys.map((key) => {
            return activeQueueLookingUsers.find(({userId}) => userId === key)!;
        }).length;
    }, [lookingUsers, item]);

    const totalQueueItemCount = useMemo(() => {
        return queueState[item].data?.length || queueCountState[item].data?.available_count || 0;
    }, [queueCountState, queueState[item]]);

    useEffect(() => {
        loadQueueCount(item).then();
    }, []);

    return (
        <div className='kas-verification-aside-menu-item'>
            {getQueueTitle(item)}
            <Box ml={1}>
                <KasSwitch>
                    <KasSwitchWhen condition={queueCountState[item].loading && !queueCountState[item].data}>
                        <Skeleton variant='rounded' animation='wave' width={40} height={24} />
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={!!queueCountState[item].error}>
                        <Stack flexDirection='row' columnGap={1} alignItems='center'>
                            <Tooltip title={queueCountState[item].error}>
                                <InfoOutlined fontSize='small' color='error' />
                            </Tooltip>
                            <IconButton
                                title={'Refresh'}
                                size='small'
                                style={{minHeight: '20px', marginTop: '-10px', marginBottom: '-10px'}}
                                onMouseDown={(event) => {
                                    event.stopPropagation();
                                }}
                                onClick={() => loadQueueCount(item)}>
                                <Refresh fontSize='small' color='action' />
                            </IconButton>
                        </Stack>
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={!!queueCountState[item].data}>
                        <Badge sx={{position: 'relative'}} badgeContent={countLookingUsers} color='info'>
                            {queueCountState[item].loading && (
                                <Skeleton
                                    className='kas-verification-aside-menu-item__loading'
                                    variant='rounded'
                                    animation='wave'
                                />
                            )}
                            <Chip
                                label={`${totalQueueItemCount}/${queueCountState[item].data?.total_count}`}
                                size='small'
                                color={totalQueueItemCount < QUEUE_WARNING_THRESHOLD ? 'secondary' : 'error'}
                                variant={
                                    totalQueueItemCount < QUEUE_WARNING_THRESHOLD ? 'filled' : 'outlined'
                                }
                                sx={{fontSize: 14, borderColor: 'transparent'}}
                            />
                        </Badge>
                    </KasSwitchWhen>
                </KasSwitch>
            </Box>
        </div>
    );
};
