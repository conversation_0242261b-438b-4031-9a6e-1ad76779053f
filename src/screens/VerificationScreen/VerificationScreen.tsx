import React from 'react';
import {
    VerificationAsideMenu,
    VerificationContent,
    VerificationHead,
} from '@/screens/VerificationScreen/components';
import {VerificationProvider} from '@/screens/VerificationScreen/useVerification';
import {Grid2} from '@mui/material';

export const VerificationScreen = () => {
    return (
        <VerificationProvider>
            <VerificationHead />
            <Grid2 container sx={{position: 'relative'}}>
                <Grid2 size={{xs: 12, lg: 2}}>
                    <VerificationAsideMenu />
                </Grid2>
                <Grid2 size={{xs: 12, lg: 10}}>
                    <VerificationContent />
                </Grid2>
            </Grid2>
        </VerificationProvider>
    );
};
