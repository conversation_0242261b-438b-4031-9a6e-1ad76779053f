import React from 'react';
import {Tab, Tabs} from '@mui/material';
import {useAccounts} from './../../useAccounts';
import {AccountItemType} from './../../interfaces';

export const AccountsMenu = () => {
    const {activeMenu, changeActiveMenu} = useAccounts();

    const handleChange = (_event: React.SyntheticEvent, newValue: AccountItemType) => {
        changeActiveMenu(newValue);
    };

    return (
        <Tabs variant='scrollable' value={activeMenu} onChange={handleChange}>
            {Object.entries(AccountItemType).map(([key, value]) => (
                <Tab key={key} label={value} value={value} />
            ))}
        </Tabs>
    );
};
