import React, {useState} from 'react';
import {useFormik} from 'formik';
import {Alert, Grid2} from '@mui/material';
import {KasModalFooter} from '@/components';
import {useSnackbar} from '@/hooks/useSnackbar';
import {useAccountsClearing} from '@/screens/AccountsScreen/components/AccountsContent/components';
import {apiRequest} from '@/utils/AxiosUtils';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {AccountsClearingModel} from '@/interfaces';

export const CancelDebit = ({data}: {data: AccountsClearingModel}) => {
    const {showMessage} = useSnackbar();
    const {setOpenModal, loadClearings} = useAccountsClearing();
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async () => {
        const url = `/api/secured/accounts/clearing/employer`;
        const payload = {entity_id: data.gid};

        setSubmitting(true);

        const response = await apiRequest(url, {
            method: 'delete',
            body: JSON.stringify(payload),
        });

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            setOpenModal(null);
            await loadClearings();
        }

        setSubmitting(false);
    };

    const formik = useFormik({
        initialValues: {},
        onSubmit,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2}>
                <Grid2 size={12}>
                    <Alert severity='error'>
                        Are you sure you want to cancel Debit for <strong>{data.employer_name}</strong>?
                    </Alert>
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        disabled={submitting}
                        submitText='OK'
                        onCancel={() => setOpenModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
