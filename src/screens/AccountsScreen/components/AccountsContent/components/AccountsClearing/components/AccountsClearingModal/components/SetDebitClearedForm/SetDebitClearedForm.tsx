import React, {useState} from 'react';
import {KasDate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, KasModalFooter} from '@/components';
import {TextField, Grid2, InputAdornment} from '@mui/material';
import {useFormik} from 'formik';
import {getValidationSchema} from './schema';
import {useSnackbar} from '@/hooks/useSnackbar';
import {apiRequest} from '@/utils/AxiosUtils';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {
    ClearingTypesSelect,
    useAccountsClearing,
} from '@/screens/AccountsScreen/components/AccountsContent/components';
import dayjs from 'dayjs';
import {AccountsClearingModel} from '@/interfaces';

interface SetDebitClearedFormValues {
    effectiveDate: string;
    clearingType: string;
    amount: string;
}

export const SetDebitClearedForm = ({data}: {data: AccountsClearingModel}) => {
    const {showMessage} = useSnackbar();
    const {setOpenModal, loadClearings} = useAccountsClearing();
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async (values: SetDebitClearedFormValues) => {
        const url = `/api/secured/accounts/clearing/employer`;
        const payload = {
            action_type_txt: values.amount,
            effective_date: dayjs(values.effectiveDate).format('YYYYMMDD'),
            action_type: values.clearingType,
            entity_id: data.gid,
        };

        setSubmitting(true);

        const response = await apiRequest(url, {
            method: 'put',
            body: JSON.stringify(payload),
        });

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            setOpenModal(null);
            await loadClearings();
        }

        setSubmitting(false);
    };

    const defaultType = () => {
        switch (data.debit_type) {
            case 'DEDUCTION':
                return 'Payroll Deduction Payment Received';
            case 'PEPM':
                return 'PEPM Received';
            case 'GUARANTEE':
            default:
                return 'Guarantee Received';
        }
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            amount: data.amount || '',
            effectiveDate: dayjs().format('YYYYMMDD'),
            clearingType: defaultType(),
        },
        onSubmit,
        validationSchema: getValidationSchema(data.amount),
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={12}>
                    <ClearingTypesSelect formik={formik} disabled={submitting} />
                </Grid2>
                <Grid2 size={6}>
                    <KasDatePickerFormField
                        formik={formik}
                        name='effectiveDate'
                        label='Effective Date'
                        disabled={submitting}
                    />
                </Grid2>
                <Grid2 size={6}>
                    <TextField
                        fullWidth
                        size='small'
                        name='amount'
                        value={formik.values.amount}
                        disabled={submitting}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Amount'
                        variant='outlined'
                        type='number'
                        slotProps={{
                            input: {
                                startAdornment: <InputAdornment position='start'>$</InputAdornment>,
                            },
                            htmlInput: {
                                step: '0.01',
                            },
                        }}
                        error={!!formik.errors.amount && formik.touched.amount}
                        helperText={formik.touched.amount && formik.errors.amount}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        loading={submitting}
                        disabled={!formik.isValid || submitting}
                        onCancel={() => setOpenModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
