import React, {useMemo} from 'react';
import {KasModal} from '@/components';
import {CancelDebit, SetDebitClearedForm} from './components';
import {useAccountsClearing} from '@/screens/AccountsScreen/components/AccountsContent/components';
import {AccountsClearingItemModal} from './../../interfaces';

export const AccountsClearingModal = () => {
    const {openModal, setOpenModal} = useAccountsClearing();

    const renderActionForm = useMemo(() => {
        switch (openModal?.type) {
            case AccountsClearingItemModal.Set_Debit_Cleared:
                return <SetDebitClearedForm {...openModal.props} />;
            case AccountsClearingItemModal.Cancel_Debit:
                return <CancelDebit {...openModal.props} />;
            default:
                return null;
        }
    }, [openModal?.type]);

    return (
        <KasModal
            title={openModal?.type || 'Action Modal'}
            open={!!openModal}
            size='small'
            onClose={() => setOpenModal(null)}>
            {renderActionForm}
        </KasModal>
    );
};
