import React from 'react';
import {Divider, Paper} from '@mui/material';
import Box from '@mui/material/Box';
import {AccountsClearingHead, AccountsClearingModal, AccountsClearingResult} from './components';
import {useAccountsClearing} from './useAccountsClearing';
import {KasLoadingBackDrop, KasLoadingError, KasSwitch, KasSwitchWhen} from '@/components';
import {NoResultsView} from '@/views';
import {DEFAULT_ERROR_MSG} from '@/constants';

export const AccountsClearing = () => {
    const {clearingState} = useAccountsClearing();

    return (
        <Paper elevation={0}>
            <Box p={2}>
                <AccountsClearingHead />
                <Divider />
                <Box position='relative' pt={2}>
                    {clearingState.loading && <KasLoadingBackDrop />}
                    <KasSwitch>
                        <KasSwitchWhen condition={!!clearingState.error}>
                            <KasLoadingError
                                view='contained'
                                error={clearingState.error || DEFAULT_ERROR_MSG}
                            />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={!clearingState.data}>
                            <NoResultsView text='Search result will appear here' />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={!!clearingState.data}>
                            <AccountsClearingResult />
                        </KasSwitchWhen>
                    </KasSwitch>
                </Box>
            </Box>
            <AccountsClearingModal />
        </Paper>
    );
};
