import {AccountsClearingModel} from '@/interfaces';

export enum AccountsClearingItemModal {
    Set_Debit_Cleared = 'Select Employer Payment Type',
    Cancel_Debit = 'Cancel Debit',
}

export type AccountsClearingModalProps =
    | {
          type: AccountsClearingItemModal.Set_Debit_Cleared;
          props: {data: AccountsClearingModel};
      }
    | {
          type: AccountsClearingItemModal.Cancel_Debit;
          props: {data: AccountsClearingModel};
      };
