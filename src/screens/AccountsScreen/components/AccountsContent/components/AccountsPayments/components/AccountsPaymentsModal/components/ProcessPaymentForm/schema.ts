import * as Yup from 'yup';
import {DEFAULT_VALIDATION_MSG} from '@/constants';

export const getValidationSchema = (maxAmount: string) => {
    const max = parseFloat(maxAmount);

    return Yup.object().shape({
        date: Yup.string().required(DEFAULT_VALIDATION_MSG),
        clearingType: Yup.string().required(DEFAULT_VALIDATION_MSG),
        amount: Yup.number()
            .typeError('Must be a valid number')
            .required(DEFAULT_VALIDATION_MSG)
            .positive('Must be greater than zero')
            .max(isNaN(max) ? Infinity : max, `Payment cannot be larger that Receivable: $${maxAmount}`),
    });
};
