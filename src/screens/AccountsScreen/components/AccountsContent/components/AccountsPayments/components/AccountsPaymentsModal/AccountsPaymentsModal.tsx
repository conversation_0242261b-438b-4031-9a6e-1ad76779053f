import React, {useMemo} from 'react';
import {KasModal} from '@/components';
import {
    CreateEmployerTransactionForm,
    useAccountsPayments,
} from '@/screens/AccountsScreen/components/AccountsContent/components';
import {AccountsPaymentsItemModal} from './../../interfaces';
import {ProcessPaymentForm} from './components';

export const AccountsPaymentsModal = () => {
    const {openModal, setOpenModal, loadPayments} = useAccountsPayments();

    const onSuccessAddedEmployerTransaction = async () => {
        setOpenModal(null);
        await loadPayments();
    };

    const renderActionForm = useMemo(() => {
        switch (openModal?.type) {
            case AccountsPaymentsItemModal.Add_Guarantee:
            case AccountsPaymentsItemModal.Add_PEPM:
                return (
                    <CreateEmployerTransactionForm
                        {...openModal.props}
                        onSuccess={onSuccessAddedEmployerTransaction}
                        onClose={() => setOpenModal(null)}
                    />
                );
            case AccountsPaymentsItemModal.Process_Payment:
                return <ProcessPaymentForm {...openModal.props} />;
            default:
                return null;
        }
    }, [openModal?.type]);

    return (
        <KasModal
            title={openModal?.type || 'Action Modal'}
            open={!!openModal}
            size='small'
            onClose={() => setOpenModal(null)}>
            {renderActionForm}
        </KasModal>
    );
};
