import React, {createContext, useContext, useState} from 'react';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {DataStateInterface} from '@/interfaces';
import {AccountsBankModel} from '@/interfaces/dashboards';
import {AccountsBanksModalProps} from '@/screens/AccountsScreen/components/AccountsContent/components/AccountsBanks/interfaces';

interface AccountsBanksContextModel {
    banksState: DataStateInterface<AccountsBankModel[]>;
    loadBanks: () => Promise<void>;
    openModal: AccountsBanksModalProps | null;
    setOpenModal: (value: AccountsBanksModalProps | null) => void;
}

const AccountsBanksContext = createContext<AccountsBanksContextModel | undefined>(undefined);

export const AccountsBanksProvider = ({children}: {children: React.ReactNode}) => {
    const [banksState, setBanksState] = useState(getDefaultState<AccountsBankModel[]>);
    const [openModal, setOpenModal] = useState<AccountsBanksModalProps | null>(null);

    const loadBanks = async () => {
        const url = `/api/secured/accounts/banks`;

        setBanksState(getLoadingState(banksState));
        const response = await apiRequest(url);
        setBanksState(getLoadedState(response));
    };

    const value: AccountsBanksContextModel = {
        banksState,
        loadBanks,
        openModal,
        setOpenModal,
    };

    return <AccountsBanksContext.Provider value={value}>{children}</AccountsBanksContext.Provider>;
};

export function useAccountsBanks() {
    const context = useContext(AccountsBanksContext);
    if (!context) {
        throw new Error('useAccountsBanks must be used within AccountsBanksProvider');
    }
    return context;
}
