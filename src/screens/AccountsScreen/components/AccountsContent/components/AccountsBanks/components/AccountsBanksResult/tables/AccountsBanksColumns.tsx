import {CellContext} from '@tanstack/react-table';
import {defaultInfoColumn} from '@/utils/TableUtils';
import {AccountsBankModel} from '@/interfaces';
import {AccountsBankActionCell} from './../components';

const _defaultInfoColumn = defaultInfoColumn<AccountsBankModel>;

export const AccountsBanksColumns = [
    _defaultInfoColumn('employer_name', 'Employer Name'),
    _defaultInfoColumn('aba_number', 'ABA Number'),
    _defaultInfoColumn('account_number', 'Account Number'),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<AccountsBankModel, string>) => {
            return <AccountsBankActionCell data={props.row.original} />;
        },
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
];
