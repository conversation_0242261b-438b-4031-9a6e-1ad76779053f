import React from 'react';
import {Edit} from '@mui/icons-material';
import {ActionCell} from '@/components/table/cells';
import {AccountsBankModel} from '@/interfaces';
import {useAccountsBanks} from '@/screens/AccountsScreen/components/AccountsContent/components';
import {AccountsBanksItemModal} from '@/screens/AccountsScreen/components/AccountsContent/components/AccountsBanks/interfaces';

export const AccountsBankActionCell = ({data}: {data: AccountsBankModel}) => {
    const {setOpenModal} = useAccountsBanks();

    return (
        <ActionCell
            Icon={<Edit />}
            onClick={() => setOpenModal({type: AccountsBanksItemModal.Edit_Employer_Bank, props: {data}})}
        />
    );
};
