import React, {useMemo} from 'react';
import {KasModal} from '@/components';
import {EditEmployerBankForm} from './components';
import {useAccountsBanks} from '@/screens/AccountsScreen/components/AccountsContent/components';
import {AccountsBanksItemModal} from '../../interfaces';

export const AccountsBanksModal = () => {
    const {openModal, setOpenModal} = useAccountsBanks();

    const renderActionForm = useMemo(() => {
        switch (openModal?.type) {
            case AccountsBanksItemModal.Edit_Employer_Bank:
                return <EditEmployerBankForm {...openModal?.props} />;
            default:
                return null;
        }
    }, [openModal?.type]);

    return (
        <KasModal
            title={openModal?.type || 'Action Modal'}
            open={!!openModal}
            onClose={() => setOpenModal(null)}>
            {renderActionForm}
        </KasModal>
    );
};
