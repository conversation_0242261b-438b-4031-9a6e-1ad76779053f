import React from 'react';
import {ColumnDef} from '@tanstack/react-table';
import {AccountsBanksColumns} from './tables';
import {TableView} from '@/views';
import {useAccountsBanks} from '@/screens/AccountsScreen/components/AccountsContent/components';
import {AccountsBankModel} from '@/interfaces';

export const AccountsBanksResult = () => {
    const {banksState} = useAccountsBanks();

    return (
        <TableView<AccountsBankModel>
            withTableActions
            columns={AccountsBanksColumns as ColumnDef<AccountsBankModel, unknown>[]}
            loading={banksState.loading}
            error={banksState.error}
            data={banksState.data}
            tableName='Accounts Banks'
            sortingColumns={[{id: 'aba_number', desc: true}]}
        />
    );
};
