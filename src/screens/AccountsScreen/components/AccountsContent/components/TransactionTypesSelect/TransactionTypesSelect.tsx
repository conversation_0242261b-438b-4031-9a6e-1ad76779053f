import React, {useEffect} from 'react';
import {KasAuto<PERSON>ple<PERSON><PERSON>ield, KasSwitch, KasSwitchWhen} from '@/components';
import {FormikValues} from 'formik';
import {Chip} from '@mui/material';
import {Refresh} from '@mui/icons-material';
import {useAccounts} from '@/screens/AccountsScreen/useAccounts';

interface EmployerSelectProps {
    formik: FormikValues;
    disabled?: boolean;
}

export const TransactionTypesSelect = ({formik, disabled = false}: EmployerSelectProps) => {
    const {
        transactionTypesState: {loading, error, data},
        loadTransactionTypes,
    } = useAccounts();

    useEffect(() => {
        if (!data && !loading && !error) {
            loadTransactionTypes().then();
        }
    }, []);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={!!error}>
                <Chip
                    label='Loading Error!'
                    variant='outlined'
                    color='error'
                    sx={{padding: 0, width: '100%'}}
                    onDelete={loadTransactionTypes}
                    deleteIcon={<Refresh titleAccess='Try Again' />}
                />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!data || loading}>
                <KasAutocompleteField
                    loading={loading}
                    disabled={disabled}
                    name='transactionType'
                    label='Transaction Type'
                    options={data || []}
                    formik={formik}
                />
            </KasSwitchWhen>
        </KasSwitch>
    );
};
