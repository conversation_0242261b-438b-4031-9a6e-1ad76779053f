import React, {useState} from 'react';
import {KasDate<PERSON><PERSON><PERSON><PERSON><PERSON>ield, KasModalFooter} from '@/components';
import {TextField, Grid2, InputAdornment} from '@mui/material';
import {useFormik} from 'formik';
import {EditEmployerTransactionFormValues, validationSchema} from './schema';
import {useSnackbar} from '@/hooks/useSnackbar';
import {apiRequest} from '@/utils/AxiosUtils';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {
    TransactionTypesSelect,
    useAccountsTransactions,
} from '@/screens/AccountsScreen/components/AccountsContent/components';
import dayjs from 'dayjs';
import {AccountsTransactionModel} from '@/interfaces';

export const EditEmployerTransactionForm = ({data}: {data: AccountsTransactionModel}) => {
    const {showMessage} = useSnackbar();
    const {setOpenModal, loadTransactions} = useAccountsTransactions();
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async (values: EditEmployerTransactionFormValues) => {
        const url = `/api/secured/accounts/employer/${data.employer_id}/transaction/${data.gid}`;
        const payload = {
            gid: data.gid,
            employer_id: data.employer_id,
            amount: values.amount,
            effective_date: dayjs(values.effectiveDate).format('YYYYMMDD'),
            intended_date: dayjs(values.intendedDate).format('YYYYMMDD'),
            transaction_type: values.transactionType,
        };

        setSubmitting(true);

        const response = await apiRequest(url, {
            method: 'put',
            body: JSON.stringify(payload),
        });

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            setOpenModal(null);
            await loadTransactions(data.employer_id);
        }

        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            amount: data.amount || '',
            effectiveDate: data.effective_date || '',
            intendedDate: data.intended_date || '',
            transactionType: data.transaction_type || '',
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={12}>
                    <TransactionTypesSelect formik={formik} disabled={submitting} />
                </Grid2>
                <Grid2 size={4}>
                    <KasDatePickerFormField
                        formik={formik}
                        name='effectiveDate'
                        label='Effective Date'
                        disabled={submitting}
                    />
                </Grid2>
                <Grid2 size={4}>
                    <KasDatePickerFormField
                        formik={formik}
                        name='intendedDate'
                        label='Intended Date'
                        disabled={submitting}
                    />
                </Grid2>
                <Grid2 size={4}>
                    <TextField
                        fullWidth
                        size='small'
                        name='amount'
                        value={formik.values.amount}
                        disabled={submitting}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Amount'
                        variant='outlined'
                        type='number'
                        slotProps={{
                            input: {
                                startAdornment: <InputAdornment position='start'>$</InputAdornment>,
                            },
                            htmlInput: {
                                step: '0.01',
                            },
                        }}
                        error={!!formik.errors.amount && formik.touched.amount}
                        helperText={formik.touched.amount && formik.errors.amount}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        loading={submitting}
                        disabled={!formik.isValid || submitting}
                        onCancel={() => setOpenModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
