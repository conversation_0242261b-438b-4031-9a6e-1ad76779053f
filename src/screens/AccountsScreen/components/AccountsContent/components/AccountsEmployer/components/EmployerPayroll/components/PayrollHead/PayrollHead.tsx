import React from 'react';
import {Grid2, Typography} from '@mui/material';
import {useAccountsEmployer} from './../../../../useAccountsEmployer';
import {useFormik} from 'formik';
import {PayrollFilter} from './components';
import {PayrollEmployerSelect} from './../../../../components';

export const PayrollHead = () => {
    const {payrollDetailsState} = useAccountsEmployer();

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            employerId: '',
        },
        onSubmit: () => {},
    });

    return (
        <Grid2 container spacing={2} mb={2}>
            <Grid2 size={2.5}>
                <Typography variant='h6' py={1}>
                    Sample Payroll
                </Typography>
            </Grid2>
            <Grid2 size={2.5}>
                <form onSubmit={formik.handleSubmit}>
                    <PayrollEmployerSelect
                        formik={formik}
                        disabled={payrollDetailsState.loading}
                        showValidation={false}
                    />
                </form>
            </Grid2>
            <Grid2 size={12}>
                <PayrollFilter employerId={formik.values.employerId} />
            </Grid2>
        </Grid2>
    );
};
