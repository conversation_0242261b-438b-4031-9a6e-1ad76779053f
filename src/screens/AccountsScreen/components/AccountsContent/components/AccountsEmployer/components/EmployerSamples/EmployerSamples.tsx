import React from 'react';
import {Divider, Paper} from '@mui/material';
import Box from '@mui/material/Box';
import {SamplesHead, SamplesResult} from './components';
import {useAccountsEmployer} from '../../useAccountsEmployer';
import {Kas<PERSON><PERSON><PERSON><PERSON>ackD<PERSON>, KasLoading<PERSON><PERSON><PERSON>, KasSwitch, KasSwitchWhen} from '@/components';
import {NoResultsView} from '@/views';
import {DEFAULT_ERROR_MSG} from '@/constants';

export const EmployerSamples = () => {
    const {
        samplesState: {loading, data, error},
    } = useAccountsEmployer();

    return (
        <Paper elevation={0}>
            <Box p={2}>
                <SamplesHead />
                <Divider />
                <Box position='relative' pt={2}>
                    {loading && <KasLoadingBackDrop />}
                    <KasSwitch>
                        <KasSwitchWhen condition={!!error}>
                            <KasLoadingError view='contained' error={error || DEFAULT_ERROR_MSG} />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={!data}>
                            <NoResultsView text='Search result will appear here' />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={!!data}>
                            <SamplesResult />
                        </KasSwitchWhen>
                    </KasSwitch>
                </Box>
            </Box>
        </Paper>
    );
};
