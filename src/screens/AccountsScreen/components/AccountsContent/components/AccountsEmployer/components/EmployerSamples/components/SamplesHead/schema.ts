import * as Yup from 'yup';
import {DEFAULT_VALIDATION_MSG} from '@/constants';

export const validationSchema = Yup.object().shape({
    employerId: Yup.string().required(DEFAULT_VALIDATION_MSG),
    count: Yup.number()
        .typeError('Must be a valid number')
        .required(DEFAULT_VALIDATION_MSG)
        .positive('Must be greater than zero')
        .max(100, 'Must be less than or equal 100'),
});

export type SamplesHeadValues = Yup.Asserts<typeof validationSchema>;
