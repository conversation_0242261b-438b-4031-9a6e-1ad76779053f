import React, {createContext, useContext, useState} from 'react';
import {AccountsCensusModel, DataStateInterface} from '@/interfaces';
import {AccountsFileExchangeParamsModel} from '@/screens/AccountsScreen/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';

interface AccountsFileExchangeContextModel {
    filesState: DataStateInterface<AccountsCensusModel[]>;
    loadFiles: (params?: AccountsFileExchangeParamsModel) => Promise<void>;
}

const AccountsFileExchangeContext = createContext<AccountsFileExchangeContextModel | undefined>(undefined);

export const AccountsFileExchangeProvider = ({children}: {children: React.ReactNode}) => {
    const [filesState, setFilesState] = useState(getDefaultState<AccountsCensusModel[]>);
    const [filterParams, setFilterParams] = useState<AccountsFileExchangeParamsModel>({assigned: true});

    const loadFiles = async (params?: AccountsFileExchangeParamsModel) => {
        const curParams = params || filterParams;
        const employerId = curParams.employerId;
        const urlParams = new URLSearchParams({
            assigned: String(curParams.assigned),
            ...(employerId && {employerId}),
        });
        const url = `/api/secured/accounts/file-exchange?${urlParams.toString()}`;

        setFilterParams(curParams);
        setFilesState(getLoadingState(filesState));
        const response = await apiRequest(url);
        setFilesState(getLoadedState(response));
    };

    const value: AccountsFileExchangeContextModel = {
        filesState,
        loadFiles,
    };

    return (
        <AccountsFileExchangeContext.Provider value={value}>{children}</AccountsFileExchangeContext.Provider>
    );
};

export function useAccountsFileExchange() {
    const context = useContext(AccountsFileExchangeContext);
    if (!context) {
        throw new Error('useAccountsFileExchange must be used within AccountsFileExchangeProvider');
    }
    return context;
}
