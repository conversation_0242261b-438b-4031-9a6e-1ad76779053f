export enum FileCardType {
    Employer_Census_Upload = 'Employer Census Upload',
    Employer_Payroll_Upload = 'Employer Payroll Upload',
    Employee_Deduction_Upload = 'Employee Deduction Upload',
    Employee_Deduction_Download = 'Employee Deduction Download',
    Employee_Deposit_Upload = 'Employee Deposit Upload',
    WesBanco_Deposit_Reporting = 'WesBanco Deposit Reporting',
}

export type FileUploadItem = 'census' | 'payroll' | 'deduction';

export enum AccountsFileExchangeModal {
    Upload_Template = 'Upload_Template',
    Deduction_Email = 'Deduction_Email',
}

export interface UploadTemplateModalProps {
    employerId: number | string;
    item: FileUploadItem;
    title: string;
}

export interface DeductionEmailTemplateModalProps {
    groupId: number | string;
    date: string;
}

export type AccountsFileExchangeModalProps =
    | {
          type: AccountsFileExchangeModal.Upload_Template;
          props: UploadTemplateModalProps;
      }
    | {
          type: AccountsFileExchangeModal.Deduction_Email;
          props: DeductionEmailTemplateModalProps;
      };
