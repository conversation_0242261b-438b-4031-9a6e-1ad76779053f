import React, {useEffect, useState} from 'react';
import {<PERSON>ton, Grid2, <PERSON><PERSON>, TextField} from '@mui/material';
import {KasDatePickerFormField} from '@/components';
import {AccountsPayrollEmployerModel, SelectModel} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {useFormik} from 'formik';
import {EmployerPayrollUploadValues, validationSchema} from './schema';
import {EmployerDetailsContainer, ViewUploadTemplate} from '../../../index';
import dayjs from 'dayjs';

interface EmployerPayrollUploadFormProps {
    employer: SelectModel<string> | undefined;
    submitting: boolean;
    onSubmitForm: (url: string, data: FormData) => Promise<void>;
}

export const EmployerPayrollUploadForm = ({
    employer,
    submitting,
    onSubmitForm,
}: EmployerPayrollUploadFormProps) => {
    const [employerDetailsState, setEmployerDetailsState] = useState(
        getDefaultState<AccountsPayrollEmployerModel>,
    );

    const onSubmit = async (values: EmployerPayrollUploadValues) => {
        const payrollDate = dayjs(values.payrollDate).format('YYYYMMDD');
        const url = `/api/secured/accounts/file-exchange/upload/payroll?payrollDate=${payrollDate}`;
        const formData = new FormData();

        formData.append('file', values.file);
        formData.append('employer_id', employer?.value || '');

        await onSubmitForm(url, formData);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            file: '',
            payrollDate: null,
        },
        onSubmit,
        validationSchema,
    });

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const selectedFile = event.target.files?.[0];
        if (selectedFile) {
            formik.setFieldValue('file', selectedFile);
        }
    };

    const loadEmployerDetails = async () => {
        await formik.setFieldValue('file', '');

        if (employer?.value) {
            const url = `/api/secured/accounts/file-exchange/employer/${employer?.value}/payroll`;

            setEmployerDetailsState(getLoadingState(employerDetailsState));
            const response = await apiRequest(url);
            setEmployerDetailsState(getLoadedState(response));
        }
    };

    useEffect(() => {
        loadEmployerDetails().then();
    }, [employer?.value]);

    useEffect(() => {
        formik.setFieldValue('payrollDate', employerDetailsState.data?.last_payroll_run_date || null);
    }, [employerDetailsState.data?.last_payroll_run_date]);

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} alignItems='center'>
                <Grid2 size={9}>
                    {employer && (
                        <Stack direction='row' spacing={2}>
                            <ViewUploadTemplate employer={employer} item='payroll' />
                            <EmployerDetailsContainer
                                state={employerDetailsState}
                                onLoadState={loadEmployerDetails}>
                                <KasDatePickerFormField
                                    formik={formik}
                                    name='payrollDate'
                                    label='Payroll Date'
                                    disabled={submitting}
                                    showValidation={false}
                                />
                                <TextField
                                    fullWidth
                                    size='small'
                                    type='file'
                                    name='file'
                                    disabled={submitting}
                                    variant='outlined'
                                    onBlur={formik.handleBlur}
                                    onChange={handleFileChange}
                                    slotProps={{htmlInput: {accept: '.csv'}}}
                                />
                            </EmployerDetailsContainer>
                        </Stack>
                    )}
                </Grid2>
                <Grid2 size={3} ml='auto'>
                    <Button
                        fullWidth
                        variant='contained'
                        type='submit'
                        title='Upload Payroll File'
                        loading={submitting}
                        disabled={!formik.isValid || submitting}>
                        Upload
                    </Button>
                </Grid2>
            </Grid2>
        </form>
    );
};
