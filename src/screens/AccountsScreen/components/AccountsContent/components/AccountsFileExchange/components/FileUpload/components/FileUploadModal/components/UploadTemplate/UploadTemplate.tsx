import React, {useEffect, useState} from 'react';
import {UploadTemplateModalProps} from './../../../../interfaces';
import {TableView} from '@/views';
import {AccountsBankModel} from '@/interfaces';
import {UploadTemplateColumns} from './tables';
import {ColumnDef} from '@tanstack/react-table';
import {KasLoading, KasLoadingError, KasSwitch, KasSwitchWhen} from '@/components';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';

export const UploadTemplate = ({employerId, item}: UploadTemplateModalProps) => {
    const [state, setState] = useState(getDefaultState<any>());

    const loadData = async () => {
        setState(getLoadingState(state));
        const response = await apiRequest(
            `/api/secured/accounts/file-exchange/census/${employerId}?type=${item}`,
        );

        setState(getLoadedState(response));
    };

    useEffect(() => {
        loadData().then();
    }, []);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={state.loading}>
                <KasLoading />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!state.error}>
                <KasLoadingError view='contained' error={state.error} />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!state.data}>
                <TableView<AccountsBankModel>
                    withTableActions
                    columns={UploadTemplateColumns as ColumnDef<AccountsBankModel, unknown>[]}
                    loading={state.loading}
                    error={state.error}
                    data={state.data}
                    tableName='Census Details'
                />
            </KasSwitchWhen>
        </KasSwitch>
    );
};
