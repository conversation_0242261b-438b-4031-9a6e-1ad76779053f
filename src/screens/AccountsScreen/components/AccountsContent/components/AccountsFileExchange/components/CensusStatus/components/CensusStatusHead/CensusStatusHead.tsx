import React, {useState} from 'react';
import {Button, Typography, Grid2, FormControlLabel, Checkbox, Stack} from '@mui/material';
import {useFormik} from 'formik';
import {CensusStatusHeadValues, validationSchema} from './schema';
import {useCensusStatus} from './../../useCensusStatus';

export const CensusStatusHead = () => {
    const {loadFiles} = useCensusStatus();
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async (values: CensusStatusHeadValues) => {
        setSubmitting(true);
        await loadFiles(values);
        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            assigned: true,
            employerId: '',
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} mb={2} alignItems='center'>
                <Grid2 size={2}>
                    <Typography variant='h6'>Census Status</Typography>
                </Grid2>
                <Grid2 size={4} alignSelf='center' my={-1}>
                    <Stack flexDirection='row' useFlexGap flexWrap='wrap'>
                        <FormControlLabel
                            disabled={submitting}
                            label='My Employers Only'
                            control={
                                <Checkbox
                                    size='small'
                                    name='assigned'
                                    sx={{height: 28}}
                                    onChange={formik.handleChange}
                                    checked={formik.values.assigned}
                                />
                            }
                        />
                    </Stack>
                </Grid2>
                <Grid2 size={2} ml='auto'>
                    <Button
                        fullWidth
                        variant='contained'
                        type='submit'
                        disabled={!formik.isValid || submitting}>
                        Search
                    </Button>
                </Grid2>
            </Grid2>
        </form>
    );
};
