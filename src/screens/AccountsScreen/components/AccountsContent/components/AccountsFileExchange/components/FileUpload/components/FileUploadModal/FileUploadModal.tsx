import React, {useMemo} from 'react';
import {KasModal} from '@/components';
import {DeductionEmail, UploadTemplate} from './components';
import {useFileUpload} from './../../useFileUpload';
import {AccountsFileExchangeModal} from './../../interfaces';

export const FileUploadModal = () => {
    const {openModal, setOpenModal} = useFileUpload();

    const size = useMemo(() => {
        switch (openModal?.type) {
            case AccountsFileExchangeModal.Upload_Template:
                return 'large';
            default:
                return 'medium';
        }
    }, [openModal?.type]);

    const title = useMemo(() => {
        switch (openModal?.type) {
            case AccountsFileExchangeModal.Upload_Template:
                return openModal?.props.title;
            case AccountsFileExchangeModal.Deduction_Email:
                return 'Email Preview';
            default:
                return 'Action Modal';
        }
    }, [openModal?.type]);

    const renderActionForm = useMemo(() => {
        switch (openModal?.type) {
            case AccountsFileExchangeModal.Upload_Template:
                return <UploadTemplate {...openModal.props} />;
            case AccountsFileExchangeModal.Deduction_Email:
                return <DeductionEmail {...openModal.props} />;
            default:
                return null;
        }
    }, [openModal?.type]);

    return (
        <KasModal title={title} open={!!openModal} size={size} onClose={() => setOpenModal(null)}>
            {renderActionForm}
        </KasModal>
    );
};
