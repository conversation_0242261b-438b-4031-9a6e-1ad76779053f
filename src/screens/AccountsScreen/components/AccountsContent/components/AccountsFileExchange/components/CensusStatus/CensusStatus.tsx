import React from 'react';
import {CensusStatusHead, CensusStatusResult} from './components';
import {Divider, Paper} from '@mui/material';
import Box from '@mui/material/Box';
import {KasLoadingBackDrop, KasLoading<PERSON><PERSON><PERSON>, KasSwitch, KasS<PERSON><PERSON>hen} from '@/components';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {NoResultsView} from '@/views';
import {useCensusStatus} from './useCensusStatus';

export const CensusStatus = () => {
    const {filesState} = useCensusStatus();

    return (
        <Paper elevation={0}>
            <Box p={2}>
                <CensusStatusHead />
                <Divider />
                <Box position='relative' pt={2}>
                    {filesState.loading && <KasLoadingBackDrop />}
                    <KasSwitch>
                        <KasSwitchWhen condition={!!filesState.error}>
                            <KasLoadingError view='contained' error={filesState.error || DEFAULT_ERROR_MSG} />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={!filesState.data}>
                            <NoResultsView text='Search result will appear here' />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={!!filesState.data}>
                            <CensusStatusResult />
                        </KasSwitchWhen>
                    </KasSwitch>
                </Box>
            </Box>
        </Paper>
    );
};
