import * as Yup from 'yup';
import {DEFAULT_VALIDATION_MSG} from '@/constants';

export const validationSchema = Yup.object().shape({
    file: Yup.string().required(DEFAULT_VALIDATION_MSG),
    provider: Yup.string().required(DEFAULT_VALIDATION_MSG),
    depositDate: Yup.string().required(DEFAULT_VALIDATION_MSG).nullable(),
});

export type EmployeeDepositUploadValues = Yup.Asserts<typeof validationSchema>;
