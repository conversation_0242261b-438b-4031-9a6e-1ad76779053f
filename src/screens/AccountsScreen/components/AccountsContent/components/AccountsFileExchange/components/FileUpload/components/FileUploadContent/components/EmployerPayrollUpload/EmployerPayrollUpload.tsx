import React, {useState} from 'react';
import {SelectModel} from '@/interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {useSnackbar} from '@/hooks/useSnackbar';
import {DEFAULT_ERROR_MSG, DEFAULT_SUCCESS_MSG} from '@/constants';
import {UploadContainer} from './../../components';
import {EmployerPayrollUploadForm} from './components';

export const EmployerPayrollUpload = () => {
    const {showMessage} = useSnackbar();
    const [submitting, setSubmitting] = useState(false);
    const [selectedEmployer, setSelectedEmployer] = useState<SelectModel<string>>();

    const onSubmit = async (url: string, formData: FormData) => {
        setSubmitting(true);

        const response = await apiRequest(url, {
            method: 'post',
            body: formData,
        });

        if (response.value) {
            showMessage(DEFAULT_SUCCESS_MSG, 'success');
        } else {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        }

        setSubmitting(false);
    };

    return (
        <UploadContainer employerType='payroll_upload' submitting={submitting} onChange={setSelectedEmployer}>
            <EmployerPayrollUploadForm
                employer={selectedEmployer}
                submitting={submitting}
                onSubmitForm={onSubmit}
            />
        </UploadContainer>
    );
};
