import React, {PropsWithChildren, useEffect, useMemo, useState} from 'react';
import {Checkbox, FormControlLabel, Grid2} from '@mui/material';
import {SelectModel} from '@/interfaces';
import {useFileUpload} from './../../../../useFileUpload';
import {useSecured} from '@/hooks/useSecured';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {useFormik} from 'formik';
import {UploadContainerValues, validationSchema} from './schema';
import {KasAutocompleteField} from '@/components';

interface UploadContainerProps extends PropsWithChildren {
    employerType: string;
    submitting: boolean;
    onChange: (employer: SelectModel<string> | undefined) => void;
}

export const UploadContainer = ({employerType, submitting, onChange, children}: UploadContainerProps) => {
    const {loadUploadEmployers} = useFileUpload();
    const {hasRoles} = useSecured();
    const [employersState, setEmployersState] = useState(getDefaultState<SelectModel<string>[]>);
    const showAssigned = hasRoles(['KASH_ACCOUNTS', 'KASH_POWERUSER']);

    const formik = useFormik<UploadContainerValues>({
        validateOnMount: true,
        initialValues: {
            assigned: true,
            employerId: '',
        },
        onSubmit: () => {},
        validationSchema,
    });

    const selectedEmployer = useMemo(() => {
        return employersState.data?.find(({value}) => value === formik.values.employerId);
    }, [formik.values.employerId, employersState.data]);

    useEffect(() => {
        onChange(selectedEmployer);
    }, [selectedEmployer]);

    const loadEmployers = async () => {
        await formik.setFieldValue('employerId', '');
        setEmployersState(getLoadingState(employersState));
        const response = await loadUploadEmployers(`${employerType}`, formik.values.assigned);
        setEmployersState(getLoadedState(response));
    };

    useEffect(() => {
        loadEmployers().then();
    }, [formik.values.assigned]);

    return (
        <Grid2 container spacing={2} wrap='nowrap'>
            <Grid2 size={2}>
                <KasAutocompleteField
                    name='employerId'
                    formik={formik}
                    label='Employer'
                    disabled={submitting}
                    loading={employersState.loading}
                    options={employersState.data || []}
                    showValidation={false}
                />
            </Grid2>
            {showAssigned && (
                <Grid2 size={2}>
                    <FormControlLabel
                        disabled={employersState.loading || submitting}
                        label='My Employers Only'
                        control={
                            <Checkbox
                                size='small'
                                name='assigned'
                                onChange={formik.handleChange}
                                checked={formik.values.assigned}
                            />
                        }
                    />
                </Grid2>
            )}
            <Grid2 flexGrow={1}>{children}</Grid2>
        </Grid2>
    );
};
