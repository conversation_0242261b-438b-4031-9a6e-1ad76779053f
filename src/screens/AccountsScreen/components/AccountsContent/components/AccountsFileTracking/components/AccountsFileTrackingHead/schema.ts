import * as Yup from 'yup';
import {DEFAULT_VALIDATION_MSG} from '@/constants';

export const validationSchema = Yup.object().shape({
    assigned: Yup.boolean().required(DEFAULT_VALIDATION_MSG),
    completed: Yup.boolean().required(DEFAULT_VALIDATION_MSG),
    length: Yup.number().required(DEFAULT_VALIDATION_MSG).nullable(),
});

export type AccountsFileTrackingHeadValues = Yup.Asserts<typeof validationSchema>;
