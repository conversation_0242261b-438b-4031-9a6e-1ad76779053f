import {CellContext, createColumnHelper} from '@tanstack/react-table';
import {defaultInfoColumn, expandCell} from '@/utils/TableUtils';
import {AccountsJobLogModel} from '@/interfaces';
import {JobActionCell, JobDetailCell, JobStatusCell} from './../components';
import React from 'react';

const columnHelper = createColumnHelper<AccountsJobLogModel>();

const _defaultInfoColumn = defaultInfoColumn<AccountsJobLogModel>;

const EXPANDED_STATUSES = ['REVIEW', 'FAILED', 'RESOLVED'];

export const LogsColumns = [
    {
        id: 'expander',
        header: '',
        cell: (props: CellContext<AccountsJobLogModel, string>) =>
            EXPANDED_STATUSES.includes(props.row.original.job_status.toUpperCase())
                ? expandCell(props)
                : null,
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
    _defaultInfoColumn('gid', 'ID'),
    _defaultInfoColumn('employer_name', 'Employer'),
    _defaultInfoColumn('job_type', 'Type'),
    _defaultInfoColumn('job_context', 'Context'),
    columnHelper.accessor('job_detail', {
        id: 'job_detail',
        header: 'Detail',
        cell: (props) => <JobDetailCell data={props.row.original} />,
    }),
    _defaultInfoColumn('job_start_time', 'Start Time'),
    _defaultInfoColumn('job_end_time', 'End Time'),
    _defaultInfoColumn('job_user_name', 'User'),
    columnHelper.accessor('job_status', {
        id: 'job_status',
        header: 'Status',
        cell: (props) => <JobStatusCell data={props.row.original} />,
    }),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<AccountsJobLogModel, string>) => (
            <JobActionCell data={props.row.original} />
        ),
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
];
