import React from 'react';
import {Check, Close, Delete, Refresh} from '@mui/icons-material';
import {ActionCell, DownloadCell} from '@/components/table/cells';
import {AccountsJobLogModel} from '@/interfaces';
import {Stack} from '@mui/material';
import ListIcon from '@mui/icons-material/List';
import {useAccountsFileTracking} from './../../../../useAccountsFileTracking';
import {AccountsFileTrackingItemModal} from './../../../../interfaces';

export const JobActionCell = ({data}: {data: AccountsJobLogModel}) => {
    const {setOpenModal} = useAccountsFileTracking();

    return (
        <Stack direction='row' spacing={1}>
            {data.job_document_id && (
                <DownloadCell
                    titleAccess='Download Original File'
                    params={JSON.stringify({path: `/secured/accounts/document/BatchJob/${data.gid}`})}
                />
            )}
            <DownloadCell
                titleAccess='Click for Report'
                params={JSON.stringify({path: `/secured/accounts/upload/jobs/${data.gid}`})}
                Icon={<ListIcon />}
            />
            {data.rollback && (
                <ActionCell
                    Icon={<Delete color='error' titleAccess='Delete Job' />}
                    onClick={() => {
                        setOpenModal({
                            type: AccountsFileTrackingItemModal.Delete_Job,
                            props: {jobId: data.gid},
                        });
                    }}
                />
            )}
            {/*{data.job_status === 'REVIEW' && data.job_type === 'CensusUploadService' && (*/}
            {/*    <ActionCell*/}
            {/*        Icon={<Check color='success' titleAccess='Confirm and Proceed' />}*/}
            {/*        onClick={() => {*/}
            {/*            setOpenModal({*/}
            {/*                type: AccountsFileTrackingItemModal.Confirm_Job,*/}
            {/*                props: {data},*/}
            {/*            });*/}
            {/*        }}*/}
            {/*    />*/}
            {/*)}*/}
            <ActionCell
                Icon={<Refresh titleAccess='Rerun File' />}
                onClick={() => {
                    setOpenModal({
                        type: AccountsFileTrackingItemModal.Rerun_File,
                        props: {data},
                    });
                }}
            />
            {data.job_status !== 'COMPLETED' && (
                <ActionCell
                    Icon={<Close color='error' titleAccess='Retry Close Operation' />}
                    onClick={() => {
                        setOpenModal({
                            type: AccountsFileTrackingItemModal.Retry_Close_Operation,
                            props: {data},
                        });
                    }}
                />
            )}
        </Stack>
    );
};
