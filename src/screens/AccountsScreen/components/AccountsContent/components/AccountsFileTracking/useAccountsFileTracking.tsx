import React, {createContext, useContext, useState} from 'react';
import {AccountsFileTrackingModalProps} from './interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {DEFAULT_ERROR_MSG, DEFAULT_SUCCESS_MSG} from '@/constants';
import {useSnackbar} from '@/hooks/useSnackbar';
import {AccountsFileTrackingParamsModel} from '@/screens/AccountsScreen/interfaces';
import {AccountsJobLogModel, AccountsJobUploadService, DataStateInterface} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';

interface AccountsFileTrackingContextModel {
    filterParams: AccountsFileTrackingParamsModel;
    logsState: DataStateInterface<AccountsJobLogModel[]>;
    loadLogs: (params?: AccountsFileTrackingParamsModel) => Promise<void>;
    openModal: AccountsFileTrackingModalProps | null;
    setOpenModal: (value: AccountsFileTrackingModalProps | null) => void;
    deleteJob: (gid: number) => Promise<void>;
    closeJob: (data: AccountsJobLogModel) => Promise<void>;
    rerunJob: (data: AccountsJobLogModel) => Promise<void>;
    confirmJob: (data: AccountsJobLogModel) => Promise<void>;
}

const AccountsFileTrackingContext = createContext<AccountsFileTrackingContextModel | undefined>(undefined);

export const AccountsFileTrackingProvider = ({children}: {children: React.ReactNode}) => {
    const {showMessage} = useSnackbar();
    const [openModal, setOpenModal] = useState<AccountsFileTrackingModalProps | null>(null);
    const [logsState, setLogsState] = useState(getDefaultState<AccountsJobLogModel[]>);
    const [filterParams, setFilterParams] = useState<AccountsFileTrackingParamsModel>({
        assigned: true,
        completed: false,
        length: null,
    });

    const loadLogs = async (params?: AccountsFileTrackingParamsModel) => {
        const curParams = params || filterParams;
        const length = curParams.length ? String(curParams.length) : null;
        const urlParams = new URLSearchParams({
            assigned: String(curParams.assigned),
            completed: String(curParams.completed),
            ...(length && {length}),
        }).toString();

        const url = `/api/secured/accounts/file-tracking/logs?${urlParams}`;

        setFilterParams(curParams);
        setLogsState(getLoadingState(logsState));
        const response = await apiRequest(url);
        setLogsState(getLoadedState(response));
    };

    const getJobType = (value: AccountsJobUploadService) => {
        switch (value) {
            case AccountsJobUploadService.Census:
                return 'census';
            case AccountsJobUploadService.Deduction:
                return 'deduction';
            case AccountsJobUploadService.Deposit:
                return 'deposit';
            case AccountsJobUploadService.Payroll:
                return 'payroll';
            default:
                return 'unknown';
        }
    };

    const onSubmitJobAction = async (url: string, init?: RequestInit) => {
        const response = await apiRequest(url, init);

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            showMessage(DEFAULT_SUCCESS_MSG, 'success');
            loadLogs().then();
        }

        setOpenModal(null);
    };

    const deleteJob = async (gid: number) => {
        const url = `/api/secured/accounts/file-tracking/job/${gid}`;

        await onSubmitJobAction(url, {method: 'delete'});
    };

    const closeJob = async (data: AccountsJobLogModel) => {
        const url = `/api/secured/accounts/file-tracking/job/action/${getJobType(data.job_type)}/close`;
        const body = JSON.stringify({gid: data.gid});

        await onSubmitJobAction(url, {method: 'put', body});
    };

    const rerunJob = async (data: AccountsJobLogModel) => {
        const url = `/api/secured/accounts/file-tracking/job/action/${getJobType(data.job_type)}/rerun`;
        const body = JSON.stringify({gid: data.gid});

        await onSubmitJobAction(url, {method: 'post', body});
    };

    const confirmJob = async (data: AccountsJobLogModel) => {
        const url = `/api/secured/accounts/file-tracking/job/action/${getJobType(data.job_type)}/confirm`;
        const body = JSON.stringify({gid: data.gid});

        await onSubmitJobAction(url, {method: 'post', body});
    };

    const value: AccountsFileTrackingContextModel = {
        filterParams,
        logsState,
        loadLogs,
        openModal,
        setOpenModal,
        deleteJob,
        closeJob,
        rerunJob,
        confirmJob,
    };

    return (
        <AccountsFileTrackingContext.Provider value={value}>{children}</AccountsFileTrackingContext.Provider>
    );
};

export function useAccountsFileTracking() {
    const context = useContext(AccountsFileTrackingContext);
    if (!context) {
        throw new Error('useAccountsFileTracking must be used within AccountsFileTrackingProvider');
    }
    return context;
}
