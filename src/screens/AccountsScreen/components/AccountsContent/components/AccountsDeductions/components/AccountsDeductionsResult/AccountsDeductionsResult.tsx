import React from 'react';
import {AccountsDeductionsColumns} from './tables';
import {TableView} from '@/views';
import {useAccountsDeductions} from '@/screens/AccountsScreen/components/AccountsContent/components';
import {AccountsDeductionModel} from '@/interfaces';

export const AccountsDeductionsResult = () => {
    const {deductionsState} = useAccountsDeductions();

    return (
        <TableView<AccountsDeductionModel>
            withTableActions
            columns={AccountsDeductionsColumns}
            loading={deductionsState.loading}
            error={deductionsState.error}
            data={deductionsState.data}
            tableName='Accounts Deduction'
        />
    );
};
