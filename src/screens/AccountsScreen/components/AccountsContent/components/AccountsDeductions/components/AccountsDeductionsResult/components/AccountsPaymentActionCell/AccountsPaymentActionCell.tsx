import React from 'react';
import {PlaylistAdd} from '@mui/icons-material';
import {ActionCell} from '@/components/table/cells';
import {AccountsDeductionModel} from '@/interfaces';
import {useAccountsDeductions} from '@/screens/AccountsScreen/components/AccountsContent/components';
import {Stack} from '@mui/material';
import {AccountsDeductionsItemModal} from './../../../../interfaces';

export const AccountsPaymentActionCell = ({data}: {data: AccountsDeductionModel}) => {
    const {setOpenModal} = useAccountsDeductions();

    if (data.request_date) {
        return null;
    }

    return (
        <Stack direction='row' spacing={1}>
            <ActionCell
                Icon={<PlaylistAdd color='primary' titleAccess='Request Deduction' />}
                onClick={() =>
                    setOpenModal({
                        type: AccountsDeductionsItemModal.Request_Deduction,
                        props: {loanId: data.loan_id},
                    })
                }
            />
        </Stack>
    );
};
