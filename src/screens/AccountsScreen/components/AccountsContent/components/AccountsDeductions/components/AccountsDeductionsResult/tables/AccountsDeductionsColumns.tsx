import {CellContext, ColumnDef, createColumnHelper} from '@tanstack/react-table';
import {defaultInfoColumn} from '@/utils/TableUtils';
import {AccountsDeductionModel} from '@/interfaces';
import {AccountsPaymentActionCell, LoanStatusCell} from './../components';
import {KasUnderwritingSharedLink} from '@/components';
import React from 'react';

const columnHelper = createColumnHelper<AccountsDeductionModel>();

const _defaultInfoColumn = defaultInfoColumn<AccountsDeductionModel>;

export const AccountsDeductionsColumns = [
    columnHelper.accessor((data) => `${data.first_name} ${data.last_name} [${data.employee_id}]`, {
        id: 'employee_id',
        header: 'Employee [ID]',
        cell: (props) => {
            const {first_name, last_name, employee_id} = props.row.original;

            return (
                <>
                    {first_name} {last_name} [<KasUnderwritingSharedLink id={employee_id} />]
                </>
            );
        },
        meta: {
            exportHTML: (cell) => {
                const {first_name, last_name, employee_id} = cell.row.original;

                return `${first_name} ${last_name} [${employee_id}]`;
            },
        },
    }),
    _defaultInfoColumn('id_at_employer', 'ID at Employer'),
    _defaultInfoColumn('loan_id', 'Loan ID'),
    columnHelper.accessor('loan_status', {
        id: 'loan_status',
        header: 'Loan Status',
        cell: (props) => <LoanStatusCell status={props.getValue()} />,
        meta: {
            exportHTML: (cell) => cell.getValue(),
        },
    }),
    _defaultInfoColumn('close_date', 'Close Date'),
    _defaultInfoColumn('request_date', 'Request Date'),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<AccountsDeductionModel, string>) => {
            return <AccountsPaymentActionCell data={props.row.original} />;
        },
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
] as ColumnDef<AccountsDeductionModel, unknown>[];
