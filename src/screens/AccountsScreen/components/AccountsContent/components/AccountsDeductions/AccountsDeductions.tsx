import React from 'react';
import {Divider, Paper, Stack} from '@mui/material';
import Box from '@mui/material/Box';
import {AccountsDeductionsHead, AccountsDeductionsModal, AccountsDeductionsResult} from './components';
import {useAccountsDeductions} from './useAccountsDeductions';
import {KasLoadingBackDrop, KasLoadingError, KasSwitch, KasSwitchWhen} from '@/components';
import {NoResultsView} from '@/views';
import {DEFAULT_ERROR_MSG} from '@/constants';

export const AccountsDeductions = () => {
    const {deductionsState} = useAccountsDeductions();

    return (
        <Paper elevation={0}>
            <Stack spacing={2} p={2}>
                <AccountsDeductionsHead />
                <Divider />
                <Box position='relative'>
                    {deductionsState.loading && <KasLoadingBackDrop />}
                    <KasSwitch>
                        <KasSwitchWhen condition={!!deductionsState.error}>
                            <KasLoadingError
                                view='contained'
                                error={deductionsState.error || DEFAULT_ERROR_MSG}
                            />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={!deductionsState.data}>
                            <NoResultsView text='Search result will appear here' />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={!!deductionsState.data}>
                            <AccountsDeductionsResult />
                        </KasSwitchWhen>
                    </KasSwitch>
                </Box>
            </Stack>
            <AccountsDeductionsModal />
        </Paper>
    );
};
