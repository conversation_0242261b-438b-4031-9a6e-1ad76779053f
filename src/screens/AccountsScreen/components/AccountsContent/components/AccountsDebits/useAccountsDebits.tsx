import React, {createContext, useContext, useState} from 'react';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {AccountsDebitModel, DataStateInterface} from '@/interfaces';
import {AccountsDebitsParamsModel} from '@/screens/AccountsScreen/interfaces';
import {AccountsDebitsModalProps} from './interfaces';
import {DEFAULT_FILTER_PARAMS} from './data';

interface AccountsDebitsContextModel {
    loading: boolean;
    debitsState: DataStateInterface<any[]>;
    loadDebits: (params?: AccountsDebitsParamsModel) => Promise<void>;
    openModal: AccountsDebitsModalProps | null;
    setOpenModal: (value: AccountsDebitsModalProps | null) => void;
    filterParams: AccountsDebitsParamsModel;
}

const AccountsDebitsContext = createContext<AccountsDebitsContextModel | undefined>(undefined);

export const AccountsDebitsProvider = ({children}: {children: React.ReactNode}) => {
    const [debitsState, setDebitsState] = useState(getDefaultState<AccountsDebitModel[]>());
    const [openModal, setOpenModal] = useState<AccountsDebitsModalProps | null>(null);
    const [loading, setLoading] = useState(false);
    const [filterParams, setFilterParams] = useState<AccountsDebitsParamsModel>(DEFAULT_FILTER_PARAMS);

    const loadDebits = async (params?: AccountsDebitsParamsModel) => {
        const curParams = params || filterParams;
        const employerId = curParams.employerId;
        const urlParams = new URLSearchParams({
            dateFrom: curParams.dateFrom.format('YYYYMMDD'),
            dateTo: curParams.dateTo.format('YYYYMMDD'),
            assigned: String(curParams.assigned),
            billed: String(curParams.billed),
            ...(employerId && {employerId}),
        });
        const url = `/api/secured/accounts/debits${employerId ? `/employer/${employerId}` : ''}?${urlParams.toString()}`;

        setLoading(true);
        setFilterParams(curParams);
        setDebitsState(getLoadingState(debitsState));
        const response = await apiRequest(url);
        setDebitsState(getLoadedState(response));
        setLoading(false);
    };

    const value: AccountsDebitsContextModel = {
        loading,
        debitsState,
        loadDebits,
        openModal,
        setOpenModal,
        filterParams,
    };

    return <AccountsDebitsContext.Provider value={value}>{children}</AccountsDebitsContext.Provider>;
};

export function useAccountsDebits() {
    const context = useContext(AccountsDebitsContext);
    if (!context) {
        throw new Error('useAccountsDebits must be used within AccountsDebitsProvider');
    }
    return context;
}
