import React, {ChangeEvent, useEffect, useState} from 'react';
import {Button, Checkbox, FormControlLabel, SelectChangeEvent, Stack, Typography, Grid2} from '@mui/material';
import MenuItem from '@mui/material/MenuItem';
import {apiRequest} from '@/utils/AxiosUtils';
import {AccountsDebitEmployerEmailModel, AccountsDebitModel} from '@/interfaces';
import {KasLoading, KasNoResults, KasSelect, KasSwitch, KasSwitchWhen} from '@/components';
import {EmailPreview, ErrorView} from '@/views';
import {useSnackbar} from '@/hooks/useSnackbar';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {useAccountsDebits} from '@/screens/AccountsScreen/components/AccountsContent/components';
import dayjs from 'dayjs';
import {usePrint} from '@/hooks/usePrint';

export const SendEmailForm = ({data}: {data: AccountsDebitModel}) => {
    const {setOpenModal} = useAccountsDebits();
    const {handlePrint, handleEmailPrintContent} = usePrint();
    const baseUrl = `/api/secured/accounts/debits/employer/${data.employer_id}/email`;
    const {showMessage} = useSnackbar();
    const [submitting, setSubmitting] = useState(false);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [items, setItems] = useState<AccountsDebitEmployerEmailModel[]>([]);
    const [selectedItem, setSelectedItem] = useState<AccountsDebitEmployerEmailModel>();
    const [selectedEmails, setSelectedEmails] = useState<string[]>([]);

    const handleChange = (event: SelectChangeEvent) => {
        const type = event.target.value;
        const item = items?.find(({email_type}) => email_type === type);

        setSelectedItem(item);
        setSelectedEmails(item?.recipients.map((item) => item.email || '') || []);
    };

    const handleChangeRecipients = (event: ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value;

        if (selectedEmails.includes(value)) {
            setSelectedEmails(selectedEmails.filter((email) => email !== value));
        } else {
            setSelectedEmails([...selectedEmails, value]);
        }
    };

    const handlePrintContent = (value: string) => {
        handleEmailPrintContent(value, selectedItem?.recipients, selectedItem?.email_type);
    };

    const sendEmail = async () => {
        setSubmitting(true);
        showMessage('Sending email...', 'info');
        const recipients = selectedItem?.recipients.filter(
            (item) => !!item.email && selectedEmails.includes(item.email),
        );
        const response = await apiRequest(`${baseUrl}?preview=false`, {
            method: 'POST',
            body: JSON.stringify({...selectedItem, recipients}),
        });

        if (response.error) {
            showMessage(response.error, 'error');
        } else {
            setOpenModal(null);
            showMessage('Email sent successfully', 'success');
        }
        setSubmitting(false);
    };

    const loadData = async () => {
        const url = `${baseUrl}?date=${dayjs(data.date).format('YYYYMMDD')}&type=${data.debit_type}`;

        setError('');
        setLoading(true);
        const response = await apiRequest(url);

        if (response.value) {
            const values: AccountsDebitEmployerEmailModel[] = response.value;

            setItems(values);

            if (values.length) {
                setSelectedItem(values[0]);
                setSelectedEmails(values[0].recipients.map((item) => item.email || ''));
            }
        } else {
            setError(response.error || DEFAULT_ERROR_MSG);
        }
        setLoading(false);
    };

    useEffect(() => {
        loadData().then();
    }, []);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={loading}>
                <KasLoading />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!error}>
                <ErrorView error={error} />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!items.length}>
                <KasNoResults text='No email available to display.' p={2} bgcolor='var(--color-grey)' />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!items.length}>
                <Grid2 container spacing={1}>
                    <Grid2 size={6}>
                        <KasSelect
                            label='Type'
                            disabled={submitting}
                            value={selectedItem?.email_type ? selectedItem.email_type : ''}
                            onChange={handleChange}
                            renderValue={() => (selectedItem ? selectedItem.email_type : '')}>
                            {items?.map((item) => (
                                <MenuItem key={item.email_type} value={item.email_type}>
                                    {item.email_type}
                                </MenuItem>
                            ))}
                        </KasSelect>
                    </Grid2>
                    {selectedItem && (
                        <Grid2 size={12}>
                            <Typography variant='subtitle1'>Recipients</Typography>
                            {selectedItem.recipients.map((item, index) => (
                                <div key={index}>
                                    <FormControlLabel
                                        disabled={submitting}
                                        label={item.email}
                                        control={
                                            <Checkbox
                                                size='small'
                                                value={item.email}
                                                checked={!!item.email && selectedEmails.includes(item.email)}
                                                onChange={handleChangeRecipients}
                                                inputProps={{'aria-label': 'controlled'}}
                                            />
                                        }
                                    />
                                </div>
                            ))}
                            <Typography variant='subtitle1'>Preview</Typography>
                            <EmailPreview
                                url={`${baseUrl}?preview=true`}
                                payload={JSON.stringify(selectedItem)}
                                onContent={handlePrintContent}
                            />
                        </Grid2>
                    )}
                    <Grid2 size={12} pt={2}>
                        <Stack direction='row' justifyContent='flex-end' spacing={2}>
                            <Button onClick={() => setOpenModal(null)}>CLOSE</Button>
                            <Button variant='outlined' onClick={() => handlePrint()}>
                                PRINT
                            </Button>
                            <Button
                                variant='contained'
                                disabled={!selectedEmails.length || submitting}
                                loading={submitting}
                                onClick={sendEmail}>
                                SEND EMAIL
                            </Button>
                        </Stack>
                    </Grid2>
                </Grid2>
            </KasSwitchWhen>
        </KasSwitch>
    );
};
