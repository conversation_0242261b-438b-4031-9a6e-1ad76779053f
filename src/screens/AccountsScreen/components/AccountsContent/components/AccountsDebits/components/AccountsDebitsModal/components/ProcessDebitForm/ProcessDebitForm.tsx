import React, {useState} from 'react';
import {KasModalFooter} from '@/components';
import {TextField, Grid2, InputAdornment} from '@mui/material';
import {useFormik} from 'formik';
import {ProcessDebitFormValues, validationSchema} from './schema';
import {useSnackbar} from '@/hooks/useSnackbar';
import {apiRequest} from '@/utils/AxiosUtils';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {useAccountsDebits} from '@/screens/AccountsScreen/components/AccountsContent/components';
import {AccountsDebitModel} from '@/interfaces';
import dayjs from 'dayjs';

export const ProcessDebitForm = ({data}: {data: AccountsDebitModel}) => {
    const {showMessage} = useSnackbar();
    const {setOpenModal, loadDebits} = useAccountsDebits();
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async (values: ProcessDebitFormValues) => {
        const url = `/api/secured/accounts/debits/process`;
        const payload = {
            entity_class: 'Employer',
            entity_id: data.employer_id,
            process_date: dayjs(data.date).format('YYYYMMDD'),
            effective_date: dayjs(data.date).format('YYYYMMDD'),
            action_type_txt: values.amount,
            debit_type: data.debit_type,
            debit_id: data.debit_id,
        };

        setSubmitting(true);

        const response = await apiRequest(url, {
            method: 'put',
            body: JSON.stringify(payload),
        });

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            setOpenModal(null);
            await loadDebits();
        }

        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            amount: data.amount || '',
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        name='amount'
                        value={formik.values.amount}
                        disabled={submitting}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Amount'
                        variant='outlined'
                        type='number'
                        slotProps={{
                            input: {
                                startAdornment: <InputAdornment position='start'>$</InputAdornment>,
                            },
                            htmlInput: {
                                step: '0.01',
                            },
                        }}
                        error={!!formik.errors.amount && formik.touched.amount}
                        helperText={formik.touched.amount && formik.errors.amount}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        loading={submitting}
                        disabled={!formik.isValid || submitting}
                        onCancel={() => setOpenModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
