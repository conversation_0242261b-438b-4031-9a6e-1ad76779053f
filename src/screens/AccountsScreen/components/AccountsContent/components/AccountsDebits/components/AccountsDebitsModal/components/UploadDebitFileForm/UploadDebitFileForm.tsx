import React, {useState} from 'react';
import {useFormik} from 'formik';
import {UploadDebitFileFormValues, validationSchema} from './schema';
import {Grid2, TextField, Typography} from '@mui/material';
import {Kas<PERSON>odalFooter} from '@/components';
import {apiRequest} from '@/utils/AxiosUtils';
import {useSnackbar} from '@/hooks/useSnackbar';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {useAccountsDebits} from '@/screens/AccountsScreen/components/AccountsContent/components';

export const UploadDebitFileForm = () => {
    const {showMessage} = useSnackbar();
    const {setOpenModal, loadDebits} = useAccountsDebits();
    const [openFileEver, setOpenFileEver] = useState(false);
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async (values: UploadDebitFileFormValues) => {
        const url = `/api/secured/accounts/payments/upload-debit-file`;
        const formData = new FormData();

        setSubmitting(true);
        formData.append('file', values.file);

        const response = await apiRequest(url, {
            method: 'post',
            body: formData,
        });

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            setOpenModal(null);
            await loadDebits();
        }

        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            file: '',
        },
        onSubmit,
        validationSchema,
    });

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const selectedFile = event.target.files?.[0];
        if (selectedFile) {
            formik.setFieldValue('file', selectedFile);
        }
    };

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={12}>
                    <Typography variant='body1'>
                        Please select the payments CSV file you would like to upload.
                    </Typography>
                </Grid2>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        type='file'
                        name='file'
                        disabled={submitting}
                        variant='outlined'
                        onBlur={formik.handleBlur}
                        onFocus={() => {
                            setOpenFileEver(!!formik.touched.file);
                        }}
                        onChange={handleFileChange}
                        slotProps={{htmlInput: {accept: '.csv'}}}
                        error={openFileEver && !!formik.errors.file}
                        helperText={openFileEver && formik.errors.file}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        submitText='Upload'
                        loading={submitting}
                        disabled={!formik.isValid || submitting}
                        onCancel={() => setOpenModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
