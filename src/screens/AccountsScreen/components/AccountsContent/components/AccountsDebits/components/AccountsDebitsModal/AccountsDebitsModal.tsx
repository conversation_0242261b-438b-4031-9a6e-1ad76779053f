import React, {useMemo} from 'react';
import {KasModal} from '@/components';
import {ProcessDebitForm, SendEmailForm, UploadDebitFileForm} from './components';
import {
    CreateEmployerTransactionForm,
    useAccountsDebits,
} from '@/screens/AccountsScreen/components/AccountsContent/components';
import {AccountsDebitsItemModal} from './../../interfaces';

export const AccountsDebitsModal = () => {
    const {openModal, setOpenModal, loadDebits} = useAccountsDebits();

    const onSuccessAddedEmployerTransaction = async () => {
        setOpenModal(null);
        await loadDebits();
    };

    const renderActionForm = useMemo(() => {
        switch (openModal?.type) {
            case AccountsDebitsItemModal.Add_Guarantee:
            case AccountsDebitsItemModal.Add_PEPM:
                return (
                    <CreateEmployerTransactionForm
                        {...openModal.props}
                        onSuccess={onSuccessAddedEmployerTransaction}
                        onClose={() => setOpenModal(null)}
                    />
                );
            case AccountsDebitsItemModal.Process_Debit:
                return <ProcessDebitForm {...openModal.props} />;
            case AccountsDebitsItemModal.Send_Email:
                return <SendEmailForm {...openModal.props} />;
            case AccountsDebitsItemModal.Upload_Debit_File:
                return <UploadDebitFileForm />;
            default:
                return null;
        }
    }, [openModal?.type]);

    const size = useMemo(() => {
        switch (openModal?.type) {
            case AccountsDebitsItemModal.Send_Email:
                return 'medium';
            default:
                return 'small';
        }
    }, [openModal?.type]);

    return (
        <KasModal
            title={openModal?.type || 'Action Modal'}
            open={!!openModal}
            size={size}
            onClose={() => setOpenModal(null)}>
            {renderActionForm}
        </KasModal>
    );
};
