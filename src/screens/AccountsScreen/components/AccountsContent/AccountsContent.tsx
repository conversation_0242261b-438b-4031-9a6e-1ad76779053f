import './styles.scss';

import React from 'react';
import {useAccounts} from '@/screens/AccountsScreen/useAccounts';
import {AccountItemType} from '@/screens/AccountsScreen/interfaces';
import {
    AccountsBanks,
    AccountsBanksProvider,
    AccountsClearing,
    AccountsClearingProvider,
    AccountsDebits,
    AccountsDebitsProvider,
    AccountsDeductions,
    AccountsDeductionsProvider,
    AccountsEmployer,
    AccountsEmployerProvider,
    AccountsFileExchange,
    AccountsFileExchangeProvider,
    AccountsFileTracking,
    AccountsFileTrackingProvider,
    AccountsPayments,
    AccountsPaymentsProvider,
    AccountsTransactions,
    AccountsTransactionsProvider,
} from './components';

export const AccountsContent = () => {
    const {activeMenu} = useAccounts();

    return (
        <div className='kas-accounts-content'>
            <div hidden={activeMenu !== AccountItemType.File_Tracking}>
                <AccountsFileTrackingProvider>
                    <AccountsFileTracking />
                </AccountsFileTrackingProvider>
            </div>
            <div hidden={activeMenu !== AccountItemType.File_Exchange}>
                <AccountsFileExchangeProvider>
                    <AccountsFileExchange />
                </AccountsFileExchangeProvider>
            </div>
            <div hidden={activeMenu !== AccountItemType.Payments}>
                <AccountsPaymentsProvider>
                    <AccountsPayments />
                </AccountsPaymentsProvider>
            </div>
            <div hidden={activeMenu !== AccountItemType.Debit}>
                <AccountsDebitsProvider>
                    <AccountsDebits />
                </AccountsDebitsProvider>
            </div>
            <div hidden={activeMenu !== AccountItemType.Clearing}>
                <AccountsClearingProvider>
                    <AccountsClearing />
                </AccountsClearingProvider>
            </div>
            <div hidden={activeMenu !== AccountItemType.History}>
                <AccountsTransactionsProvider>
                    <AccountsTransactions />
                </AccountsTransactionsProvider>
            </div>
            <div hidden={activeMenu !== AccountItemType.Banks}>
                <AccountsBanksProvider>
                    <AccountsBanks />
                </AccountsBanksProvider>
            </div>
            <div hidden={activeMenu !== AccountItemType.Employer}>
                <AccountsEmployerProvider>
                    <AccountsEmployer />
                </AccountsEmployerProvider>
            </div>
            <div hidden={activeMenu !== AccountItemType.Deductions}>
                <AccountsDeductionsProvider>
                    <AccountsDeductions />
                </AccountsDeductionsProvider>
            </div>
        </div>
    );
};
