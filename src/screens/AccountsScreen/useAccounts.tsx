import React, {createContext, useContext, useEffect, useState} from 'react';
import {useHashHandler} from '@/hooks/useHashHandler';
import {AccountItemType} from './interfaces';
import {DataStateInterface, SelectModel} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {LookupDTO} from '@/models';

interface AccountsContextModel {
    activeMenu: AccountItemType;
    changeActiveMenu: (value: AccountItemType) => void;
    employersState: DataStateInterface<SelectModel<LookupDTO>[]>;
    loadEmployers: () => Promise<void>;
    txLockDateState: DataStateInterface<string>;
    transactionTypesState: DataStateInterface<SelectModel<string>[]>;
    loadTransactionTypes: () => Promise<void>;
    clearingTypesState: DataStateInterface<SelectModel<string>[]>;
    loadClearingTypes: () => Promise<void>;
}

const AccountsContext = createContext<AccountsContextModel | undefined>(undefined);

interface AccountProviderProps {
    children: React.ReactNode;
}

export const AccountsProvider = ({children}: AccountProviderProps) => {
    const {hashMatch, updateRoute} = useHashHandler();
    const [activeMenu, setActiveMenu] = useState<AccountItemType>(AccountItemType.File_Exchange);
    const [employersState, setEmployersState] = useState(getDefaultState<SelectModel<LookupDTO>[]>);
    const [txLockDateState, setTxLockDateStateState] = useState(getDefaultState<string>);
    const [transactionTypesState, setTransactionTypesState] = useState(
        getDefaultState<SelectModel<string>[]>,
    );
    const [clearingTypesState, setClearingTypesState] = useState(getDefaultState<SelectModel<string>[]>);

    const loadClearingTypes = async () => {
        const url = `/api/secured/ui/lookup/db/clearing-type`;

        setClearingTypesState(getLoadingState(clearingTypesState));
        const response = await apiRequest(url);
        setClearingTypesState(getLoadedState(response));
    };

    const loadTransactionTypes = async () => {
        const url = `/api/secured/ui/lookup/db/employer/tx`;

        setTransactionTypesState(getLoadingState(transactionTypesState));
        const response = await apiRequest(url);
        setTransactionTypesState(getLoadedState(response));
    };

    const loadEmployers = async () => {
        const url = `/api/secured/ui/lookup/employer?d=primary:active`;

        setEmployersState(getLoadingState(employersState));
        const response = await apiRequest(url);
        setEmployersState(getLoadedState(response));
    };

    const loadTxLockDate = async () => {
        const url = `/api/secured/common/environment/txlockdate`;

        setTxLockDateStateState(getLoadingState(txLockDateState));
        const response = await apiRequest(url);
        setTxLockDateStateState(getLoadedState(response));
    };

    const changeActiveMenu = (value: AccountItemType) => {
        updateRoute({hash: value, type: null, value: null});
    };

    const handleRouteChange = () => {
        const hash = hashMatch.hash;

        if (hash) {
            const hashMenuItem = hash.toUpperCase() as AccountItemType;
            const isValidMenuType = Object.values(AccountItemType).includes(hashMenuItem);

            if (isValidMenuType) {
                setActiveMenu(hashMenuItem);
            }
        }
    };

    useEffect(() => {
        handleRouteChange();
    }, [hashMatch]);

    useEffect(() => {
        loadTxLockDate().then();
    }, []);

    useEffect(() => {
        loadEmployers().then();
    }, []);

    const value: AccountsContextModel = {
        activeMenu,
        changeActiveMenu,
        employersState,
        loadEmployers,
        txLockDateState,
        transactionTypesState,
        loadTransactionTypes,
        clearingTypesState,
        loadClearingTypes,
    };

    return <AccountsContext.Provider value={value}>{children}</AccountsContext.Provider>;
};

export function useAccounts() {
    const context = useContext(AccountsContext);
    if (!context) {
        throw new Error('useAccounts must be used within AccountsProvider');
    }
    return context;
}
