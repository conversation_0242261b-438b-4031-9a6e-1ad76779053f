import React, {createContext, useContext, useEffect, useState, useMemo} from 'react';
import {useHashHandler} from '@/hooks/useHashHandler';
import {useSecured} from '@/hooks/useSecured';
import {ManagerItem} from './interfaces';

interface ManagerContextModel {
    activeMenu: ManagerItem | undefined;
    changeActiveMenu: (value: ManagerItem) => void;
    availableTabs: ManagerItem[];
}

const ManagerContext = createContext<ManagerContextModel | undefined>(undefined);

export const ManagerProvider = ({children}: {children: React.ReactNode}) => {
    const {hashMatch, updateRoute} = useHashHandler();
    const {hasAnyRole, isUserAdmin} = useSecured();

    const availableTabs = useMemo(() => {
        const tabs: ManagerItem[] = [];

        if (isUserAdmin) {
            return Object.values(ManagerItem);
        }

        if (hasAnyRole(['KASH_SUPPORT:MANAGER'])) {
            tabs.push(ManagerItem.SUPPORT);
        }

        if (hasAnyRole(['KASH_COLLECTIONS:MANAGER'])) {
            tabs.push(ManagerItem.STATS, ManagerItem.DATA_EXPORT, ManagerItem.PENDING_SETTLEMENTS);
        }

        if (hasAnyRole(['KASH_VERIFICATION:MANAGER'])) {
            tabs.push(ManagerItem.VERIFICATION);
        }

        return tabs;
    }, [hasAnyRole, isUserAdmin]);

    const [activeMenu, setActiveMenu] = useState<ManagerItem | undefined>();

    const changeActiveMenu = (value: ManagerItem) => {
        updateRoute({hash: value, type: null, value: null});
    };

    const handleRouteChange = () => {
        const hash = hashMatch.hash;

        if (hash) {
            const hashMenuItem = hash.toUpperCase() as ManagerItem;
            const isValidMenuType = Object.values(ManagerItem).includes(hashMenuItem);
            const isAllowedForUser = availableTabs.includes(hashMenuItem);

            if (isValidMenuType && isAllowedForUser) {
                setActiveMenu(hashMenuItem);
                return;
            }
        }

        if (availableTabs.length > 0) {
            setActiveMenu(availableTabs[0]);
            updateRoute({hash: availableTabs[0], type: null, value: null});
        }
    };

    useEffect(() => {
        handleRouteChange();
    }, [hashMatch, availableTabs]);

    const value: ManagerContextModel = {
        activeMenu,
        changeActiveMenu,
        availableTabs,
    };

    return <ManagerContext.Provider value={value}>{children}</ManagerContext.Provider>;
};

export function useManager() {
    const context = useContext(ManagerContext);
    if (!context) {
        throw new Error('useManager must be used within ManagerProvider');
    }
    return context;
}
