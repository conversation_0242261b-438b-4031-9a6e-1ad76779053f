import React from 'react';
import {Divider, Paper, Stack} from '@mui/material';
import Box from '@mui/material/Box';
import {PendingActionsHead, PendingActionsResult} from './components';
import {KasLoadingB<PERSON>D<PERSON>, KasLoading<PERSON><PERSON>r, KasSwitch, KasS<PERSON><PERSON>hen} from '@/components';
import {NoResultsView} from '@/views';
import {usePendingActions} from './usePendingActions';

export const PendingActions = () => {
    const {
        dataState: {loading, error, data},
    } = usePendingActions();

    return (
        <Paper elevation={0}>
            <Stack p={2} spacing={2}>
                <PendingActionsHead />
                <Divider />
                <Box position='relative'>
                    {loading && <KasLoadingBackDrop />}
                    <KasSwitch>
                        <KasSwitchWhen condition={!!error}>
                            <KasLoadingError view='contained' error={error} />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={!data}>
                            <NoResultsView text='Search result will appear here' />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={!!data}>
                            <PendingActionsResult />
                        </KasSwitchWhen>
                    </KasSwitch>
                </Box>
            </Stack>
        </Paper>
    );
};
