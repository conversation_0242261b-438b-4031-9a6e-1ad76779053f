import React from 'react';
import {usePendingActions} from '../../usePendingActions';
import {MakerCheckerRequestDTO} from '@/models/makerCheckerRequestDTO';
import {PendingActionsDetailsTable} from './tables';
import {TableView} from '@/views';

export const PendingActionsResult = () => {
    const {dataState} = usePendingActions();

    return (
        <TableView<MakerCheckerRequestDTO>
            loading={dataState.loading}
            error={dataState.error}
            data={dataState.data}
            columns={PendingActionsDetailsTable}
            tableName='Pending Actions'
        />
    );
};
