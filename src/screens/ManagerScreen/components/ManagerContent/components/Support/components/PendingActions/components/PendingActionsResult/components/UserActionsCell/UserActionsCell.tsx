import {Stack} from '@mui/material';
import {MakerCheckerRequestDTO} from '@/models/makerCheckerRequestDTO';
import {Check, Delete} from '@mui/icons-material';
import {ActionCell} from '@/components/table/cells';
import {usePendingActions} from './../../../../usePendingActions';

export const UserActionsCell = ({request}: {request: MakerCheckerRequestDTO}) => {
    const {onDecline, onApprove} = usePendingActions();
    const requestId = request.request_id;
    const showDecline = !!(requestId && (request.can_check || request.user_created));
    const showApprove = !!(request.can_check && !request.user_created && !request.user_approved);

    return (
        <Stack direction='row' alignItems='center' spacing={1}>
            {showApprove && (
                <ActionCell Icon={<Check titleAccess='Approve' />} onClick={() => onApprove(request)} />
            )}
            {showDecline && (
                <ActionCell Icon={<Delete titleAccess='Decline' />} onClick={() => onDecline(requestId)} />
            )}
        </Stack>
    );
};
