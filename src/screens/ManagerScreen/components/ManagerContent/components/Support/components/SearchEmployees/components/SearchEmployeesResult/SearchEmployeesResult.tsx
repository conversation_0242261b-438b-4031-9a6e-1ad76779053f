import React from 'react';
import {useSearchEmployees} from '../../useSearchEmployees';
import {SearchEmployeesDetailsTable} from './tables';
import {TableView} from '@/views';
import {MergeEmployeesTableModel} from '@/interfaces';

export const SearchEmployeesResult = () => {
    const {dataState} = useSearchEmployees();

    return (
        <TableView<MergeEmployeesTableModel>
            loading={dataState.loading}
            error={dataState.error}
            data={dataState.data}
            columns={SearchEmployeesDetailsTable}
            tableName='Pending Actions'
        />
    );
};
