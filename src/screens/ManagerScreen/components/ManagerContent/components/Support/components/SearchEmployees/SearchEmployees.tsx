import React from 'react';
import {Divider, Paper, Stack} from '@mui/material';
import Box from '@mui/material/Box';
import {SearchEmployeesHead, SearchEmployeesResult} from './components';
import {KasLoadingBackDrop, KasLoadingError, KasSwitch, KasSwitchWhen} from '@/components';
import {NoResultsView} from '@/views';
import {useSearchEmployees} from './useSearchEmployees';

export const SearchEmployees = () => {
    const {
        dataState: {loading, error, data},
    } = useSearchEmployees();

    return (
        <Paper elevation={0}>
            <Stack p={2} spacing={2}>
                <SearchEmployeesHead />
                <Divider />
                <Box position='relative'>
                    {loading && <KasLoadingBackDrop />}
                    <KasSwitch>
                        <KasSwitchWhen condition={!!error}>
                            <KasLoadingError view='contained' error={error} />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={!data}>
                            <NoResultsView text='Search result will appear here' />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={!!data}>
                            <SearchEmployeesResult />
                        </KasSwitchWhen>
                    </KasSwitch>
                </Box>
            </Stack>
        </Paper>
    );
};
