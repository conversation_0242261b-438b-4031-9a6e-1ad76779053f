import React from 'react';
import {Grid2} from '@mui/material';
import {useFormik} from 'formik';
import {KasDownloadButton} from '@/components';
import {usePlatformExport} from './../../usePlatformExport';

export const DefaultExportForm = ({path}: {path: string}) => {
    const {downloading, downloadData} = usePlatformExport();

    const onSubmit = async () => {
        const params = JSON.stringify({path});

        await downloadData(params);
    };

    const formik = useFormik({
        initialValues: {},
        onSubmit,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2}>
                <Grid2 size={3}>
                    <KasDownloadButton submitting={downloading} disabled={!formik.isValid || downloading} />
                </Grid2>
            </Grid2>
        </form>
    );
};
