import React, {useMemo, useState} from 'react';
import {Grid2, Paper, SelectChangeEvent, Typography} from '@mui/material';
import {KasSelect} from '@/components';
import Box from '@mui/material/Box';
import {SelectModel} from '@/interfaces';
import {PLATFORM_OPTIONS} from './data';
import MenuItem from '@mui/material/MenuItem';
import {PlatformExportItem} from './interfaces';
import {DefaultExportForm, LoanStatisticsForm, MissingRecordingsForm} from './components';
import {usePlatformExport} from './usePlatformExport';

export const PlatformExport = () => {
    const {downloading} = usePlatformExport();
    const [selectedPlatform, setSelectedPlatform] = useState<SelectModel<PlatformExportItem>>(
        PLATFORM_OPTIONS[0],
    );

    const handleChangeVintage = (event: SelectChangeEvent) => {
        const newValue = event.target.value as string;
        const newSelectedPlatform = PLATFORM_OPTIONS.find(({value}) => value === newValue);

        newSelectedPlatform && setSelectedPlatform(newSelectedPlatform);
    };

    const renderForm = useMemo(() => {
        switch (selectedPlatform.value) {
            case PlatformExportItem.LOAN_STATISTICS:
                return <LoanStatisticsForm />;
            case PlatformExportItem.LIVEVOX_RECORD:
                return <MissingRecordingsForm />;
            case PlatformExportItem.LIVEVOX_CAMPAIGN:
                return <DefaultExportForm path='/support/secured/collections/export/livevox' />;
            case PlatformExportItem.LIVEVOX_HIT:
                return (
                    <DefaultExportForm path='/support/secured/collections/export/report/livevox_no_hit/download' />
                );
            default:
                return null;
        }
    }, [selectedPlatform]);

    return (
        <Paper elevation={0}>
            <Box p={2}>
                <Grid2 container spacing={2}>
                    <Grid2 size={3} alignSelf='center'>
                        <Typography variant='h6'>Platform Export</Typography>
                    </Grid2>
                    <Grid2 size={2}>
                        <KasSelect
                            labelId='kas-platform-export-select'
                            value={selectedPlatform.value}
                            label='Platform'
                            disabled={downloading}
                            onChange={handleChangeVintage}>
                            {PLATFORM_OPTIONS.map((item) => (
                                <MenuItem key={item.id} value={item.value}>
                                    {item.label}
                                </MenuItem>
                            ))}
                        </KasSelect>
                    </Grid2>
                    <Grid2 size={6}>{renderForm}</Grid2>
                </Grid2>
            </Box>
        </Paper>
    );
};
