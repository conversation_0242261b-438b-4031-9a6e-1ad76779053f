import React, {createContext, useContext, useState} from 'react';
import {useDownloadBlob} from '@/hooks/useDownloadBlob';

interface PlatformExportContextModel {
    downloading: boolean;
    downloadData: (params: string) => Promise<void>;
}

const PlatformExportContext = createContext<PlatformExportContextModel | undefined>(undefined);

export const PlatformExportProvider = ({children}: {children: React.ReactNode}) => {
    const {getBlob} = useDownloadBlob();
    const [downloading, setDownloading] = useState(false);

    const downloadData = async (params: string) => {
        setDownloading(true);
        await getBlob(params, '/api/secured/download-collections-blob');
        setDownloading(false);
    };

    const value: PlatformExportContextModel = {downloading, downloadData};

    return <PlatformExportContext.Provider value={value}>{children}</PlatformExportContext.Provider>;
};

export function usePlatformExport() {
    const context = useContext(PlatformExportContext);
    if (!context) {
        throw new Error('usePlatformExport must be used within PlatformExportProvider');
    }
    return context;
}
