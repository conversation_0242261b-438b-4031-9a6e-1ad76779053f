import React from 'react';
import {Button, ButtonGroup, Stack} from '@mui/material';
import {OutcomeCollectionStatisticsModel} from '@/interfaces';
import {<PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, <PERSON>ltip, Legend, ResponsiveContainer, CartesianGrid} from 'recharts';
import {toCurrency, toCurrencyNoDecimals} from '@/utils/FormatUtils';
import {KasCustomLegend, KasCustomTooltip} from '@/components';
import {CollectionStatisticsChartView, useCollectionStatisticChart} from './useCollectionStatisticChart';

export const CollectionStatisticsChart = ({data}: {data: OutcomeCollectionStatisticsModel[]}) => {
    const {activeView, setActiveView, chartData, hiddenKeys, uniqBars, handleLegendClick, isAmountView} =
        useCollectionStatisticChart(data);

    return (
        <Stack spacing={2}>
            <ButtonGroup>
                {Object.entries(CollectionStatisticsChartView).map(([key, value]) => (
                    <Button
                        key={key}
                        variant={activeView === value ? 'contained' : 'text'}
                        onClick={() => setActiveView(value)}>
                        {value}
                    </Button>
                ))}
            </ButtonGroup>
            <ResponsiveContainer width='100%' height={400}>
                <BarChart data={chartData} margin={{top: 20, right: 30, left: 20, bottom: 5}}>
                    <CartesianGrid strokeDasharray='3 3' />
                    <XAxis dataKey='date' style={{fontSize: 'var(--small-text-size)'}} />
                    <YAxis
                        tickFormatter={isAmountView ? toCurrencyNoDecimals : undefined}
                        style={{fontSize: 'var(--small-text-size)'}}
                    />
                    <Tooltip
                        cursor={{fill: 'var(--color-divider)', opacity: 0.5}}
                        content={
                            <KasCustomTooltip
                                labelKey='date'
                                valueFormatter={isAmountView ? toCurrency : undefined}
                            />
                        }
                    />
                    <Legend
                        content={() => (
                            <KasCustomLegend
                                payload={uniqBars.map(({key, color, value}) => ({
                                    value,
                                    type: 'rect',
                                    id: key,
                                    color,
                                    dataKey: key,
                                }))}
                                hiddenKeys={hiddenKeys}
                                onClick={handleLegendClick}
                            />
                        )}
                    />
                    {uniqBars
                        .filter((a) => !hiddenKeys.includes(a.key))
                        .map(({key, color, value}) => (
                            <Bar key={key} dataKey={key} stackId='bar' name={value} fill={color} />
                        ))}
                </BarChart>
            </ResponsiveContainer>
        </Stack>
    );
};
