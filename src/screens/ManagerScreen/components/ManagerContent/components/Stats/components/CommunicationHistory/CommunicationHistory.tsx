import React from 'react';
import {Divider, Paper} from '@mui/material';
import Box from '@mui/material/Box';
import {CommunicationHistoryCharts, CommunicationHistoryHead} from './components';
import {KasLoadingB<PERSON>D<PERSON>, Kas<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, KasSwitch, Kas<PERSON><PERSON><PERSON><PERSON>} from '@/components';
import {NoResultsView} from '@/views';
import {useStats} from './../../useStats';

export const CommunicationHistory = () => {
    const {
        communicationHistoryState: {data, error, loading},
    } = useStats();

    return (
        <Paper elevation={0}>
            <Box p={2}>
                <CommunicationHistoryHead />
                <Divider />
                <Box position='relative' pt={2}>
                    {loading && <KasLoadingBackDrop />}
                    <KasSwitch>
                        <KasSwitchWhen condition={!!error}>
                            <KasLoadingError view='contained' error={error} />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={!data}>
                            <NoResultsView text='Search result will appear here' />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={!!data}>
                            <CommunicationHistoryCharts />
                        </KasSwitchWhen>
                    </KasSwitch>
                </Box>
            </Box>
        </Paper>
    );
};
