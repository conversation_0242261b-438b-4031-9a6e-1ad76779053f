import React from 'react';
import {Stack} from '@mui/material';
import {OutcomeStatisticsModel} from '@/interfaces';
import {XAxis, YA<PERSON><PERSON>, Toolt<PERSON>, Legend, ResponsiveContainer, CartesianGrid, Bar, BarChart} from 'recharts';
import {KasCustomLegend, KasCustomTooltip} from '@/components';
import {toCurrency, toCurrencyNoDecimals} from '@/utils/FormatUtils';
import {useSettlementStatisticChart} from './useSettlementStatisticChart';

export const SettlementStatisticsChart = ({data}: {data: OutcomeStatisticsModel}) => {
    const {chartData, hiddenKeys, uniqBars, handleLegendClick} = useSettlementStatisticChart(
        data.settlement_receivable_stats,
        data.settlement_received_stats,
    );

    return (
        <Stack spacing={2}>
            <ResponsiveContainer width='100%' height={400}>
                <BarChart data={chartData} margin={{top: 20, right: 30, left: 20, bottom: 5}} stackOffset='sign'>
                    <CartesianGrid strokeDasharray='3 3' />
                    <XAxis dataKey='date' style={{fontSize: 'var(--small-text-size)'}} />
                    <YAxis
                        tickFormatter={toCurrencyNoDecimals}
                        style={{fontSize: 'var(--small-text-size)'}}
                    />
                    <Tooltip
                        cursor={{fill: 'var(--color-divider)', opacity: 0.5}}
                        content={
                            <KasCustomTooltip labelKey='date' valueFormatter={toCurrency} showTotal={false} />
                        }
                    />
                    <Legend
                        content={() => (
                            <KasCustomLegend
                                payload={uniqBars.map(({key, color, value}) => ({
                                    value,
                                    type: 'rect',
                                    id: key,
                                    color,
                                    dataKey: key,
                                }))}
                                hiddenKeys={hiddenKeys}
                                onClick={handleLegendClick}
                            />
                        )}
                    />
                    {uniqBars
                        .filter((a) => !hiddenKeys.includes(a.key))
                        .map(({key, color, value}) => (
                            <Bar key={key} dataKey={key} stackId='bar' name={value} fill={color} />
                        ))}
                </BarChart>
            </ResponsiveContainer>
        </Stack>
    );
};
