import React, {createContext, useContext, useState} from 'react';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {
    CommunicationHistoryModel,
    Completable,
    DataStateInterface,
    DayJsRangeModel,
    OutcomeStatisticsModel,
} from '@/interfaces';
import {getDateRange} from '@/utils/DateUtils';

interface StatsContextModel {
    dateParams: DayJsRangeModel;
    communicationHistoryState: DataStateInterface<CommunicationHistoryModel>;
    loadCommunicationHistory: (date: string) => Promise<void>;
    statisticState: DataStateInterface<OutcomeStatisticsModel>;
}

const StatsContext = createContext<StatsContextModel | undefined>(undefined);

export const StatsProvider = ({children}: {children: React.ReactNode}) => {
    const [communicationHistoryState, setStatsState] = useState(getDefaultState<CommunicationHistoryModel>);
    const [statisticState, setStatisticState] = useState(getDefaultState<OutcomeStatisticsModel>);
    const [dateParams, setDateParams] = useState(getDateRange());

    const loadCommunicationHistory = async (date: string) => {
        const curParams = getDateRange(date);
        const url = '/api/secured/manager/stats/history';
        const body = JSON.stringify({
            start_time: curParams.start?.format('YYYY-MM-DD HH:mm:ss'),
            end_time: curParams.end?.format('YYYY-MM-DD HH:mm:ss'),
            summary: true,
            types: [],
        });

        setDateParams(curParams);
        loadStatistic(curParams).then();
        setStatsState(getLoadingState(communicationHistoryState));
        const response = await apiRequest(url, {method: 'post', body});
        setStatsState(getLoadedState(response));
    };

    const loadStatistic = async (params: DayJsRangeModel) => {
        const dateFrom = params.start?.format('YYYYMMDD');
        const dateTo = params.end?.format('YYYYMMDD');
        const url = `/api/secured/manager/stats/outcome?dateFrom=${dateFrom}&dateTo=${dateTo}`;

        setStatisticState(getLoadingState(statisticState));
        const response: Completable<OutcomeStatisticsModel> = await apiRequest(url);
        setStatisticState(getLoadedState(response));
    };

    const value: StatsContextModel = {
        dateParams,
        communicationHistoryState,
        loadCommunicationHistory,
        statisticState,
    };

    return <StatsContext.Provider value={value}>{children}</StatsContext.Provider>;
};

export function useStats() {
    const context = useContext(StatsContext);
    if (!context) {
        throw new Error('useStats must be used within StatsProvider');
    }
    return context;
}
