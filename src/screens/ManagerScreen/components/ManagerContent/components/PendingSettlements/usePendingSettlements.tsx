import React, {createContext, useContext, useState} from 'react';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {CollectionSettlementLoanModel, Completable, DataStateInterface, SelectModel} from '@/interfaces';
import {CollectionSettlementActionModalProps, SettlementsListFormValues} from './interfaces';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {useSnackbar} from '@/hooks/useSnackbar';
import {DEFAULT_FILTER_PARAMS} from './data';

interface PendingSettlementsContextModel {
    settlementLoansState: DataStateInterface<CollectionSettlementLoanModel[]>;
    loadSettlementLoans: (params?: SettlementsListFormValues) => Promise<void>;
    collectionAgentsState: DataStateInterface<SelectModel<string>[]>;
    loadCollectionAgents: () => Promise<void>;
    openActionModal: CollectionSettlementActionModalProps | null;
    setOpenActionModal: (value: CollectionSettlementActionModalProps | null) => void;
    flagSettlementLoan: (loanId: number | string, expiryDate?: string) => Promise<Completable<boolean>>;
    filterParams: SettlementsListFormValues;
}

const PendingSettlementsContext = createContext<PendingSettlementsContextModel | undefined>(undefined);

export const PendingSettlementsProvider = ({children}: {children: React.ReactNode}) => {
    const {showMessage} = useSnackbar();
    const [collectionAgentsState, setCollectionAgentsState] = useState(
        getDefaultState<SelectModel<string>[]>,
    );
    const [settlementLoansState, setSettlementLoansState] = useState(
        getDefaultState<CollectionSettlementLoanModel[]>,
    );
    const [openActionModal, setOpenActionModal] = useState<CollectionSettlementActionModalProps | null>(null);
    const [filterParams, setFilterParams] = useState<SettlementsListFormValues>(DEFAULT_FILTER_PARAMS);

    const loadSettlementLoans = async (params?: SettlementsListFormValues) => {
        const curParams = params || filterParams;
        const url = '/api/secured/manager/pending-settlements/loan';
        const body = JSON.stringify({
            assigned_user_id: curParams.assignedUser,
            dlq_amount: curParams.delinquentAmountRange,
            dlq_days: curParams.delinquentDaysRange,
            loan_balance: curParams.loanBalanceRange,
        });

        setFilterParams(curParams);
        setSettlementLoansState(getLoadingState(settlementLoansState));
        const response = await apiRequest(url, {method: 'post', body});
        setSettlementLoansState(getLoadedState(response));
    };

    const updateVisibleSettlementLoans = async (loanId: number | string) => {
        const isVisibleLoan = settlementLoansState.data?.some(
            (item) => item.loan_id.toString() === loanId.toString(),
        );

        if (isVisibleLoan) {
            await loadSettlementLoans();
        }
    };

    const loadCollectionAgents = async () => {
        const url = '/api/secured/manager/pending-settlements/collection-agents';

        setCollectionAgentsState(getLoadingState(collectionAgentsState));
        const response = await apiRequest(url);
        setCollectionAgentsState(getLoadedState(response));
    };

    const flagSettlementLoan = async (loanId: number | string, expiryDate?: string) => {
        const body = JSON.stringify({
            loan_id: loanId,
            ...(expiryDate && {expiry_date: expiryDate}),
        });

        const response: Completable<boolean> = await apiRequest(
            '/api/secured/manager/pending-settlements/loan/flag',
            {
                method: 'post',
                body,
            },
        );

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            showMessage(`Updated expiry date for loan: ${loanId}`, 'success');
            updateVisibleSettlementLoans(loanId).then();
        }

        return response;
    };

    const value: PendingSettlementsContextModel = {
        settlementLoansState,
        loadSettlementLoans,
        collectionAgentsState,
        loadCollectionAgents,
        openActionModal,
        setOpenActionModal,
        flagSettlementLoan,
        filterParams,
    };

    return <PendingSettlementsContext.Provider value={value}>{children}</PendingSettlementsContext.Provider>;
};

export function usePendingSettlements() {
    const context = useContext(PendingSettlementsContext);
    if (!context) {
        throw new Error('usePendingSettlements must be used within PendingSettlementsProvider');
    }
    return context;
}
