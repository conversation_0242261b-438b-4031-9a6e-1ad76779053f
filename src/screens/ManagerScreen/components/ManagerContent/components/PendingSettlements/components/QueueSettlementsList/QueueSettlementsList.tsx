import React from 'react';
import {Divider, Paper, Typography, Button, Box as MuiBox} from '@mui/material';
import Box from '@mui/material/Box';
import {QueueSettlementsListResult} from './components';
import {SettlementApprovalModal} from '../QueueSettlementsModal/components';
import {useQueueSettlements} from '@/screens/ManagerScreen/components/ManagerContent/components/PendingSettlements/useQueueSettlements';

export const QueueSettlementsList = () => {
    const {queueSettlementsState, loadQueueSettlements} = useQueueSettlements();

    return (
        <>
            <Paper elevation={0}>
                <Box p={2}>
                    <MuiBox display='flex' justifyContent='space-between' alignItems='center' mb={2}>
                        <Typography variant='h6'>
                            Queued Settlement Offers
                        </Typography>
                        <Button 
                            variant='outlined' 
                            onClick={loadQueueSettlements}
                            disabled={queueSettlementsState.loading}
                            data-testid='queue-settlements-refresh'
                        >
                            Refresh
                        </Button>
                    </MuiBox>
                    <Divider />
                    <Box position='relative' pt={2}>
                        <QueueSettlementsListResult />
                    </Box>
                </Box>
            </Paper>
            <SettlementApprovalModal />
        </>
    );
}; 