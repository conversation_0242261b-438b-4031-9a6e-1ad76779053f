import React, {useState, useEffect} from 'react';
import {KasModal, KasL<PERSON>ding, KasSwitch, KasSwitchWhen} from '@/components';
import {Box} from '@mui/material';
import {apiRequest} from '@/utils/AxiosUtils';
import {SettlementOfferModel} from '@/interfaces/settlement-offer.interface';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {useQueueSettlements} from '@/screens/ManagerScreen/components/ManagerContent/components/PendingSettlements/components/QueueSettlementsList/useQueueSettlements';
import {SettlementApprovalFormValues} from './schema';
import {useSnackbar} from '@/hooks/useSnackbar';
import {SettlementApprovalForm} from './components/SettlementApprovalForm';

export const SettlementApprovalModal = () => {
    const {openModalQueueId, setOpenModalQueueId, loadQueueSettlements} = useQueueSettlements();
    const {showMessage} = useSnackbar();
    const [settlementOffer, setSettlementOffer] = useState<SettlementOfferModel | null>(null);
    const [loading, setLoading] = useState(false);

    // Load settlement offer details when openModalQueueId changes
    useEffect(() => {
        if (openModalQueueId) {
            loadSettlementOffer();
        } else {
            setSettlementOffer(null);
        }
    }, [openModalQueueId]);

    const loadSettlementOffer = async () => {
        if (!openModalQueueId) return;
        
        setLoading(true);
        try {
            const url = `/api/secured/manager/settlement/offer/queue/${openModalQueueId}`;
            const response = await apiRequest(url);
            if (response.value) {
                setSettlementOffer(response.value);
            }
        } catch (error) {
            console.error('Failed to load settlement offer:', error);
        } finally {
            setLoading(false);
        }
    };

    const onSubmit = async (values: SettlementApprovalFormValues) => {
        if (!openModalQueueId) return;
        
        const url = `/api/secured/manager/settlement/offer/queue/${openModalQueueId}`;

        const ratioAsDecimal = (parseFloat(values.ratio) / 100).toFixed(4);
        const response = await apiRequest(url, {
            method: 'put',
            body: JSON.stringify({
                fixed_amount: values.fixedAmount,
                ratio: ratioAsDecimal,
            }),
        });

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            showMessage('Settlement offer approved successfully', 'success');
            await loadQueueSettlements();
            handleClose();
        }
    };

    const handleClose = () => {
        setOpenModalQueueId(null);
    };

    return (
        <KasModal
            title='Approve Settlement Offer'
            size='small'
            open={!!openModalQueueId}
            onClose={handleClose}>
            <KasSwitch>
                <KasSwitchWhen condition={loading}>
                    <Box display='flex' justifyContent='center' alignItems='center' minHeight={100}>
                        <KasLoading size={32} />
                    </Box>
                </KasSwitchWhen>
                <KasSwitchWhen condition={!loading && !!settlementOffer}>
                    <SettlementApprovalForm
                        settlementOffer={settlementOffer!}
                        onSubmit={onSubmit}
                        onCancel={handleClose}
                    />
                </KasSwitchWhen>
            </KasSwitch>
        </KasModal>
    );
};
