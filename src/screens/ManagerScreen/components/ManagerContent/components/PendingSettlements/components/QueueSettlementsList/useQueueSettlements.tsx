import React, {createContext, useContext, useState, useEffect} from 'react';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {Completable, DataStateInterface} from '@/interfaces';
import {SettlementOfferModel} from '@/interfaces/settlement-offer.interface';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {useSnackbar} from '@/hooks/useSnackbar';

interface QueueSettlementsContextModel {
    queueSettlementsState: DataStateInterface<SettlementOfferModel[]>;
    loadQueueSettlements: () => Promise<void>;
    openModalQueueId: number | null;
    setOpenModalQueueId: (id: number | null) => void;
    declineSettlementOffer: (queueId: number) => Promise<Completable<boolean>>;
}

const QueueSettlementsContext = createContext<QueueSettlementsContextModel | undefined>(undefined);

export const QueueSettlementsProvider = ({children}: {children: React.ReactNode}) => {
    const {showMessage} = useSnackbar();
    const [queueSettlementsState, setQueueSettlementsState] = useState(
        getDefaultState<SettlementOfferModel[]>,
    );
    const [openModalQueueId, setOpenModalQueueId] = useState<number | null>(null);

    // Load queue settlements on mount
    useEffect(() => {
        loadQueueSettlements();
    }, []);

    const loadQueueSettlements = async () => {
        const url = '/api/secured/manager/settlement/offer/queue';

        setQueueSettlementsState(getLoadingState(queueSettlementsState));
        const response = await apiRequest(url);
        setQueueSettlementsState(getLoadedState(response));
    };

    const declineSettlementOffer = async (queueId: number) => {
        const url = `/api/secured/manager/settlement/offer/queue/${queueId}`;

        const response: Completable<boolean> = await apiRequest(url, {
            method: 'delete',
        });

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            showMessage('Settlement offer declined successfully', 'success');
            await loadQueueSettlements();
        }

        return response;
    };

    const value: QueueSettlementsContextModel = {
        queueSettlementsState,
        loadQueueSettlements,
        openModalQueueId,
        setOpenModalQueueId,
        declineSettlementOffer,
    };

    return <QueueSettlementsContext.Provider value={value}>{children}</QueueSettlementsContext.Provider>;
};

export function useQueueSettlements() {
    const context = useContext(QueueSettlementsContext);
    if (!context) {
        throw new Error('useQueueSettlements must be used within QueueSettlementsProvider');
    }
    return context;
} 