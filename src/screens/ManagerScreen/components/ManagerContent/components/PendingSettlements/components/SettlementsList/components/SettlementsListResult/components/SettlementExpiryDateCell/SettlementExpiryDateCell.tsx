import React, {useMemo} from 'react';
import {CollectionSettlementLoanExpandModel} from './../../../../../../interfaces';
import dayjs from 'dayjs';
import {useTheme} from '@mui/material';

export const SettlementExpiryDateCell = ({data}: {data: CollectionSettlementLoanExpandModel}) => {
    const {palette} = useTheme();

    const color = useMemo(() => {
        const diff = dayjs(data.expiry_date).diff(dayjs(), 'days');

        return diff <= 7 ? palette.error.main : 'inherit';
    }, [data.expiry_date]);

    return <span style={{color}}>{data.date}</span>;
};
