import React from 'react';
import {Stack} from '@mui/material';
import {Check, Close} from '@mui/icons-material';
import {ActionCell} from '@/components/table/cells';
import {useGlobalModal} from '@/components/KasGlobalModals';
import {GlobalModal} from '@/components/KasGlobalModals/interfaces';
import {useQueueSettlements} from '@/screens/ManagerScreen/components/ManagerContent/components/PendingSettlements/useQueueSettlements';

interface QueueSettlementActionCellProps {
    queueId: number | null;
}

export const QueueSettlementActionCell = ({queueId}: QueueSettlementActionCellProps) => {
    const { setOpenModalQueueId, declineSettlementOffer} = useQueueSettlements();
    const {showGlobalModal} = useGlobalModal();

    const handleDecline = () => {
        if (!queueId) return;
        
        showGlobalModal({
            type: GlobalModal.Confirm,
            props: {
                title: 'Decline Settlement Offer',
                onSubmit: async () => {
                    await declineSettlementOffer(queueId);
                    return true;
                },
                onClose: () => {},
            },
        });
    };

    const handleApprove = () => {
        if (queueId) {
            setOpenModalQueueId(queueId);
        }
    };

    return (
        <Stack direction='row' justifyContent='center' spacing={1}>
            <ActionCell
                Icon={<Check color='success' />}
                onClick={handleApprove}
            />
            <ActionCell
                Icon={<Close color='error' />}
                onClick={handleDecline}
            />
        </Stack>
    );
}; 