import React from 'react';
import {Divider, Paper, Typography} from '@mui/material';
import Box from '@mui/material/Box';
import {SettlementsListForm, SettlementsListResult} from './components';

export const SettlementsList = () => {
    return (
        <Paper elevation={0}>
            <Box p={2}>
                <Typography variant='h6' mb={2}>
                    Settlements List
                </Typography>
                <SettlementsListForm />
                <Divider />
                <Box position='relative' pt={2}>
                    <SettlementsListResult />
                </Box>
            </Box>
        </Paper>
    );
};
