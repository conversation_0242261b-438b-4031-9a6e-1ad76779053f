import React, {useMemo} from 'react';
import {KasModal} from '@/components';
import {CollectionSettlementAction} from '../../interfaces';
import {UnflagSettlementLoanForm, UpdateSettlementLoanForm} from './components';
import {usePendingSettlements} from '../../usePendingSettlements';

export const PendingSettlementsModal = () => {
    const {openActionModal, setOpenActionModal} = usePendingSettlements();

    const renderContent = useMemo(() => {
        switch (openActionModal?.type) {
            case CollectionSettlementAction.DELETE:
                return <UnflagSettlementLoanForm {...openActionModal.props} />;
            case CollectionSettlementAction.UPDATE_DUE_DATE:
                return <UpdateSettlementLoanForm {...openActionModal.props} />;
            default:
                return null;
        }
    }, [openActionModal]);

    return (
        <KasModal
            title={openActionModal?.type || ''}
            size='small'
            open={!!openActionModal}
            onClose={() => setOpenActionModal(null)}>
            {renderContent}
        </KasModal>
    );
};
