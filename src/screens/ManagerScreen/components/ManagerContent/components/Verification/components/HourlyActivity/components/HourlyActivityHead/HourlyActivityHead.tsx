import React, {useEffect, useState} from 'react';
import {Button, Grid2, Typography} from '@mui/material';
import {HourlyActivityValues, validationSchema} from './schema';
import {useFormik} from 'formik';
import {useHourlyActivity} from '../../useHourlyActivity';
import {KasDatePickerFormField} from '@/components';
import dayjs from 'dayjs';
import {ManagerItem} from '@/screens/ManagerScreen/interfaces';
import {useManager} from '@/screens/ManagerScreen/useManager';

export const HourlyActivityHead = () => {
    const {activeMenu} = useManager();
    const {loadData, dataState} = useHourlyActivity();
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async (values: HourlyActivityValues) => {
        const date = dayjs(values.date).format('YYYY-MM-DD');
        setSubmitting(true);
        await loadData(date);
        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            date: dayjs().format('YYYY-MM-DD'),
        },
        onSubmit,
        validationSchema,
    });

    useEffect(() => {
        if (activeMenu === ManagerItem.VERIFICATION && !dataState.data) {
            formik.handleSubmit();
        }
    }, [activeMenu, dataState.data]);

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} alignItems='center'>
                <Grid2 size={3}>
                    <Typography variant='h6'>Hourly Activity</Typography>
                </Grid2>
                <Grid2 size={3}>
                    <KasDatePickerFormField formik={formik} name='date' label='Date' disabled={submitting} />
                </Grid2>
                <Grid2 size={3} ml='auto'>
                    <Button
                        fullWidth
                        variant='contained'
                        type='submit'
                        loading={submitting}
                        disabled={!formik.isValid || submitting}>
                        Apply
                    </Button>
                </Grid2>
            </Grid2>
        </form>
    );
};
