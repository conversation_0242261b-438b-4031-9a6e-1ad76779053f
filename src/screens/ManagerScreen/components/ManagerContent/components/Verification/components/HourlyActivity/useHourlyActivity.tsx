import React, {createContext, useContext, useState} from 'react';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {DataStateInterface, IGoogleAnalyticsRow} from '@/interfaces';

interface HourlyActivityContextModel {
    dataState: DataStateInterface<IGoogleAnalyticsRow[]>;
    loadData: (date: string) => Promise<void>;
}

const HourlyActivityContext = createContext<HourlyActivityContextModel | undefined>(undefined);

export const HourlyActivityProvider = ({children}: {children: React.ReactNode}) => {
    const [dataState, setDataState] = useState(getDefaultState<IGoogleAnalyticsRow[]>);

    const loadData = async (date: string) => {
        const searchParams = new URLSearchParams({
            startDate: date,
            endDate: date,
            dimensions: 'hour,eventName',
            metrics: 'eventCount',
            actions: 'approve,decline,send-email',
        }).toString();
        const url = `/api/analytics?${searchParams}`;

        setDataState(getLoadingState(dataState));
        const response = await apiRequest(url);
        setDataState(getLoadedState(response));
    };

    const value: HourlyActivityContextModel = {
        dataState,
        loadData,
    };

    return <HourlyActivityContext.Provider value={value}>{children}</HourlyActivityContext.Provider>;
};

export function useHourlyActivity() {
    const context = useContext(HourlyActivityContext);
    if (!context) {
        throw new Error('useHourlyActivity must be used within HourlyActivityProvider');
    }
    return context;
}
