import React, {useMemo, useState} from 'react';
import {TableView} from '@/views';
import {useActivityStatistic} from '../../useActivityStatistic';
import {ActivityStatisticModel} from './../../interfaces';
import {Button, ButtonGroup} from '@mui/material';
import {QueueActivityColumns, AgentActivityColumns, DateActivityColumns} from './tables';
import dayjs from 'dayjs';

export enum TimeActivityResultView {
    Agent = 'Agent',
    Queue = 'Queue',
    Date = 'Date',
}

export const ActivityStatisticResult = () => {
    const {dataState} = useActivityStatistic();
    const [activeView, setActiveView] = useState<TimeActivityResultView>(TimeActivityResultView.Agent);

    const groupActivityData = (
        data: typeof dataState.data,
        groupBy: 'agent' | 'queue' | 'date',
    ): ActivityStatisticModel[] => {
        const statsMap = new Map<string, ActivityStatisticModel>();

        (data || []).forEach((row) => {
            const agent = row.dimensionValues?.[0]?.value || 'Unknown';
            const action = row.dimensionValues?.[1]?.value;
            const queue = row.dimensionValues?.[2]?.value || 'Unknown';
            const dateRow = row.dimensionValues?.[3]?.value;
            const date = dateRow ? dayjs(dateRow).format('YYYY-MM-DD') : 'Unknown';
            const count = Number(row.metricValues?.[0]?.value || '0');
            const time = Number(row.metricValues?.[1]?.value || '0');
            const key = groupBy === 'agent' ? agent : queue;
            const model: ActivityStatisticModel = {
                ...(groupBy === 'agent' && {agent}),
                ...(groupBy === 'queue' && {queue}),
                ...(groupBy === 'date' && {date}),
                actions: {
                    approve: {count: 0, time: 0},
                    decline: {count: 0, time: 0},
                    sendEmail: {count: 0, time: 0},
                    incompleteExit: {count: 0, time: 0},
                },
            };

            if (!statsMap.has(key)) {
                statsMap.set(key, model);
            }

            const entry = statsMap.get(key)!;

            if (action === 'approve') {
                entry.actions.approve.count += count;
                entry.actions.approve.time = time;
            } else if (action === 'decline') {
                entry.actions.decline.count += count;
                entry.actions.decline.time = time;
            } else if (action === 'send-email') {
                entry.actions.sendEmail.count += count;
                entry.actions.sendEmail.time = time;
            }
            if (action === 'incomplete-exit') {
                entry.actions.incompleteExit.count += count;
                entry.actions.incompleteExit.time = time;
            }
        });

        return Array.from(statsMap.values());
    };

    const agentData = useMemo(() => groupActivityData(dataState.data, 'agent'), [dataState.data]);
    const queueData = useMemo(() => groupActivityData(dataState.data, 'queue'), [dataState.data]);
    const dateData = useMemo(() => groupActivityData(dataState.data, 'date'), [dataState.data]);

    return (
        <TableView<ActivityStatisticModel>
            withTableActions
            loading={dataState.loading}
            error={dataState.error}
            data={
                activeView === TimeActivityResultView.Agent
                    ? agentData
                    : activeView === TimeActivityResultView.Queue
                      ? queueData
                      : dateData
            }
            columns={
                activeView === TimeActivityResultView.Agent
                    ? AgentActivityColumns
                    : activeView === TimeActivityResultView.Queue
                      ? QueueActivityColumns
                      : DateActivityColumns
            }
            tableName='Agent Activity'
            tableActions={
                <ButtonGroup>
                    {Object.entries(TimeActivityResultView).map(([key, value]) => (
                        <Button
                            key={key}
                            variant={activeView === value ? 'contained' : 'text'}
                            onClick={() => setActiveView(value)}>
                            {value}
                        </Button>
                    ))}
                </ButtonGroup>
            }
        />
    );
};
