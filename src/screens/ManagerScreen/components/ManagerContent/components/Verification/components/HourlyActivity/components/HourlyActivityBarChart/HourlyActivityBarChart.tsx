import React, {useMemo, useState} from 'react';
import {<PERSON><PERSON><PERSON>, Bar, <PERSON>Axis, <PERSON><PERSON><PERSON>s, CartesianGrid, Tooltip, Responsive<PERSON><PERSON><PERSON>, <PERSON>} from 'recharts';
import {Box} from '@mui/material';
import {useHourlyActivity} from '../../useHourlyActivity';
import {KasCustomLegend, KasCustomTooltip} from '@/components';
import {HourlyActivityModel} from './../../interfaces';

const ACTIONS = [
    {key: 'approve', label: 'Approve', color: '#4caf50'},
    {key: 'decline', label: 'Decline', color: '#f44336'},
    {key: 'sendEmail', label: 'Send Email', color: '#2196f3'},
];

const ALL_HOURS = Array.from({length: 24}, (_, i) => i.toString().padStart(2, '0'));

export const HourlyActivityBarChart = () => {
    const {dataState} = useHourlyActivity();
    const [hiddenKeys, setHiddenKeys] = useState<string[]>([]);

    const handleLegendClick = (key: string) => {
        setHiddenKeys((prev) => (prev.includes(key) ? prev.filter((k) => k !== key) : [...prev, key]));
    };

    const data: HourlyActivityModel[] = useMemo(() => {
        const statsMap = new Map<string, HourlyActivityModel>();
        (dataState.data || []).forEach((row) => {
            if (!row.dimensionValues || row.dimensionValues.length < 2) return;
            const rawHour = row.dimensionValues[0]?.value;
            const hour = rawHour?.padStart(2, '0');
            let action = row.dimensionValues[1]?.value;
            if (action === 'send-email') action = 'sendEmail';
            if (!action || !['approve', 'decline', 'sendEmail'].includes(action)) return;
            const typedAction = action as keyof HourlyActivityModel['actions'];
            const count = Number(
                row.metricValues && row.metricValues.length > 0 && row.metricValues[0]?.value
                    ? row.metricValues[0].value
                    : '0',
            );
            if (!hour || !typedAction) return;
            if (!statsMap.has(hour)) {
                statsMap.set(hour, {
                    hour,
                    actions: {approve: 0, decline: 0, sendEmail: 0},
                });
            }
            const hourObj = statsMap.get(hour)!;
            hourObj.actions[typedAction] += count;
        });

        return ALL_HOURS.map(
            (hour) =>
                statsMap.get(hour) || {
                    hour,
                    actions: {approve: 0, decline: 0, sendEmail: 0},
                },
        );
    }, [dataState.data]);

    const legendPayload = useMemo(
        () =>
            ACTIONS.map((a) => ({
                value: a.label,
                type: 'rect' as const,
                id: a.key,
                color: a.color,
                dataKey: a.key,
            })),
        [],
    );

    return (
        <Box>
            <ResponsiveContainer width='100%' height={400}>
                <BarChart data={data} margin={{top: 20, right: 30, left: 20, bottom: 5}}>
                    <CartesianGrid strokeDasharray='3 3' />
                    <XAxis dataKey='hour' style={{fontSize: 'var(--small-text-size)'}} />
                    <YAxis
                        allowDecimals={false}
                        domain={[0, 'dataMax']}
                        style={{fontSize: 'var(--small-text-size)'}}
                    />
                    <Tooltip
                        cursor={{fill: 'var(--color-divider)', opacity: 0.5}}
                        content={<KasCustomTooltip labelKey='hour' labelFormatter={(v) => `Hour: ${v}`} />}
                    />
                    <Legend
                        content={() => (
                            <KasCustomLegend
                                payload={legendPayload}
                                hiddenKeys={hiddenKeys}
                                onClick={handleLegendClick}
                            />
                        )}
                    />
                    {ACTIONS.filter((a) => !hiddenKeys.includes(a.key)).map((action) => (
                        <Bar
                            key={action.key}
                            dataKey={`actions.${action.key}`}
                            name={action.label}
                            fill={action.color}
                            stackId='bar'
                        />
                    ))}
                </BarChart>
            </ResponsiveContainer>
        </Box>
    );
};
