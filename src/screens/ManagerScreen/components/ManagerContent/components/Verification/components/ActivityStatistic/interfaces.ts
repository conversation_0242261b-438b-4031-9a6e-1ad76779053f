export interface ActivityStatisticValueModel {
    count: number;
    time: number;
}

export interface ActivityStatisticActionModel {
    approve: ActivityStatisticValueModel;
    decline: ActivityStatisticValueModel;
    sendEmail: ActivityStatisticValueModel;
    incompleteExit: ActivityStatisticValueModel;
}

export interface ActivityStatisticModel {
    agent?: string;
    queue?: string;
    date?: string;
    actions: ActivityStatisticActionModel;
}
