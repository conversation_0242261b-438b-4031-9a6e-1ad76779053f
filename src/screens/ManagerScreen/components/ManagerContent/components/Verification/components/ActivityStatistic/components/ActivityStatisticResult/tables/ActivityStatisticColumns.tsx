import {CellContext, ColumnDef, createColumnHelper} from '@tanstack/react-table';
import {defaultInfoColumn, totalSum} from '@/utils/TableUtils';
import {getHumanizedTimeDuration} from '@/utils/DateUtils';
import {NO_RESULTS_SYMBOL} from '@/constants';
import {ActivityStatisticActionModel, ActivityStatisticModel} from './../../../interfaces';

const columnHelper = createColumnHelper<ActivityStatisticModel>();

const _defaultInfoColumn = defaultInfoColumn<ActivityStatisticModel>;

const _renderAverageTime = (key: keyof ActivityStatisticActionModel) =>
    columnHelper.accessor(`actions.${key}.time`, {
        id: `${key}-average-time`,
        header: 'Average Time',
        cell: (props) => {
            const value = props.row.original.actions[key];

            return value.count > 0 ? getHumanizedTimeDuration(value.time / value.count) : NO_RESULTS_SYMBOL;
        },
    });

const defaultColumns = [
    columnHelper.group({
        id: 'approve',
        header: 'Approve',
        columns: [
            columnHelper.accessor('actions.approve.count', {
                id: 'approve-count',
                header: 'Count',
                footer: (props) => totalSum({...props, columnId: 'approve-count'}),
            }),
            _renderAverageTime('approve'),
        ],
    }),
    columnHelper.group({
        id: 'decline',
        header: 'Decline',
        columns: [
            columnHelper.accessor('actions.decline.count', {
                id: 'decline-count',
                header: 'Count',
                footer: (props) => totalSum({...props, columnId: 'decline-count'}),
            }),
            _renderAverageTime('decline'),
        ],
    }),
    columnHelper.group({
        id: 'sendEmail',
        header: 'Send Email',
        columns: [
            columnHelper.accessor('actions.sendEmail.count', {
                id: 'sendEmail-count',
                header: 'Count',
                footer: (props) => totalSum({...props, columnId: 'sendEmail-count'}),
            }),
            _renderAverageTime('sendEmail'),
        ],
    }),
    columnHelper.group({
        id: 'total',
        header: 'Total',
        columns: [
            {
                id: 'total-count',
                header: 'Count',
                cell: (props: CellContext<ActivityStatisticModel, string>) => {
                    const {approve, decline, sendEmail} = props.row.original.actions;

                    return approve.count + decline.count + sendEmail.count;
                },
                footer: (props) => {
                    const keys: Array<keyof ActivityStatisticActionModel> = [
                        'approve',
                        'decline',
                        'sendEmail',
                    ];

                    return keys.reduce(
                        (prev, cur) => prev + totalSum({...props, columnId: `${cur}-count`}),
                        0,
                    );
                },
                enableSorting: false,
                meta: {
                    notExport: true,
                },
            },
            {
                id: 'total-time',
                header: 'Average Time',
                cell: (props: CellContext<ActivityStatisticModel, string>) => {
                    const {approve, decline, sendEmail} = props.row.original.actions;
                    const count = approve.count + decline.count + sendEmail.count;
                    const time = approve.time + decline.time + sendEmail.time;

                    return count > 0 ? getHumanizedTimeDuration(time / count) : NO_RESULTS_SYMBOL;
                },
                enableSorting: false,
                meta: {
                    notExport: true,
                },
            },
        ],
    }),
];

export const AgentActivityColumns = [
    _defaultInfoColumn('agent', 'Agent', 'Total:'),
    ...defaultColumns,
] as ColumnDef<ActivityStatisticModel, unknown>[];
export const QueueActivityColumns = [
    _defaultInfoColumn('queue', 'Queue', 'Total:'),
    ...defaultColumns,
] as ColumnDef<ActivityStatisticModel, unknown>[];
export const DateActivityColumns = [
    _defaultInfoColumn('date', 'Date', 'Total:'),
    ...defaultColumns,
] as ColumnDef<ActivityStatisticModel, unknown>[];
