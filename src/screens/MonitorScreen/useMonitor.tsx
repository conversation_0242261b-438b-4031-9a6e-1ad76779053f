import React, {createContext, useContext, useEffect, useState} from 'react';
import {MonitorItemType} from '@/screens/MonitorScreen/interfaces';
import {useHashHandler} from '@/hooks/useHashHandler';

interface MonitorContextModel {
    activeMenu: MonitorItemType;
    changeActiveMenu: (value: MonitorItemType) => void;
}

const MonitorContext = createContext<MonitorContextModel | undefined>(undefined);

interface MonitorProviderProps {
    children: React.ReactNode;
}

export const MonitorProvider = ({children}: MonitorProviderProps) => {
    const {hashMatch, updateRoute} = useHashHandler();
    const [activeMenu, setActiveMenu] = useState<MonitorItemType>(MonitorItemType.Backup_ACH);

    const changeActiveMenu = (value: MonitorItemType) => {
        updateRoute({hash: value, type: null, value: null});
    };

    const handleRouteChange = () => {
        const hash = hashMatch.hash;

        if (hash) {
            const hashMenuItem = hash.toUpperCase() as MonitorItemType;
            const isValidMenuType = Object.values(MonitorItemType).includes(hashMenuItem);

            if (isValidMenuType) {
                setActiveMenu(hashMenuItem);
            }
        }
    };

    useEffect(() => {
        handleRouteChange();
    }, [hashMatch]);

    const value: MonitorContextModel = {
        activeMenu,
        changeActiveMenu,
    };

    return <MonitorContext.Provider value={value}>{children}</MonitorContext.Provider>;
};

export function useMonitor() {
    const context = useContext(MonitorContext);
    if (!context) {
        throw new Error('useMonitor must be used within MonitorProvider');
    }
    return context;
}
