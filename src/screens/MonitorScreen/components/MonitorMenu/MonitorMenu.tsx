import React from 'react';
import {Tab, Tabs} from '@mui/material';
import {MonitorItemType} from '@/screens/MonitorScreen/interfaces';
import {useMonitor} from '@/screens/MonitorScreen/useMonitor';

export const MonitorMenu = () => {
    const {activeMenu, changeActiveMenu} = useMonitor();

    const handleChange = (_event: React.SyntheticEvent, newValue: MonitorItemType) => {
        changeActiveMenu(newValue);
    };

    return (
        <Tabs variant='scrollable' value={activeMenu} onChange={handleChange}>
            {Object.entries(MonitorItemType).map(([key, value]) => (
                <Tab key={key} label={value} value={value} />
            ))}
        </Tabs>
    );
};
