import {CellContext, createColumnHelper} from '@tanstack/react-table';
import {defaultInfoColumn} from '@/utils/TableUtils';
import React, {PropsWithChildren} from 'react';
import {KasStrike, KasUnderwritingSharedLink} from '@/components';
import { MonitorDisabledBackupACHModel } from '@/interfaces';

const columnHelper = createColumnHelper<MonitorDisabledBackupACHModel>();

const _defaultInfoColumn = defaultInfoColumn<MonitorDisabledBackupACHModel>;

interface CellContentProps extends PropsWithChildren {
    props: CellContext<MonitorDisabledBackupACHModel, string>;
}

const CellContent = ({props, children}: CellContentProps) => (
    <KasStrike
        key={`${props.column.id}-${props.row.original.loan_id}`}
        isStrike={false}>
        {children}
    </KasStrike>
);
const _strikeContent = (props: CellContext<MonitorDisabledBackupACHModel, string>) => (
    <CellContent props={props}>{props.getValue()}</CellContent>
);
export const DisabledBackupACHTableColumns = [
    _defaultInfoColumn('loan_id', 'Loan ID', undefined, _strikeContent),
    columnHelper.accessor('employeeInfo', {
        id: 'employeeInfo',
        header: 'Employee ID',
        enableSorting: false,
        cell: (props) => {
            const {employee_id, first_name, last_name} = props.row.original;
            return (
                <KasStrike isStrike={false}>
                    {first_name} {last_name} [<KasUnderwritingSharedLink id={employee_id} />]
                </KasStrike>
            );
        },}),
    _defaultInfoColumn('ach_type', 'ACH Type', undefined, _strikeContent),
    _defaultInfoColumn('commentText', 'Comment', undefined, _strikeContent),
    _defaultInfoColumn('disabled_date', 'Date of Disable', undefined, _strikeContent),
];
