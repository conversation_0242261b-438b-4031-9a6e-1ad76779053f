import React, {createContext, useContext, useState} from 'react';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {Completable, DataStateInterface, MonitorDisabledBackupACHModel} from '@/interfaces';

interface DisabledBackupACHContextModel {
    backupState: DataStateInterface<MonitorDisabledBackupACHModel[]>;
    loadBackups: () => Promise<Completable<MonitorDisabledBackupACHModel[]>>;
}

const DisabledBackupACHContext = createContext<DisabledBackupACHContextModel | undefined>(undefined);

export const DisabledBackupACHProvider = ({children}: {children: React.ReactNode}) => {
    const [backupState, setBackupState] = useState(getDefaultState<MonitorDisabledBackupACHModel[]>());

    const loadBackups = async (): Promise<Completable<MonitorDisabledBackupACHModel[]>> => {
        const url = `/api/secured/monitor/disabled-backup-ach`;

        setBackupState(getLoadingState(backupState));

        const response = await apiRequest(url);

        setBackupState(getLoadedState(response));

        return response;
    };

    const value: DisabledBackupACHContextModel = {
        backupState,
        loadBackups,
    };

    return <DisabledBackupACHContext.Provider value={value}>{children}</DisabledBackupACHContext.Provider>;
};

export function useDisabledBackupACH() {
    const context = useContext(DisabledBackupACHContext);
    if (!context) {
        throw new Error('useBackupACH must be used within BackupACHProvider');
    }
    return context;
}
