import React, {useEffect, useMemo} from 'react';
import {useBackupACH} from './../../components';
import {ColumnDef} from '@tanstack/react-table';
import {TableView} from '@/views';
import {BackupACHTableColumns} from './tables';
import {MonitorBackupACHModel} from '@/interfaces';
import Box from '@mui/material/Box';
import {Button, Divider, Grid2, Paper, Typography} from '@mui/material';

export const BackupACH = () => {
    const {backupState, loadBackups} = useBackupACH();

    const data: MonitorBackupACHModel[] | null = useMemo(() => {
        return (
            backupState.data?.map((item) => ({
                ...item,
                employeeInfo: `${item.first_name} ${item.last_name} [${item.employee_id}]`,
            })) ?? null
        );
    }, [backupState.data]);

    useEffect(() => {
        loadBackups().then();
    }, []);

    return (
        <Paper elevation={0}>
            <Box p={2}>
                <Grid2 container spacing={2} mb={2} alignItems='center'>
                    <Grid2 size={10}>
                        <Typography variant='h6'>Backup ACH</Typography>
                    </Grid2>
                    <Grid2 size={2}>
                        <Button
                            fullWidth
                            variant='contained'
                            onClick={loadBackups}
                            disabled={backupState.loading}>
                            Reload
                        </Button>
                    </Grid2>
                </Grid2>
                <Divider />
                <Box position='relative' pt={2}>
                    <TableView<MonitorBackupACHModel>
                        withTableActions
                        loading={backupState.loading}
                        error={backupState.error}
                        data={data}
                        columns={BackupACHTableColumns as ColumnDef<MonitorBackupACHModel, unknown>[]}
                        onRetry={loadBackups}
                        tableName='Backup ACH'
                    />
                </Box>
            </Box>
        </Paper>
    );
};
