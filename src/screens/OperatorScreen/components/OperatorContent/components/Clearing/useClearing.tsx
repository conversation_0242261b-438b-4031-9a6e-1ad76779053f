import React, {createContext, useContext, useState} from 'react';
import {apiRequest} from '@/utils/AxiosUtils';
import {Completable, DataStateInterface, OperatorPaymentModel} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {ClearingModalProps, ClearingParamsModel} from './interfaces';
import {DEFAULT_FILTER_PARAMS} from './data';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {useSnackbar} from '@/hooks/useSnackbar';

interface ClearingContextModel {
    dataState: DataStateInterface<OperatorPaymentModel[]>;
    loadData: (params?: ClearingParamsModel) => Promise<void>;
    openModal: ClearingModalProps | null;
    setOpenModal: (value: ClearingModalProps | null) => void;
    filterParams: ClearingParamsModel;
    onClearBatch: (type: string, date: string) => Promise<void>;
    clearingBatch: boolean;
}

const ClearingContext = createContext<ClearingContextModel | undefined>(undefined);

export const ClearingProvider = ({children}: {children: React.ReactNode}) => {
    const {showMessage} = useSnackbar();
    const [dataState, setDataState] = useState(getDefaultState<OperatorPaymentModel[]>);
    const [openModal, setOpenModal] = useState<ClearingModalProps | null>(null);
    const [filterParams, setFilterParams] = useState<ClearingParamsModel>(DEFAULT_FILTER_PARAMS);
    const [clearingBatch, setClearingBatch] = useState(false);

    const loadData = async (params?: ClearingParamsModel) => {
        const curParams = params || filterParams;
        const searchParams = new URLSearchParams({
            future: String(curParams.future),
            date: curParams.date,
            ...(curParams.type && {type: curParams.type}),
        }).toString();
        const url = `/api/secured/operator/clearing?${searchParams}`;

        setFilterParams(curParams);
        setDataState(getLoadingState(dataState));
        const response: Completable<OperatorPaymentModel[]> = await apiRequest(url);
        setDataState(getLoadedState(response));
    };

    const onClearBatch = async (type: string, date: string) => {
        const curParams = {...filterParams, type, date};
        const payload = {ach_type: type, end_date: date};
        const url = `/api/secured/operator/clearing/all`;

        setClearingBatch(true);

        const response: Completable<boolean> = await apiRequest(url, {
            method: 'post',
            body: JSON.stringify(payload),
        });

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            setOpenModal(null);
            await loadData(curParams);
        }

        setClearingBatch(false);
    };

    const value: ClearingContextModel = {
        dataState,
        loadData,
        openModal,
        setOpenModal,
        filterParams,
        onClearBatch,
        clearingBatch,
    };

    return <ClearingContext.Provider value={value}>{children}</ClearingContext.Provider>;
};

export function useClearing() {
    const context = useContext(ClearingContext);
    if (!context) {
        throw new Error('useClearing must be used within ClearingProvider');
    }
    return context;
}
