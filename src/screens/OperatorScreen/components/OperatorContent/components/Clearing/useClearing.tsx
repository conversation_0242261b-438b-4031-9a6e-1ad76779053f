import React, {createContext, useContext, useState} from 'react';
import {apiRequest} from '@/utils/AxiosUtils';
import {Completable, DataStateInterface, OperatorPaymentModel} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {ClearingModalProps} from './interfaces';

interface ClearingContextModel {
    dataState: DataStateInterface<OperatorPaymentModel[]>;
    loadData: (params?: string) => Promise<void>;
    openModal: ClearingModalProps | null;
    setOpenModal: (value: ClearingModalProps | null) => void;
}

const ClearingContext = createContext<ClearingContextModel | undefined>(undefined);

export const ClearingProvider = ({children}: {children: React.ReactNode}) => {
    const [dataState, setDataState] = useState(getDefaultState<OperatorPaymentModel[]>);
    const [openModal, setOpenModal] = useState<ClearingModalProps | null>(null);
    const [filterParams, setFilterParams] = useState<string>();

    const loadData = async (params?: string) => {
        const curParams = params || filterParams;
        const url = `/api/secured/operator/clearing?${curParams}`;

        setFilterParams(curParams);
        setDataState(getLoadingState(dataState));
        const response: Completable<OperatorPaymentModel[]> = await apiRequest(url);
        setDataState(getLoadedState(response));
    };

    const value: ClearingContextModel = {dataState, loadData, openModal, setOpenModal};

    return <ClearingContext.Provider value={value}>{children}</ClearingContext.Provider>;
};

export function useClearing() {
    const context = useContext(ClearingContext);
    if (!context) {
        throw new Error('useClearing must be used within ClearingProvider');
    }
    return context;
}
