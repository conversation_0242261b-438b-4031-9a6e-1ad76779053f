import React from 'react';
import {useFormik} from 'formik';
import {Alert, Grid2} from '@mui/material';
import {KasModalFooter} from '@/components';
import {useClearing} from '../../../../useClearing';
import {ClearingAllProps} from './../../../../interfaces';

export const ClearAll = ({type, date}: ClearingAllProps) => {
    const {setOpenModal, onClearBatch, clearingBatch} = useClearing();

    const onSubmit = async () => {
        await onClearBatch(type, date);
    };

    const formik = useFormik({
        initialValues: {},
        onSubmit,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2}>
                <Grid2 size={12}>
                    <Alert severity='warning'>
                        Are you sure you want to clear all ACH agreements until {date}?
                    </Alert>
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        disabled={clearingBatch}
                        disableCancel={clearingBatch}
                        loading={clearingBatch}
                        submitText='OK'
                        onCancel={() => setOpenModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
