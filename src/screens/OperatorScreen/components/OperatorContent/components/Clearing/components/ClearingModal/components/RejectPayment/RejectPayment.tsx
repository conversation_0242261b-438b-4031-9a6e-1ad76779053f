import React, {useState} from 'react';
import {Grid2} from '@mui/material';
import {useClearing} from '../../../../useClearing';
import {useFormik} from 'formik';
import {RejectPaymentValues, validationSchema} from './schema';
import {KasAutocompleteField, KasDatePickerFormField, KasModalFooter} from '@/components';
import {REASON_OPTIONS} from './data';
import {OperatorPaymentModel} from '@/interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {useSnackbar} from '@/hooks/useSnackbar';
import {getNextBusinessDay} from '@/utils/DateUtils';
import dayjs from 'dayjs';

export const RejectPayment = ({data}: {data: OperatorPaymentModel}) => {
    const {showMessage} = useSnackbar();
    const {loadData, setOpenModal} = useClearing();
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async (values: RejectPaymentValues) => {
        const url = `/api/secured/operator/clearing`;
        const payload = {
            action_type: values.reason,
            effective_date: dayjs(values.date).format('YYYYMMDD'),
            entity_id: data.entity_id,
            entity_class: data.entity_class,
        };

        setSubmitting(true);

        const response = await apiRequest(url, {
            method: 'post',
            body: JSON.stringify(payload),
        });

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            setOpenModal(null);
            await loadData();
        }

        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            date: getNextBusinessDay(),
            reason: '',
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2}>
                <Grid2 size={6}>
                    <KasAutocompleteField
                        name='reason'
                        formik={formik}
                        label='Reject Reason'
                        disabled={submitting}
                        options={REASON_OPTIONS}
                    />
                </Grid2>
                <Grid2 size={6}>
                    <KasDatePickerFormField formik={formik} name='date' label='Date' disabled={submitting} />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        loading={submitting}
                        disabled={!formik.isValid || submitting}
                        onCancel={() => setOpenModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
