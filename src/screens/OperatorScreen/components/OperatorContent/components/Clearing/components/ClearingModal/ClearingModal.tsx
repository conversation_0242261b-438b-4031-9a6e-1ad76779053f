import React, {useMemo} from 'react';
import {KasModal} from '@/components';
import {RejectPayment, ClearAll, SetCleared} from './components';
import {useClearing} from '../../useClearing';
import {ClearingItemModal} from './../../interfaces';

export const ClearingModal = () => {
    const {openModal, setOpenModal, clearingBatch} = useClearing();

    const renderActionForm = useMemo(() => {
        switch (openModal?.type) {
            case ClearingItemModal.Set_Cleared:
                return <SetCleared {...openModal.props} />;
            case ClearingItemModal.Reject_Payment:
                return <RejectPayment {...openModal.props} />;
            case ClearingItemModal.Clear_All:
                return <ClearAll {...openModal.props} />;
            default:
                return null;
        }
    }, [openModal?.type]);

    return (
        <KasModal
            title={openModal?.type || 'Action Modal'}
            open={!!openModal}
            size='small'
            disableClose={clearingBatch}
            onClose={() => setOpenModal(null)}>
            {renderActionForm}
        </KasModal>
    );
};
