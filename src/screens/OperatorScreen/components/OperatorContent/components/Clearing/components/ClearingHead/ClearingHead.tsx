import React, {useEffect} from 'react';
import {<PERSON>ton, Checkbox, FormControlLabel, Grid2, Typography} from '@mui/material';
import {useClearing} from '../../useClearing';
import {useFormik} from 'formik';
import {validationSchema} from './schema';
import {useOperator} from '@/screens/OperatorScreen/useOperator';
import {OperatorItem} from '@/screens/OperatorScreen/interfaces';
import {KasAutocompleteField, KasDatePickerFormField} from '@/components';
import {TYPE_OPTIONS} from './data';
import dayjs from 'dayjs';
import {ClearingItemModal, ClearingParamsModel} from './../../interfaces';

export const ClearingHead = () => {
    const {activeMenu} = useOperator();
    const {dataState, loadData, setOpenModal, clearingBatch} = useClearing();

    const onSubmit = async (values: ClearingParamsModel) => {
        await loadData(values);
    };

    const formik = useFormik<ClearingParamsModel>({
        validateOnMount: true,
        initialValues: {
            date: dayjs().format('YYYYMMDD'),
            type: '',
            future: false,
        },
        onSubmit,
        validationSchema,
    });

    useEffect(() => {
        if (activeMenu === OperatorItem.CLEARING && !dataState.data && !dataState.loading) {
            formik.handleSubmit();
        }
    }, [activeMenu, dataState.data, dataState.loading]);

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2}>
                <Grid2 size={2}>
                    <Typography variant='subtitle1' py={1}>
                        Payment Clearing Queue
                    </Typography>
                </Grid2>
                <Grid2 size={2}>
                    <KasDatePickerFormField
                        formik={formik}
                        name='date'
                        label='Date'
                        disableFuture
                        disabled={dataState.loading}
                    />
                </Grid2>
                <Grid2 size={2}>
                    <KasAutocompleteField
                        name='type'
                        formik={formik}
                        label='Type'
                        disabled={dataState.loading}
                        options={TYPE_OPTIONS}
                    />
                </Grid2>
                <Grid2 size={2}>
                    <FormControlLabel
                        disabled={dataState.loading}
                        label='Preview Future Clearings'
                        control={
                            <Checkbox
                                size='small'
                                name='future'
                                onChange={formik.handleChange}
                                checked={formik.values.future}
                            />
                        }
                    />
                </Grid2>
                <Grid2 size={2}>
                    <Button
                        fullWidth
                        variant='contained'
                        size='small'
                        type='submit'
                        loading={dataState.loading}
                        disabled={dataState.loading || !formik.isValid}>
                        Search
                    </Button>
                </Grid2>
                <Grid2 size={2}>
                    <Button
                        fullWidth
                        variant='outlined'
                        size='small'
                        loading={clearingBatch}
                        disabled={dataState.loading || !formik.isValid}
                        onClick={() =>
                            setOpenModal({
                                type: ClearingItemModal.Clear_All,
                                props: {
                                    type: formik.values.type || '',
                                    date: dayjs(formik.values.date).format('YYYYMMDD'),
                                },
                            })
                        }>
                        Batch Clear
                    </Button>
                </Grid2>
            </Grid2>
        </form>
    );
};
