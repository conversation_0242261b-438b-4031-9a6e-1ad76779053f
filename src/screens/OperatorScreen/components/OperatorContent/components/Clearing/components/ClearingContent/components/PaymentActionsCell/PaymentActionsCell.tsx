import React from 'react';
import {OperatorPaymentModel} from '@/interfaces';
import {ActionCell} from '@/components/table/cells';
import {Check, Close} from '@mui/icons-material';
import {Stack} from '@mui/material';
import {useClearing} from '../../../../useClearing';
import {ClearingItemModal} from './../../../../interfaces';

export const PaymentActionsCell = ({data}: {data: OperatorPaymentModel}) => {
    const {setOpenModal} = useClearing();

    return (
        <Stack direction='row' alignItems='center' spacing={1}>
            <ActionCell
                Icon={<Check color='success' titleAccess='Cleared' />}
                onClick={() => setOpenModal({type: ClearingItemModal.Set_Cleared, props: {data}})}
            />
            <ActionCell
                Icon={<Close color='error' titleAccess='Reject' />}
                onClick={() => setOpenModal({type: ClearingItemModal.Reject_Payment, props: {data}})}
            />
        </Stack>
    );
};
