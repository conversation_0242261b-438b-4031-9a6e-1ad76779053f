import {OperatorPaymentModel} from '@/interfaces';
import {useClearing} from '../../useClearing';
import {ClearingTable} from './tables';
import {Row} from '@tanstack/react-table';
import {TableView} from '@/views';
import {OpenPaymentDetails} from '@/screens/OperatorScreen/components/OperatorContent/components/payment';
import React from 'react';

export const ClearingContent = () => {
    const {dataState} = useClearing();

    return (
        <TableView<OperatorPaymentModel>
            withTableActions={true}
            loading={dataState.loading}
            error={dataState.error}
            data={dataState.data}
            columns={ClearingTable}
            tableName='Payment Clearing Queue'
            sortingColumns={[{id: 'schedule_date', desc: false}]}
            renderExpand={(row: Row<OperatorPaymentModel>) => <OpenPaymentDetails data={row.original} />}
        />
    );
};
