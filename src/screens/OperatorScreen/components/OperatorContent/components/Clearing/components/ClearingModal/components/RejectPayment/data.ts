import {SelectModel} from '@/interfaces';

export const REASON_OPTIONS: SelectModel<string>[] = [
    {
        id: 'ACH_RETURNED_ACCOUNT_CLOSED',
        value: 'ACH_RETURNED_ACCOUNT_CLOSED',
        label: 'ACH Returned: Account Closed',
    },
    {
        id: 'ACH_RETURNED_ACCOUNT_FROZEN',
        value: 'ACH_RETURNED_ACCOUNT_FROZEN',
        label: 'ACH Returned: Account Frozen',
    },
    {
        id: 'ACH_RETURNED_NOT_AUTHORIZED',
        value: 'ACH_RETURNED_NOT_AUTHORIZED',
        label: 'ACH Returned: Cust Adv Not Authorized',
    },
    {
        id: 'ACH_RETURNED_INSUFFICIENT_FUNDS',
        value: 'ACH_RETURNED_INSUFFICIENT_FUNDS',
        label: 'ACH Returned: Insufficient Funds',
    },
    {
        id: 'ACH_RETURNED_INVALID_ACCOUNT',
        value: 'ACH_RETURNED_INVALID_ACCOUNT',
        label: 'ACH Returned: Invalid Account',
    },
    {
        id: 'ACH_RETURNED_NON_TRANSACTIONAL_ACCOUNT',
        value: 'ACH_RETURNED_NON_TRANSACTIONAL_ACCOUNT',
        label: 'ACH Returned: Non-Transactional Account',
    },
    {
        id: 'ACH_RETURNED_PAYMENT_STOPPED',
        value: 'ACH_RETURNED_PAYMENT_STOPPED',
        label: 'ACH Returned: Payment Stopped',
    },
    {
        id: 'ACH_RETURNED_UNABLE_TO_LOCATE',
        value: 'ACH_RETURNED_UNABLE_TO_LOCATE',
        label: 'ACH Returned: Unable to Locate',
    },
    {
        id: 'ACH_RETURNED_UNAUTHORIZED_DEBIT',
        value: 'ACH_RETURNED_UNAUTHORIZED_DEBIT',
        label: 'ACH Returned: Unauthorized Debit',
    },
    {
        id: 'DIRECT_DEPOSIT_RETURNED_NOT_AUTHORIZED',
        value: 'DIRECT_DEPOSIT_RETURNED_NOT_AUTHORIZED',
        label: 'Direct Deposit Returned: Cust Adv Not Authorized',
    },
];
