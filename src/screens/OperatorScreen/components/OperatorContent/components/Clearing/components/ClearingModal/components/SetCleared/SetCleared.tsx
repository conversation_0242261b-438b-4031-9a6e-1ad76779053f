import React, {useState} from 'react';
import {useFormik} from 'formik';
import {Alert, Grid2} from '@mui/material';
import {KasModalFooter} from '@/components';
import {useSnackbar} from '@/hooks/useSnackbar';
import {apiRequest} from '@/utils/AxiosUtils';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {OperatorPaymentModel} from '@/interfaces';
import {useClearing} from '../../../../useClearing';

export const SetCleared = ({data}: {data: OperatorPaymentModel}) => {
    const {showMessage} = useSnackbar();
    const {setOpenModal, loadData} = useClearing();
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async () => {
        const url = `/api/secured/operator/clearing`;
        const payload = {entity_id: data.entity_id, entity_class: data.entity_class};

        setSubmitting(true);

        const response = await apiRequest(url, {
            method: 'put',
            body: JSON.stringify(payload),
        });

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            setOpenModal(null);
            await loadData();
        }

        setSubmitting(false);
    };

    const formik = useFormik({
        initialValues: {},
        onSubmit,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2}>
                <Grid2 size={12}>
                    <Alert severity='warning'>This Action will Mark ACH Payment Cleared. Continue?</Alert>
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        disabled={submitting}
                        submitText='OK'
                        onCancel={() => setOpenModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
