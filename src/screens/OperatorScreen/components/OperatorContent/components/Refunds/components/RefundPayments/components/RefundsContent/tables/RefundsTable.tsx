import React from 'react';
import {CellContext, ColumnDef, createColumnHelper} from '@tanstack/react-table';
import {OperatorRefundModel} from '@/interfaces';
import {defaultAmountColumn, defaultInfoColumn, expandCell} from '@/utils/TableUtils';
import {KasUnderwritingSharedLink} from '@/components';
import {RefundActionsCell} from '../components';

const columnHelper = createColumnHelper<OperatorRefundModel>();

const _defaultInfoColumn = defaultInfoColumn<OperatorRefundModel>;
const _defaultAmountColumn = defaultAmountColumn<OperatorRefundModel>;

export const RefundsTable = [
    {
        id: 'expander',
        header: '',
        cell: (props: CellContext<OperatorRefundModel, string>) => expandCell(props),
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
    columnHelper.accessor((data) => `${data.employee_name} [${data.employee_id}]`, {
        id: 'employee_id',
        header: 'Employee [ID]',
        cell: (props) => {
            const {employee_name, employee_id} = props.row.original;

            return (
                <>
                    {employee_name} [<KasUnderwritingSharedLink id={employee_id} />]
                </>
            );
        },
        meta: {
            exportHTML: (cell) => {
                const {employee_name, employee_id} = cell.row.original;

                return `${employee_name} [${employee_id}]`;
            },
        },
    }),
    _defaultInfoColumn('employer_name', 'Employer'),
    _defaultInfoColumn('status', 'Status'),
    _defaultInfoColumn('state', 'State'),
    columnHelper.group({
        id: 'loan',
        header: 'Loan',
        columns: [_defaultInfoColumn('loan_id', 'ID'), _defaultInfoColumn('delinq_days', 'Days Dlq')],
    }),
    _defaultAmountColumn('refund_owed', 'Refund Amount', false),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<OperatorRefundModel, string>) => (
            <RefundActionsCell data={props.row.original} />
        ),
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
] as ColumnDef<OperatorRefundModel, unknown>[];
