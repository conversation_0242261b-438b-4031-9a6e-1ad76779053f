import {OperatorRefundModel} from '@/interfaces';
import {useRefundPayments} from '../../useRefundPayments';
import {RefundsTable} from './tables';
import {Row} from '@tanstack/react-table';
import {TableView} from '@/views';
import {OpenRefundDetails} from './components';

export const RefundsContent = () => {
    const {dataState} = useRefundPayments();

    return (
        <TableView<OperatorRefundModel>
            withTableActions={true}
            loading={dataState.loading}
            error={dataState.error}
            data={dataState.data}
            columns={RefundsTable}
            tableName='Payment RefundPayments Queue'
            sortingColumns={[{id: 'employee_id', desc: true}]}
            renderExpand={(row: Row<OperatorRefundModel>) => <OpenRefundDetails data={row.original} />}
        />
    );
};
