import React, {useEffect} from 'react';
import {Button, Checkbox, FormControlLabel, Grid2, Typography} from '@mui/material';
import {useRefundPayments} from '../../useRefundPayments';
import {useFormik} from 'formik';
import {ClearingHeadValues, validationSchema} from './schema';
import {useOperator} from '@/screens/OperatorScreen/useOperator';
import {OperatorItem} from '@/screens/OperatorScreen/interfaces';

export const RefundsHead = () => {
    const {activeMenu} = useOperator();
    const {dataState, loadData} = useRefundPayments();

    const onSubmit = async (values: ClearingHeadValues) => {
        const params = new URLSearchParams({
            underDollar: String(values.underDollar),
        }).toString();

        await loadData(params);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            underDollar: false,
        },
        onSubmit,
        validationSchema,
    });

    useEffect(() => {
        if (activeMenu === OperatorItem.REFUNDS && !dataState.data) {
            formik.handleSubmit();
        }
    }, [activeMenu]);

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2}>
                <Grid2 size={2.5}>
                    <Typography variant='subtitle1' py={1}>
                        Account Refunds
                    </Typography>
                </Grid2>
                <Grid2 size={3}>
                    <FormControlLabel
                        disabled={dataState.loading}
                        label='Include Under Dollar'
                        control={
                            <Checkbox
                                size='small'
                                name='underDollar'
                                onChange={formik.handleChange}
                                checked={formik.values.underDollar}
                            />
                        }
                    />
                </Grid2>
                <Grid2 size={2} ml='auto'>
                    <Button
                        fullWidth
                        variant='contained'
                        size='small'
                        type='submit'
                        loading={dataState.loading}
                        disabled={dataState.loading}>
                        Apply
                    </Button>
                </Grid2>
            </Grid2>
        </form>
    );
};
