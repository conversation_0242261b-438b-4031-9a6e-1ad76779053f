import React, {createContext, useContext, useState} from 'react';
import {apiRequest} from '@/utils/AxiosUtils';
import {Completable, DataStateInterface, OperatorRefundModel} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {RefundsModalProps} from './interfaces';

interface RefundPaymentsContextModel {
    dataState: DataStateInterface<OperatorRefundModel[]>;
    loadData: (params?: string) => Promise<void>;
    openModal: RefundsModalProps | null;
    setOpenModal: (value: RefundsModalProps | null) => void;
}

const RefundPaymentsContext = createContext<RefundPaymentsContextModel | undefined>(undefined);

export const RefundPaymentsProvider = ({children}: {children: React.ReactNode}) => {
    const [dataState, setDataState] = useState(getDefaultState<OperatorRefundModel[]>);
    const [openModal, setOpenModal] = useState<RefundsModalProps | null>(null);
    const [filterParams, setFilterParams] = useState<string>();

    const loadData = async (params?: string) => {
        const curParams = params || filterParams;
        const url = `/api/secured/operator/refunds?${curParams}`;

        setFilterParams(curParams);
        setDataState(getLoadingState(dataState));
        const response: Completable<OperatorRefundModel[]> = await apiRequest(url);
        setDataState(getLoadedState(response));
    };

    const value: RefundPaymentsContextModel = {dataState, loadData, openModal, setOpenModal};

    return <RefundPaymentsContext.Provider value={value}>{children}</RefundPaymentsContext.Provider>;
};

export function useRefundPayments() {
    const context = useContext(RefundPaymentsContext);
    if (!context) {
        throw new Error('useRefundPayments must be used within RefundPaymentsProvider');
    }
    return context;
}
