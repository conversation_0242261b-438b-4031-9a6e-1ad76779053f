import {OperatorAgreementModel} from '@/interfaces';
import {useAgreementLookup} from '../../useAgreementLookup';
import {AgreementHistoryTable} from './tables';
import {TableView} from '@/views';

export const AgreementLookupContent = () => {
    const {dataState} = useAgreementLookup();

    return (
        <TableView<OperatorAgreementModel>
            withTableActions={true}
            loading={dataState.loading}
            error={dataState.error}
            data={dataState.data}
            columns={AgreementHistoryTable}
            tableName='Agreement History'
            sortingColumns={[{id: 'loan_id', desc: false}]}
        />
    );
};
