import * as Yup from 'yup';
import {DEFAULT_VALIDATION_MSG} from '@/constants';
import {LookupDTO} from '@/models';

export const validationSchema = Yup.object().shape({
    amount: Yup.number().required(DEFAULT_VALIDATION_MSG),
    effectiveDate: Yup.string().required(DEFAULT_VALIDATION_MSG),
    transactionType: Yup.string().required(DEFAULT_VALIDATION_MSG),
    amountType: Yup.number().required(DEFAULT_VALIDATION_MSG),
    loan: Yup.object().nullable(),
});

export interface EmployeeTransactionFormValues {
    amount: number | string;
    effectiveDate: string;
    transactionType: string;
    amountType: number | string;
    loan: LookupDTO | null;
}
