import React, {useEffect, useState} from 'react';
import {Kas<PERSON><PERSON>ding, KasNoR<PERSON>ult<PERSON>, KasSwitch, KasSwitchWhen} from '@/components';
import {apiRequest} from '@/utils/AxiosUtils';
import {Completable, OperatorTransactionModel} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {ErrorView} from '@/views';
import {EmployeeTransactionForm} from './../../components';

export const EditEmployeeTransaction = ({data}: {data: OperatorTransactionModel}) => {
    const [detailsState, setDetailsState] = useState(getDefaultState<OperatorTransactionModel>());

    const loadDetails = async () => {
        const url = `/api/secured/operator/transactions/${data.gid}`;

        setDetailsState(getLoadingState(detailsState));
        const response: Completable<OperatorTransactionModel> = await apiRequest(url);
        setDetailsState(getLoadedState(response));
    };

    useEffect(() => {
        loadDetails().then();
    }, []);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={detailsState.loading}>
                <KasLoading />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!detailsState.error}>
                <ErrorView error={detailsState.error} />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!detailsState.data}>
                <KasNoResults text='No email available to display.' p={2} bgcolor='var(--color-grey)' />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!detailsState.data}>
                {!!detailsState.data && <EmployeeTransactionForm data={detailsState.data} />}
            </KasSwitchWhen>
        </KasSwitch>
    );
};
