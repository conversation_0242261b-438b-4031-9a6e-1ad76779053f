import React from 'react';
import {CircularProgress, Stack, Typography, useTheme} from '@mui/material';
import {Refresh} from '@mui/icons-material';
import IconButton from '@mui/material/IconButton';
import {
    KasDotsMenu,
    KasDotsMenuItemProps,
    KasLink,
    KasLoadingStatusIcon,
    KasSwitch,
    KasSwitchWhen,
} from '@/components';
import {useTransactionsEmployee} from './../../useTransactionsEmployee';
import {EmployeeTitle} from './../../../../components';
import {TransactionsEmployeeModalType} from './../../interfaces';

export const TransactionsEmployeeHead = () => {
    const {palette} = useTheme();
    const {
        setOpenModal,
        employee,
        dataState: {loading, error},
        loadData,
        loansState,
        loadLoans,
    } = useTransactionsEmployee();

    const menuItems: KasDotsMenuItemProps[] = [
        {
            ContentComponent: <Typography variant='body1'>Add Transaction</Typography>,
            onClick: () =>
                setOpenModal({
                    type: TransactionsEmployeeModalType.Add_Transaction,
                }),
        },
        {
            ContentComponent: (
                <KasSwitch>
                    <KasSwitchWhen condition={!!loansState.error}>
                        <span style={{color: palette.error.main}}>{loansState.error}</span>
                        <KasLink onClick={loadLoans}>Try Again</KasLink>
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={!loansState.error}>
                        <Stack flexDirection='row' alignItems='center' columnGap={1}>
                            <Typography variant='body1'>
                                Add Check{' '}
                                {!loansState.loading && !loansState.data?.length ? '(No Loans)' : ''}
                            </Typography>
                            {loansState.loading && <CircularProgress size={16} />}
                        </Stack>
                    </KasSwitchWhen>
                </KasSwitch>
            ),
            disabled: !loansState.data || loansState.data.length === 0 || loansState.loading,
            onClick: () =>
                setOpenModal({
                    type: TransactionsEmployeeModalType.Add_Check,
                }),
        },
    ];

    return (
        <Stack flexDirection='row' alignItems='center' justifyContent='space-between' spacing={2}>
            <Typography variant='h3'>
                <EmployeeTitle data={employee.label} />
            </Typography>
            <Stack flexDirection='row' columnGap={1}>
                <KasSwitch>
                    <KasSwitchWhen condition={loading || !!error}>
                        <KasLoadingStatusIcon loading={loading} loadingError={error} />
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={!loading}>
                        <IconButton title={'Refresh'} onClick={loadData}>
                            <Refresh />
                        </IconButton>
                    </KasSwitchWhen>
                </KasSwitch>
                <KasDotsMenu menuItems={menuItems} />
            </Stack>
        </Stack>
    );
};
