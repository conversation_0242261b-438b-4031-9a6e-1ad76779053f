import React, {useMemo} from 'react';
import {KasInfo} from '@/components';
import {Grid2, useTheme, Stack} from '@mui/material';
import {EmployeeTransactionFormValues} from './../../schema';
import {LookupDTO} from '@/models';
import {toCurrency} from '@/utils/FormatUtils';
import {East} from '@mui/icons-material';

interface BalanceCalculatorProps {
    values: EmployeeTransactionFormValues;
    balance: LookupDTO;
}

export const BalanceCalculator = ({values, balance}: BalanceCalculatorProps) => {
    const {palette} = useTheme();

    const accountBalanceTo = useMemo(() => {
        if (!balance || !values.amount || !values.amountType) {
            return null;
        }

        const amount = Number(values.amount) * Number(values.amountType);
        const newBalance = Number(balance.params?.balance) + amount;

        return toCurrency(newBalance);
    }, [values.amount, values.amountType]);

    const loanBalanceTo = useMemo(() => {
        if (!values.loan || !values.amount || !values.amountType) {
            return null;
        }

        const amount = Number(values.amount) * Number(values.amountType);
        const newBalance = Number(values.loan.params?.balance) + amount;

        return toCurrency(newBalance);
    }, [values.loan, values.amount, values.amountType]);

    const balanceColor = Number(values.amountType) > 0 ? palette.success.main : palette.error.main;

    return (
        <>
            <Grid2 size={4}>
                <KasInfo label='Account Balance'>
                    {balance?.params?.balance ? (
                        <Stack flexDirection='row' alignItems='center' columnGap={1}>
                            <span>{toCurrency(balance.params.balance)}</span>
                            {accountBalanceTo && (
                                <>
                                    <East fontSize='small' />
                                    <span style={{color: balanceColor}}>{accountBalanceTo}</span>
                                </>
                            )}
                        </Stack>
                    ) : null}
                </KasInfo>
            </Grid2>
            <Grid2 size={4}>
                <KasInfo label='Loan Balance'>
                    {values.loan?.params?.balance ? (
                        <Stack flexDirection='row' alignItems='center' columnGap={1}>
                            <span>{toCurrency(values.loan.params.balance)}</span>
                            {loanBalanceTo && (
                                <>
                                    <East fontSize='small' />
                                    <span style={{color: balanceColor}}>{loanBalanceTo}</span>
                                </>
                            )}
                        </Stack>
                    ) : null}
                </KasInfo>
            </Grid2>
        </>
    );
};
