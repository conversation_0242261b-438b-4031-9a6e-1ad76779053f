import React, {useEffect, useState} from 'react';
import {apiRequest} from '@/utils/AxiosUtils';
import {OperatorTransactionAuditModel} from '@/interfaces';
import {TableView} from '@/views';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {Completable} from '@/interfaces';
import {TransactionAuditsTable} from './tables';

export const TransactionAudits = ({gid}: {gid: number}) => {
    const [dataState, setDataState] = useState(getDefaultState<OperatorTransactionAuditModel[]>);

    const loadData = async () => {
        const url = `/api/secured/operator/transactions/${gid}/audit`;

        setDataState(getLoadingState(dataState));
        const response: Completable<OperatorTransactionAuditModel[]> = await apiRequest(url);
        setDataState(getLoadedState(response));
    };

    useEffect(() => {
        loadData().then();
    }, []);

    return (
        <TableView<OperatorTransactionAuditModel>
            withTableActions={true}
            loading={dataState.loading}
            error={dataState.error}
            data={dataState.data}
            columns={TransactionAuditsTable}
            tableName={`Transaction Audits for ID: ${gid}`}
            sortingColumns={[{id: 'create_time', desc: true}]}
        />
    );
};
