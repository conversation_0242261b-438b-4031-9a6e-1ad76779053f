import React, {createContext, useContext, useEffect, useState} from 'react';
import {apiRequest} from '@/utils/AxiosUtils';
import {Completable, DataStateInterface, OperatorTransactionModel, SelectModel} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {TransactionsEmployeeModalProps} from './interfaces';

interface TransactionsEmployeeContextModel {
    dataState: DataStateInterface<OperatorTransactionModel[]>;
    loadData: () => Promise<void>;
    employee: SelectModel<number>;
    openModal: TransactionsEmployeeModalProps | null;
    setOpenModal: (value: TransactionsEmployeeModalProps | null) => void;
    transactionTypesState: DataStateInterface<SelectModel<string>[]>;
    loadTransactionTypes: () => Promise<void>;
}

const TransactionsEmployeeContext = createContext<TransactionsEmployeeContextModel | undefined>(undefined);

interface TransactionsEmployeeProviderProps {
    children: React.ReactNode;
    employee: SelectModel<number>;
}

export const TransactionsEmployeeProvider = ({children, employee}: TransactionsEmployeeProviderProps) => {
    const [transactionTypesState, setTransactionTypesState] = useState(
        getDefaultState<SelectModel<string>[]>,
    );
    const [dataState, setDataState] = useState(getDefaultState<OperatorTransactionModel[]>);
    const [openModal, setOpenModal] = useState<TransactionsEmployeeModalProps | null>(null);

    const loadData = async () => {
        const url = `/api/secured/operator/transactions/employee/${employee.value}`;

        setDataState(getLoadingState(dataState));
        const response: Completable<OperatorTransactionModel[]> = await apiRequest(url);
        setDataState(getLoadedState(response));
    };

    const loadTransactionTypes = async () => {
        const url = `/api/secured/ui/lookup/db/tx`;

        setTransactionTypesState(getLoadingState(transactionTypesState));
        const response: Completable<SelectModel<string>[]> = await apiRequest(url);
        setTransactionTypesState(getLoadedState(response));
    };

    useEffect(() => {
        loadData().then();
        loadTransactionTypes().then();
    }, []);

    const value: TransactionsEmployeeContextModel = {
        dataState,
        loadData,
        employee,
        openModal,
        setOpenModal,
        transactionTypesState,
        loadTransactionTypes,
    };

    return (
        <TransactionsEmployeeContext.Provider value={value}>{children}</TransactionsEmployeeContext.Provider>
    );
};

export function useTransactionsEmployee() {
    const context = useContext(TransactionsEmployeeContext);
    if (!context) {
        throw new Error('useTransactionsEmployee must be used within TransactionsEmployeeProvider');
    }
    return context;
}
