import React from 'react';
import {useTransactionsEmployee} from './useTransactionsEmployee';
import {TableView} from '@/views';
import {OperatorTransactionModel} from '@/interfaces';
import {Divider, Paper, Stack} from '@mui/material';
import {TransactionsEmployeeTableColumns} from './tables';
import {TransactionsEmployeeHead, TransactionsEmployeeModal} from './components';

export const TransactionsEmployee = () => {
    const {
        dataState: {loading, error, data},
    } = useTransactionsEmployee();

    return (
        <Paper elevation={0}>
            <Stack spacing={2} px={5} py={2}>
                <TransactionsEmployeeHead />
                <Divider />
                <TableView<OperatorTransactionModel>
                    withTableActions={true}
                    loading={loading}
                    error={error}
                    data={data}
                    columns={TransactionsEmployeeTableColumns}
                    tableName='Employee Transactions'
                    sortingColumns={[{id: 'effective_date', desc: true}]}
                />
            </Stack>
            <TransactionsEmployeeModal />
        </Paper>
    );
};
