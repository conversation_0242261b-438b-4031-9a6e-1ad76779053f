import React from 'react';
import {useTransactions} from '../../../../useTransactions';
import {CallMade} from '@mui/icons-material';
import {ActionCell} from '@/components/table/cells';
import {SelectModel} from '@/interfaces';

export const OpenResultItemButton = ({data}: {data: SelectModel<number>}) => {
    const {selectEmployee} = useTransactions();

    return (
        <ActionCell
            Icon={<CallMade color='primary' titleAccess='Show Details' />}
            onClick={() => selectEmployee(data)}
        />
    );
};
