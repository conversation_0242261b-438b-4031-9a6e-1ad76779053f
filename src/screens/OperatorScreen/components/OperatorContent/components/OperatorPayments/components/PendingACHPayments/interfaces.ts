import {OperatorPaymentModel} from '@/interfaces';

export enum PendingACHPaymentsItemModal {
    Set_Processed = 'Adjust Processed Amount',
    Cancel_Request = 'Cancel ACH Request',
}

export type PendingACHPaymentsModalProps =
    | {
          type: PendingACHPaymentsItemModal.Set_Processed;
          props: {data: OperatorPaymentModel};
      }
    | {
          type: PendingACHPaymentsItemModal.Cancel_Request;
          props: {data: OperatorPaymentModel};
      };
