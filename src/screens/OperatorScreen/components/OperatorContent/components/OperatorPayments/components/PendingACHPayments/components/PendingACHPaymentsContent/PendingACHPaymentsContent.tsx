import {OperatorPaymentModel} from '@/interfaces';
import {usePendingACHPayments} from './../../usePendingACHPayments';
import {PendingPaymentsTable} from './tables';
import {Row} from '@tanstack/react-table';
import {TableView} from '@/views';
import {OpenPaymentDetails} from '@/screens/OperatorScreen/components/OperatorContent/components/payment';

export const PendingACHPaymentsContent = () => {
    const {dataState} = usePendingACHPayments();

    return (
        <TableView<OperatorPaymentModel>
            withTableActions={true}
            loading={dataState.loading}
            error={dataState.error}
            data={dataState.data}
            columns={PendingPaymentsTable}
            tableName='Pending ACH Payments'
            sortingColumns={[
                {id: 'schedule_date', desc: true},
                {id: 'loan_id', desc: true},
                {id: 'employee_id', desc: false},
            ]}
            renderExpand={(row: Row<OperatorPaymentModel>) => <OpenPaymentDetails data={row.original} />}
        />
    );
};
