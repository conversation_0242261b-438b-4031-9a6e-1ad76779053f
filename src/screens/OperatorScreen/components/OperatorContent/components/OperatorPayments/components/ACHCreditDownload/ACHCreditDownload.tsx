import React, {useState} from 'react';
import {Button, Checkbox, FormControlLabel, Grid2, Paper, Typography} from '@mui/material';
import Box from '@mui/material/Box';
import {KasDateRangePicker} from '@/components';
import {isValidRangeDate} from '@/utils/DateUtils';
import {DayJsRangeModel} from '@/interfaces';
import {useFormik} from 'formik';
import {useDownloadBlob} from '@/hooks/useDownloadBlob';
import {getParamValueAsBinary} from '@/utils/AxiosUtils';
import dayjs from 'dayjs';

interface ACHCreditDownloadValues {
    ufd: boolean;
}

export const ACHCreditDownload = () => {
    const [submitting, setSubmitting] = useState(false);
    const {getBlob} = useDownloadBlob();
    const [dateRange, setDateRange] = useState<DayJsRangeModel>({start: dayjs(), end: dayjs()});

    const onSubmit = async (values: ACHCreditDownloadValues) => {
        const params = JSON.stringify({
            path: `/secured/operator/employee/ach/credit/${dateRange.start?.format('YYYYMMDD')}/${dateRange.end?.format('YYYYMMDD')}`,
            params: {
                ufd: getParamValueAsBinary(values.ufd.toString()),
            },
        });

        setSubmitting(true);
        await getBlob(params);
        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            ufd: true,
        },
        onSubmit,
    });

    return (
        <Paper elevation={0}>
            <Box p={2}>
                <form onSubmit={formik.handleSubmit}>
                    <Grid2 container spacing={2} alignItems='center'>
                        <Grid2 size={2.5}>
                            <Typography variant='subtitle1'>ACH Credit Download</Typography>
                        </Grid2>
                        <Grid2 size={5}>
                            <KasDateRangePicker
                                value={dateRange}
                                disabled={submitting}
                                onChange={setDateRange}
                            />
                        </Grid2>
                        <Grid2 size={2.5}>
                            <FormControlLabel
                                disabled={submitting}
                                label='Use Funding Date'
                                control={
                                    <Checkbox
                                        size='small'
                                        name='ufd'
                                        onChange={formik.handleChange}
                                        checked={formik.values.ufd}
                                    />
                                }
                            />
                        </Grid2>
                        <Grid2 size={2} ml='auto'>
                            <Button
                                fullWidth
                                variant='contained'
                                type='submit'
                                title='Download ACH Credit File Package'
                                loading={submitting}
                                disabled={submitting || !isValidRangeDate(dateRange)}>
                                Download
                            </Button>
                        </Grid2>
                    </Grid2>
                </form>
            </Box>
        </Paper>
    );
};
