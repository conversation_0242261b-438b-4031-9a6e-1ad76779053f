import React from 'react';
import {
    ACHCreditDownload,
    ACHDebitDownload,
    PendingACHFiles,
    PendingACHFilesProvider,
    PendingACHPayments,
    PendingACHPaymentsProvider,
} from './components';
import {Grid2} from '@mui/material';

export const OperatorPayments = () => {
    return (
        <Grid2 container spacing={2}>
            <Grid2 size={12}>
                <ACHCreditDownload />
            </Grid2>
            <Grid2 size={12}>
                <ACHDebitDownload />
            </Grid2>
            <Grid2 size={12}>
                <PendingACHFilesProvider>
                    <PendingACHFiles />
                </PendingACHFilesProvider>
            </Grid2>
            <Grid2 size={12}>
                <PendingACHPaymentsProvider>
                    <PendingACHPayments />
                </PendingACHPaymentsProvider>
            </Grid2>
        </Grid2>
    );
};
