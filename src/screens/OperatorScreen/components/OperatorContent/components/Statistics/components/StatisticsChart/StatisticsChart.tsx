import React from 'react';
import {Divider, Paper, Stack} from '@mui/material';
import Box from '@mui/material/Box';
import {useStatisticsChart} from './useStatisticsChart';
import {KasLoadingBackD<PERSON>, <PERSON><PERSON><PERSON><PERSON>ding<PERSON><PERSON><PERSON>, Kas<PERSON>oR<PERSON><PERSON><PERSON>, KasS<PERSON>, KasSwitchWhen} from '@/components';
import {NoResultsView} from '@/views';
import {StatisticsChartBar, StatisticsHead} from './components';

export const StatisticsChart = () => {
    const {regularStatisticsState, prequalStatisticsState, chartData, loadStatistics} = useStatisticsChart();
    const loading = regularStatisticsState.loading || prequalStatisticsState.loading;
    const error = regularStatisticsState.error || prequalStatisticsState.error;
    const notLoaded = !regularStatisticsState.data || !prequalStatisticsState.data;

    return (
        <Paper elevation={0}>
            <Stack p={2} spacing={2}>
                <StatisticsHead />
                <Divider />
                <Box position='relative'>
                    {loading && <KasLoadingBackDrop />}
                    <KasSwitch>
                        <KasSwitchWhen condition={!!error}>
                            <KasLoadingError view='contained' error={error} onTryAgain={loadStatistics} />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={notLoaded}>
                            <NoResultsView text='Search result will appear here' />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={chartData && chartData.length > 0}>
                            <StatisticsChartBar data={chartData} />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={chartData && chartData.length === 0}>
                            <KasNoResults text='No records found' p={2} bgcolor='var(--color-grey)' />
                        </KasSwitchWhen>
                    </KasSwitch>
                </Box>
            </Stack>
        </Paper>
    );
};
