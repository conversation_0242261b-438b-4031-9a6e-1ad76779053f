import React, {createContext, useCallback, useContext, useMemo, useState} from 'react';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {
    Completable,
    DataStateInterface,
    OperatorStatisticChartModel,
    OperatorStatisticModel,
} from '@/interfaces';
import {uniqElements} from '@/utils/ArrayUtils';

interface StatisticsChartContextModel {
    regularStatisticsState: DataStateInterface<OperatorStatisticModel[]>;
    prequalStatisticsState: DataStateInterface<OperatorStatisticModel[]>;
    loadStatistics: () => Promise<void>;
    chartData: OperatorStatisticChartModel[];
    hiddenBars: string[];
    updateHiddenBars: (bar: string) => void;
}

const StatisticsChartContext = createContext<StatisticsChartContextModel | undefined>(undefined);

export const StatisticsChartProvider = ({children}: {children: React.ReactNode}) => {
    const [regularStatisticsState, setRegularStatisticsState] = useState(
        getDefaultState<OperatorStatisticModel[]>,
    );
    const [prequalStatisticsState, setPrequalStatisticsState] = useState(
        getDefaultState<OperatorStatisticModel[]>,
    );
    const [hiddenBars, setHiddenBars] = useState<string[]>([]);

    const updateHiddenBars = useCallback(
        (dataKey: string) => {
            const newValue = hiddenBars.includes(dataKey)
                ? hiddenBars.filter((key) => key !== dataKey)
                : [...hiddenBars, dataKey];

            setHiddenBars(newValue);
        },
        [hiddenBars],
    );

    const mergeByDate = (data: OperatorStatisticModel[]) => {
        const groupedByDate: Record<string, OperatorStatisticModel> = {};

        data.forEach((item) => {
            if (!groupedByDate[item.date]) {
                groupedByDate[item.date] = {
                    date: item.date,
                    declined: 0,
                    bank: 0,
                    verification: 0,
                    loans: 0,
                    amount: 0,
                    approved_count: 0,
                    approved_amount: 0,
                    approved_count_refi: 0,
                    approved_amount_refi: 0,
                };
            }

            groupedByDate[item.date] = {
                date: item.date,
                declined: groupedByDate[item.date].declined + item.declined,
                bank: groupedByDate[item.date].bank + item.bank,
                verification: groupedByDate[item.date].verification + item.verification,
                loans: groupedByDate[item.date].loans + item.loans,
                amount: groupedByDate[item.date].amount + item.amount,
                approved_count: groupedByDate[item.date].approved_count + item.approved_count,
                approved_amount: groupedByDate[item.date].approved_amount + item.approved_amount,
                approved_count_refi: groupedByDate[item.date].approved_count_refi + item.approved_count_refi,
                approved_amount_refi:
                    groupedByDate[item.date].approved_amount_refi + item.approved_amount_refi,
            };
        });

        return groupedByDate;
    };

    const chartData: OperatorStatisticChartModel[] = useMemo(() => {
        if (regularStatisticsState.data && prequalStatisticsState.data) {
            const uniqDates = uniqElements(
                [...regularStatisticsState.data, ...prequalStatisticsState.data],
                (item) => item.date,
            );
            const regularStatistics = mergeByDate(regularStatisticsState.data);
            const prequalStatistics = mergeByDate(prequalStatisticsState.data);
            const resultArr = uniqDates
                .map((date) => {
                    const regular = regularStatistics[date] || {
                        date,
                        declined: 0,
                        bank: 0,
                        verification: 0,
                        loans: 0,
                        amount: 0,
                        approved_count: 0,
                        approved_amount: 0,
                        approved_count_refi: 0,
                        approved_amount_refi: 0,
                    };
                    const prequal = prequalStatistics[date] || {
                        date,
                        declined: 0,
                        bank: 0,
                        verification: 0,
                        loans: 0,
                        amount: 0,
                        approved_count: 0,
                        approved_amount: 0,
                        approved_count_refi: 0,
                        approved_amount_refi: 0,
                    };

                    return {
                        date,
                        approved: (regular.approved_count || 0) + (prequal.approved_count || 0),
                        declined: (regular.declined || 0) + (prequal.declined || 0),
                        bank: (regular.bank || 0) + (prequal.bank || 0),
                        loans: regular.loans || 0,
                        verification: (regular.verification || 0) + (prequal.verification || 0),
                        amount: regular.amount || 0,
                        approved_count: regular.approved_count || 0,
                        approved_amount: regular.approved_amount || 0,
                        prequal_approved_count: prequal.approved_count || 0,
                        prequal_approved_amount: prequal.approved_amount || 0,
                        approved_count_refi:
                            (regular.approved_count_refi || 0) + (prequal.approved_count_refi || 0),
                        approved_amount_refi:
                            (regular.approved_amount_refi || 0) + (prequal.approved_amount_refi || 0),
                    };
                })
                .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

            return resultArr;
        }

        return [];
    }, [regularStatisticsState.data, prequalStatisticsState.data]);

    const loadRegularStatisticsData = async () => {
        const url = `/api/secured/operator/statistics?prequal=false`;

        setRegularStatisticsState(getLoadingState(regularStatisticsState));
        const response: Completable<OperatorStatisticModel[]> = await apiRequest(url);
        setRegularStatisticsState(getLoadedState(response));
    };

    const loadPrequalStatisticsData = async () => {
        const url = `/api/secured/operator/statistics?prequal=true`;

        setPrequalStatisticsState(getLoadingState(prequalStatisticsState));
        const response: Completable<OperatorStatisticModel[]> = await apiRequest(url);
        setPrequalStatisticsState(getLoadedState(response));
    };

    const loadStatistics = async () => {
        await Promise.all([loadRegularStatisticsData(), loadPrequalStatisticsData()]);
    };

    const value: StatisticsChartContextModel = {
        regularStatisticsState,
        prequalStatisticsState,
        loadStatistics,
        chartData,
        hiddenBars,
        updateHiddenBars,
    };

    return <StatisticsChartContext.Provider value={value}>{children}</StatisticsChartContext.Provider>;
};

export function useStatisticsChart() {
    const context = useContext(StatisticsChartContext);
    if (!context) {
        throw new Error('useStatisticsChart must be used within StatisticsChartProvider');
    }
    return context;
}
