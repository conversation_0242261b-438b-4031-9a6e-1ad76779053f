import React from 'react';
import {OperatorStatisticChartModel} from '@/interfaces';
import dayjs from 'dayjs';
import Box from '@mui/material/Box';
import {useTheme} from '@mui/material';
import {
    Bar,
    CartesianGrid,
    ComposedChart,
    Legend,
    Line,
    ResponsiveContainer,
    Tooltip,
    XAxis,
    YAxis,
} from 'recharts';
import {SMAInfo, StatisticsChartTooltip} from './components';
import {toCurrencyCompact} from '@/utils/FormatUtils';
import {KasCustomLegend} from '@/components';
import {useStatisticsChart} from './../../useStatisticsChart';
import {BarConfig, LineConfig} from './interfaces';

export const StatisticsChartBar = ({data}: {data: OperatorStatisticChartModel[]}) => {
    const {hiddenBars, updateHiddenBars} = useStatisticsChart();
    const {palette} = useTheme();
    const barConfigs: BarConfig[] = [
        {dataKey: 'loans', stackId: 'loans', name: 'Loans', fill: palette.primary.main},
        {dataKey: 'approved', stackId: 'approved', name: 'Approved', fill: palette.success.main},
        {
            dataKey: 'verification',
            stackId: 'declined',
            name: 'Declined (Verification)',
            fill: palette.secondary.main,
        },
        {dataKey: 'bank', stackId: 'declined', name: 'Declined (Bank)', fill: palette.warning.main},
        {dataKey: 'declined', stackId: 'declined', name: 'Declined (UW)', fill: palette.error.main},
    ];
    const lineConfigs: LineConfig[] = [
        {dataKey: 'amount', name: 'Loan Amount', stroke: palette.action.disabled},
    ];

    const legendPayload = [
        ...barConfigs.map(({fill, name, dataKey}) => ({
            value: name,
            type: 'rect' as const,
            id: dataKey,
            color: fill,
            dataKey,
        })),
        ...lineConfigs.map(({stroke, name, dataKey}) => ({
            value: name,
            type: 'line' as const,
            id: dataKey,
            color: stroke,
            dataKey,
        })),
    ];

    return (
        <Box>
            <SMAInfo data={data} />
            <ResponsiveContainer width='100%' height={400}>
                <ComposedChart data={data} margin={{top: 20, right: 20, left: 20, bottom: 5}} barGap={2}>
                    <CartesianGrid strokeDasharray='3 3' />
                    <XAxis
                        dataKey='date'
                        tickFormatter={(date) => dayjs(date).format('MM-DD')}
                        style={{fontSize: 'var(--small-text-size)'}}
                    />
                    <YAxis
                        yAxisId='count'
                        orientation='left'
                        style={{fontSize: 'var(--small-text-size)'}}
                        label={{
                            value: 'Count',
                            angle: -90,
                            position: 'insideLeft',
                        }}
                    />
                    <YAxis
                        yAxisId='amount'
                        orientation='right'
                        style={{fontSize: 'var(--small-text-size)'}}
                        tickFormatter={toCurrencyCompact}
                        label={{
                            value: 'Amount',
                            angle: 90,
                            position: 'insideRight',
                        }}
                    />
                    <Tooltip content={(props) => <StatisticsChartTooltip {...props} chartData={data} />} />
                    <Legend
                        content={() => (
                            <KasCustomLegend
                                payload={legendPayload}
                                hiddenKeys={hiddenBars}
                                onClick={updateHiddenBars}
                            />
                        )}
                    />

                    {barConfigs.map((config) =>
                        !hiddenBars.includes(config.dataKey) ? (
                            <Bar
                                key={config.dataKey}
                                yAxisId='count'
                                dataKey={config.dataKey}
                                stackId={config.stackId}
                                name={config.name}
                                fill={config.fill}
                            />
                        ) : null,
                    )}

                    {lineConfigs.map((config) =>
                        !hiddenBars.includes(config.dataKey) ? (
                            <Line
                                key={config.dataKey}
                                yAxisId='amount'
                                type='monotone'
                                dataKey={config.dataKey}
                                name={config.name}
                                stroke={config.stroke}
                                strokeWidth={2}
                                dot={false}
                            />
                        ) : null,
                    )}
                </ComposedChart>
            </ResponsiveContainer>
        </Box>
    );
};
