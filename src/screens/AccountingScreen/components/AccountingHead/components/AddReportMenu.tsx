import React from 'react';
import MenuItem from '@mui/material/MenuItem';
import {generateUniqueId} from '@/utils/UniqueIdUtils';
import {Button} from '@mui/material';
import {ArrowDropDown, ArrowDropUp} from '@mui/icons-material';
import Menu from '@mui/material/Menu';
import {useAccounting} from '@/screens/AccountingScreen';
import {AccountingItemModel} from '@/interfaces';

export const AddReportMenu = () => {
    const {availableItems, addActiveItem} = useAccounting();
    const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);
    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
    };
    const addItem = (value: AccountingItemModel) => {
        addActiveItem({...value, id: generateUniqueId()});
        setAnchorEl(null);
    };

    return (
        <>
            <Button
                variant='contained'
                aria-controls={open ? 'item-menu' : undefined}
                endIcon={open ? <ArrowDropUp /> : <ArrowDropDown />}
                onClick={handleClick}>
                ADD REPORT
            </Button>
            <Menu id='item-menu' anchorEl={anchorEl} open={open} onClose={() => setAnchorEl(null)}>
                {availableItems.map((item) => (
                    <MenuItem key={item.id} onClick={() => addItem(item)}>
                        {item.content}
                    </MenuItem>
                ))}
            </Menu>
        </>
    );
};
