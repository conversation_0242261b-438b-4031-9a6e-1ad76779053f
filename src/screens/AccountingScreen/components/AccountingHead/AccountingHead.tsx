import './styles.scss';

import React, {useMemo} from 'react';
import {Button, Stack, Typography} from '@mui/material';
import {DeleteOutline, UnfoldLess, UnfoldMore} from '@mui/icons-material';
import {useAccounting} from '@/screens/AccountingScreen';
import {AddComparisonMenu, AddReportMenu} from './components';

export const AccountingHead = () => {
    const {updateActiveItems, activeItems} = useAccounting();

    const onRemoveAll = () => {
        updateActiveItems([]);
    };

    const onShowAll = () => {
        updateActiveItems(
            activeItems.map((value) => ({
                ...value,
                expanded: !!(value.data || value.loadingError),
            })),
        );
    };

    const onHideAll = () => {
        updateActiveItems(
            activeItems.map((value) => ({
                ...value,
                expanded: false,
            })),
        );
    };

    const showAllDisabled = useMemo(() => {
        return !activeItems
            .map(({expanded, data, loadingError}) => (data || loadingError ? expanded : true))
            .includes(false);
    }, [activeItems]);

    const hideAllDisabled = useMemo(() => {
        return !activeItems.map(({expanded}) => expanded).includes(true);
    }, [activeItems]);

    return (
        <div className='kas-accounting-head'>
            <Typography variant='h3'>Accounting Dashboard</Typography>
            <Stack direction='row' spacing={1}>
                {activeItems.length > 0 && (
                    <>
                        <Button
                            variant='outlined'
                            color='error'
                            size='small'
                            title='Remove All Sections'
                            onClick={onRemoveAll}>
                            <DeleteOutline fontSize='small' />
                        </Button>
                        <Button
                            variant='outlined'
                            size='small'
                            title='Hide All Sections'
                            disabled={hideAllDisabled}
                            onClick={onHideAll}>
                            <UnfoldLess fontSize='small' />
                        </Button>
                        <Button
                            variant='outlined'
                            size='small'
                            title='Show All Sections'
                            disabled={showAllDisabled}
                            onClick={onShowAll}>
                            <UnfoldMore fontSize='small' />
                        </Button>
                    </>
                )}
                <AddComparisonMenu />
                <AddReportMenu />
            </Stack>
        </div>
    );
};
