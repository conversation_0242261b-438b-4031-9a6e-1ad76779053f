import React, {createContext, useContext, useState} from 'react';

import {KasComparisonTableView} from '@/components/KasTable/KasComparisonTable';

interface ComparisonDetailsContextModel {
    activeButton: KasComparisonTableView;
    setActiveButton: (value: KasComparisonTableView) => void;
}

const ComparisonDetailsContext = createContext<ComparisonDetailsContextModel>({
    activeButton: KasComparisonTableView.Totals,
    setActiveButton: () => null,
});

export const ComparisonDetailsProvider = ({children}: {children: React.ReactNode}) => {
    const [activeButton, setActiveButton] = useState<KasComparisonTableView>(KasComparisonTableView.Totals);

    const value: ComparisonDetailsContextModel = {
        activeButton,
        setActiveButton,
    };

    return <ComparisonDetailsContext.Provider value={value}>{children}</ComparisonDetailsContext.Provider>;
};

export function useComparisonDetails() {
    const context = useContext(ComparisonDetailsContext);
    if (!context) {
        throw new Error('useComparisonDetails must be used within ComparisonDetailsProvider');
    }
    return context;
}
