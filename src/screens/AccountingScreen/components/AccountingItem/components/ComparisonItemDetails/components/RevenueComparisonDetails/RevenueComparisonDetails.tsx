import React, {useMemo} from 'react';
import {AccountingItemModel, AccountingRevenueType} from '@/interfaces';
import {ProjectedComparison, RevenueComparison} from './components';

interface ItemFormProps {
    item: AccountingItemModel;
}

export const RevenueComparisonDetails = ({item}: ItemFormProps) => {
    const renderForm = useMemo(() => {
        switch (item.filterType) {
            case AccountingRevenueType.Revenue:
                return <RevenueComparison itemData={item.data} />;
            case AccountingRevenueType.Tables_Projected:
                return <ProjectedComparison itemData={item.data} />;
            case AccountingRevenueType.Charge_Off:
                return null;
            case AccountingRevenueType.Billing:
                return null;
            default:
                return null;
        }
    }, [item]);

    return <>{renderForm}</>;
};
