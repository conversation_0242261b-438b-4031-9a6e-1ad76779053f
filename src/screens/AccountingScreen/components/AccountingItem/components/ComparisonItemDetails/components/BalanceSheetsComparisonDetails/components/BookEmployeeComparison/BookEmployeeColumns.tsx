import React, {useMemo} from 'react';
import {KasComparisonTable} from '@/components';
import {ColumnDef} from '@tanstack/react-table';
import {ComparisonAccountingTableModel, ComparisonColumnsTableModel} from '@/interfaces';
import {useComparisonDetails} from '@/screens/AccountingScreen/components/AccountingItem/components';
import {BookEmployeeColumnsTableColumns} from './tables';
import {ComparisonDetailsProps} from '@/screens/AccountingScreen/components/AccountingItem/components/ComparisonItemDetails/interface';

export const BookEmployeeColumns = ({itemData}: ComparisonDetailsProps<ComparisonAccountingTableModel>) => {
    const {activeButton, setActiveButton} = useComparisonDetails();

    const data: ComparisonColumnsTableModel<ComparisonAccountingTableModel>[] = useMemo(
        () =>
            Object.values(
                itemData.reduce((acc: {[key: string]: any}, curr: ComparisonAccountingTableModel) => {
                    const key = curr.step;

                    if (key !== null) {
                        if (!acc[key]) {
                            acc[key] = {
                                step: key,
                                title: curr.title,
                                date_first: null,
                                date_second: null,
                            };
                        }

                        if (!acc[key].date_first) {
                            acc[key].date_first = curr;
                        } else if (!acc[key].date_second) {
                            acc[key].date_second = curr;
                        }
                    }

                    return acc;
                }, {}),
            ),
        [itemData],
    );

    return (
        <KasComparisonTable<ComparisonColumnsTableModel<ComparisonAccountingTableModel>>
            columns={
                BookEmployeeColumnsTableColumns as ColumnDef<
                    ComparisonColumnsTableModel<ComparisonAccountingTableModel>,
                    unknown
                >[]
            }
            data={data}
            activeView={activeButton}
            onChangeView={setActiveButton}
        />
    );
};
