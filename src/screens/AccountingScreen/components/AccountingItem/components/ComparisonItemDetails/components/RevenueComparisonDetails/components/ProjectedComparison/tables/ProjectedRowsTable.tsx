import {AccessorFn, CellContext, DeepKeys} from '@tanstack/react-table';
import {RowsComparisonProjectedTableModel} from '@/interfaces';
import {AmountCell, DateCell} from '@/components/table/cells';
import {createColumn} from '@/utils/TableUtils';

const renderCell = (cell: CellContext<RowsComparisonProjectedTableModel, number | string | null>) => {
    return <AmountCell isMulticolor={!!cell.row.original.diff} data={Number(cell.getValue() || 0)} />;
};

const infoColumn = (
    accessor: AccessorFn<RowsComparisonProjectedTableModel> | DeepKeys<RowsComparisonProjectedTableModel>,
    header: string,
) => {
    return createColumn<RowsComparisonProjectedTableModel, string>(
        accessor,
        accessor as string,
        header,
        (data) => {
            const isDiff = data.row.original.diff;
            return (
                <DateCell data={data.getValue()} format='YYYY-MM'>
                    {isDiff ? ' Difference' : ''}
                </DateCell>
            );
        },
        undefined,
        false,
    );
};

const amountColumn = (
    accessor: AccessorFn<RowsComparisonProjectedTableModel> | DeepKeys<RowsComparisonProjectedTableModel>,
    header: string,
) => {
    return createColumn<RowsComparisonProjectedTableModel, string>(
        accessor,
        accessor as string,
        header,
        renderCell,
        undefined,
        false,
    );
};

export const ProjectedRowsTableColumns = [
    infoColumn('bucket', 'Bucket'),
    amountColumn('revenue_interest', 'Interest'),
    amountColumn('revenue_fee', 'Fee'),
    amountColumn('revenue_total', 'Total'),
];
