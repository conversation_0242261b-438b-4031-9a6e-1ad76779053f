import {createColumnHelper} from '@tanstack/react-table';
import {ComparisonColumnsTableModel, ComparisonRevenueTableModel} from '@/interfaces';
import {
    calculateColumnsDiff,
    calculateColumnsTotal,
    defaultColumnsAmountColumn,
    defaultInfoColumn,
    diffColumnsAmountColumn,
    revenueRealizedTotal,
    revenueTotal,
    revenueUnrealizedTotal,
    totalColumnsAmountColumn,
} from '@/utils/TableUtils';

const columnHelper = createColumnHelper<ComparisonColumnsTableModel<ComparisonRevenueTableModel>>();

const _defaultInfoColumn = defaultInfoColumn<ComparisonRevenueTableModel>;
const _defaultColumnsAmountColumn = defaultColumnsAmountColumn<ComparisonRevenueTableModel>;
const _diffColumnsAmountColumn = diffColumnsAmountColumn<ComparisonRevenueTableModel>;
const _calculateColumnsTotal = calculateColumnsTotal<ComparisonRevenueTableModel>;
const _totalColumnsAmountColumn = totalColumnsAmountColumn<ComparisonRevenueTableModel>;
const _calculateColumnsDiff = calculateColumnsDiff<ComparisonRevenueTableModel>;

export const RevenueColumnsTableColumns = [
    _defaultInfoColumn('entity_id', 'ID'),
    _defaultInfoColumn('name', 'Name'),
    columnHelper.group({
        id: 'unrealized-tables',
        header: 'Unrealized Revenue',
        columns: [
            _defaultColumnsAmountColumn('date_first.unrealized_interest', 'Interest 1'),
            _defaultColumnsAmountColumn('date_second.unrealized_interest', 'Interest 2'),
            _diffColumnsAmountColumn('unrealized_interest', 'Diff Interest'),
            _defaultColumnsAmountColumn('date_first.unrealized_origination_fee', 'Orig Fee 1'),
            _defaultColumnsAmountColumn('date_second.unrealized_origination_fee', 'Orig Fee 2'),
            _diffColumnsAmountColumn('unrealized_origination_fee', 'Diff Orig Fee'),
            _defaultColumnsAmountColumn('date_first.unrealized_processing_fee', 'Proc Fee 1'),
            _defaultColumnsAmountColumn('date_second.unrealized_processing_fee', 'Proc Fee 2'),
            _diffColumnsAmountColumn('unrealized_processing_fee', 'Diff Proc Fee'),
            _defaultColumnsAmountColumn('date_first.unrealized_other_fee', 'Other Fee 1'),
            _defaultColumnsAmountColumn('date_second.unrealized_other_fee', 'Other Fee 2'),
            _diffColumnsAmountColumn('unrealized_other_fee', 'Diff Other Fee'),
            _defaultColumnsAmountColumn('date_first.unrealized_pepm', 'PEPM 1'),
            _defaultColumnsAmountColumn('date_second.unrealized_pepm', 'PEPM 2'),
            _diffColumnsAmountColumn('unrealized_pepm', 'Diff PEPM'),
            _totalColumnsAmountColumn(
                'date_first_unrealized-subtotal',
                'Subtotal 1',
                _calculateColumnsTotal('date_first', revenueUnrealizedTotal),
            ),
            _totalColumnsAmountColumn(
                'date_second_unrealized-subtotal',
                'Subtotal 2',
                _calculateColumnsTotal('date_second', revenueUnrealizedTotal),
            ),
            _diffColumnsAmountColumn(
                'unrealized-subtotal',
                'Diff Subtotal',
                _calculateColumnsDiff(revenueUnrealizedTotal),
            ),
        ],
    }),
    columnHelper.group({
        id: 'realized-tables',
        header: 'Realized Revenue',
        columns: [
            _defaultColumnsAmountColumn('date_first.realized_interest', 'Interest 1'),
            _defaultColumnsAmountColumn('date_second.realized_interest', 'Interest 2'),
            _diffColumnsAmountColumn('realized_interest', 'Diff Interest'),
            _defaultColumnsAmountColumn('date_first.realized_origination_fee', 'Orig Fee 1'),
            _defaultColumnsAmountColumn('date_second.realized_origination_fee', 'Orig Fee 2'),
            _diffColumnsAmountColumn('realized_origination_fee', 'Diff Orig Fee'),
            _defaultColumnsAmountColumn('date_first.realized_processing_fee', 'Proc Fee 1'),
            _defaultColumnsAmountColumn('date_second.realized_processing_fee', 'Proc Fee 2'),
            _diffColumnsAmountColumn('realized_processing_fee', 'Diff Proc Fee'),
            _defaultColumnsAmountColumn('date_first.realized_other_fee', 'Other Fee 1'),
            _defaultColumnsAmountColumn('date_second.realized_other_fee', 'Other Fee 2'),
            _diffColumnsAmountColumn('realized_other_fee', 'Diff Other Fee'),
            _defaultColumnsAmountColumn('date_first.pepm_received', 'PEPM 1'),
            _defaultColumnsAmountColumn('date_second.pepm_received', 'PEPM 2'),
            _diffColumnsAmountColumn('pepm_received', 'Diff PEPM'),
            _totalColumnsAmountColumn(
                'date_first_realized-subtotal',
                'Subtotal 1',
                _calculateColumnsTotal('date_first', revenueRealizedTotal),
            ),
            _totalColumnsAmountColumn(
                'date_second_realized-subtotal',
                'Subtotal 2',
                _calculateColumnsTotal('date_second', revenueRealizedTotal),
            ),
            _diffColumnsAmountColumn(
                'realized-subtotal',
                'Diff Subtotal',
                _calculateColumnsDiff(revenueRealizedTotal),
            ),
        ],
    }),
    columnHelper.group({
        id: 'other-tables',
        header: 'Other Revenue',
        columns: [
            _defaultColumnsAmountColumn('date_first.account_credit', 'Credit 1'),
            _defaultColumnsAmountColumn('date_second.account_credit', 'Credit 2'),
            _diffColumnsAmountColumn('account_credit', 'Diff Credit'),
            _defaultColumnsAmountColumn('date_first.other_income', 'Other Income 1'),
            _defaultColumnsAmountColumn('date_second.other_income', 'Other Income 2'),
            _diffColumnsAmountColumn('other_income', 'Diff Other Income'),
        ],
    }),
    _defaultColumnsAmountColumn('date_first.reconciles.EFFECTIVE', 'Deferrer 1'),
    _defaultColumnsAmountColumn('date_second.reconciles.EFFECTIVE', 'Deferrer 2'),
    _diffColumnsAmountColumn('reconciles', 'Diff Deferrer'),
    _totalColumnsAmountColumn(
        'date_first_total',
        'Total 1',
        _calculateColumnsTotal('date_first', revenueTotal),
    ),
    _totalColumnsAmountColumn(
        'date_second_total',
        'Total 2',
        _calculateColumnsTotal('date_second', revenueTotal),
    ),
    _diffColumnsAmountColumn('total', 'Diff Total', _calculateColumnsDiff(revenueTotal)),
];
