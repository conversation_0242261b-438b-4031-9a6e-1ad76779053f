import React, {useMemo} from 'react';
import {KasComparisonTable} from '@/components';
import {ColumnDef} from '@tanstack/react-table';
import {ComparisonRevenueTableModel} from '@/interfaces';
import {RevenueTotalTableColumns} from './tables';
import {useComparisonDetails} from '@/screens/AccountingScreen/components/AccountingItem/components';
import {ComparisonDetailsProps} from '@/screens/AccountingScreen/components/AccountingItem/components/ComparisonItemDetails/interface';

export const RevenueTotals = ({itemData}: ComparisonDetailsProps<ComparisonRevenueTableModel>) => {
    const {activeButton, setActiveButton} = useComparisonDetails();

    const data: ComparisonRevenueTableModel[] = useMemo(
        () =>
            itemData
                .map((item) => ({
                    ...item,
                    reconciles: {
                        EFFECTIVE: item.reconciles.EFFECTIVE,
                    },
                }))
                .reduce(
                    (accumulator: ComparisonRevenueTableModel[], current: ComparisonRevenueTableModel) => {
                        const existingObject = accumulator.find(
                            (item: ComparisonRevenueTableModel) =>
                                item.comparisonDate === current.comparisonDate,
                        );
                        if (existingObject) {
                            existingObject.unrealized_interest += current.unrealized_interest;
                            existingObject.unrealized_origination_fee += current.unrealized_origination_fee;
                            existingObject.unrealized_processing_fee += current.unrealized_processing_fee;
                            existingObject.unrealized_other_fee += current.unrealized_other_fee;
                            existingObject.realized_interest += current.realized_interest;
                            existingObject.realized_origination_fee += current.realized_origination_fee;
                            existingObject.realized_processing_fee += current.realized_processing_fee;
                            existingObject.realized_other_fee += current.realized_other_fee;
                            existingObject.account_credit += current.account_credit;
                            existingObject.other_income += current.other_income;
                            existingObject.pepm_billed += current.pepm_billed;
                            existingObject.pepm_received += current.pepm_received;
                            existingObject.reconciles.EFFECTIVE += current.reconciles.EFFECTIVE;
                        } else {
                            accumulator.push(current);
                        }
                        return accumulator;
                    },
                    [],
                ),
        [itemData],
    );

    return (
        <KasComparisonTable<ComparisonRevenueTableModel>
            columns={RevenueTotalTableColumns as ColumnDef<ComparisonRevenueTableModel, unknown>[]}
            data={data}
            activeView={activeButton}
            onChangeView={setActiveButton}
        />
    );
};
