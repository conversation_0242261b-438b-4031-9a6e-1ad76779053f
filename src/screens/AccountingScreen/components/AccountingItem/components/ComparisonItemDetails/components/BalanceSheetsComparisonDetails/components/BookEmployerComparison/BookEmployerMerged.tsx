import React, {useEffect, useState} from 'react';
import {KasComparisonTable} from '@/components';
import {ColumnDef} from '@tanstack/react-table';
import {ComparisonAccountingTableModel} from '@/interfaces';
import {BookEmployerMergedTableColumns} from './tables';
import {Button, ButtonGroup} from '@mui/material';
import {useComparisonDetails} from '@/screens/AccountingScreen/components/AccountingItem/components';
import {ComparisonDetailsProps} from '@/screens/AccountingScreen/components/AccountingItem/components/ComparisonItemDetails/interface';
import {uniqElements} from '@/utils/ArrayUtils';

export const BookEmployerMerged = ({itemData}: ComparisonDetailsProps<ComparisonAccountingTableModel>) => {
    const {activeButton, setActiveButton} = useComparisonDetails();
    const [activeDateRangeButton, setActiveDateRangeButton] = useState('');
    const uniqueComparisonDates = uniqElements(itemData, (item) => item.comparisonDate);
    const data: ComparisonAccountingTableModel[] = itemData.filter(
        ({comparisonDate}) => comparisonDate === activeDateRangeButton,
    );

    const handleButtonClick = (value: string) => {
        setActiveDateRangeButton(value);
    };

    useEffect(() => {
        setActiveDateRangeButton(uniqueComparisonDates[0]);
    }, []);

    return (
        <KasComparisonTable<ComparisonAccountingTableModel>
            columns={BookEmployerMergedTableColumns as ColumnDef<ComparisonAccountingTableModel, unknown>[]}
            data={data}
            activeView={activeButton}
            onChangeView={setActiveButton}>
            <ButtonGroup>
                {uniqueComparisonDates.map((item, index) => (
                    <Button
                        key={item}
                        variant={activeDateRangeButton === item ? 'contained' : 'text'}
                        onClick={() => handleButtonClick(item)}>
                        Date range {index + 1}
                    </Button>
                ))}
            </ButtonGroup>
        </KasComparisonTable>
    );
};
