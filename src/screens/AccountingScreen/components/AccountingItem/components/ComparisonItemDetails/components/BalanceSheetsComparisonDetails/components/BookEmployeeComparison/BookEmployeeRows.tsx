import React, {useMemo, useState} from 'react';
import {KasComparisonTable} from '@/components';
import {ColumnDef} from '@tanstack/react-table';
import {ComparisonAccountingTableModel, RowsComparisonAccountingTableModel} from '@/interfaces';
import {Checkbox, FormControlLabel} from '@mui/material';
import {useComparisonDetails} from '@/screens/AccountingScreen/components/AccountingItem/components';
import {BookEmployeeRowsTableColumns} from './tables';
import {ComparisonDetailsProps} from '@/screens/AccountingScreen/components/AccountingItem/components/ComparisonItemDetails/interface';
import {uniqElements} from '@/utils/ArrayUtils';

export const BookEmployeeRows = ({itemData}: ComparisonDetailsProps<ComparisonAccountingTableModel>) => {
    const {activeButton, setActiveButton} = useComparisonDetails();
    const [showRowDiff, setShowRowDiff] = useState(false);

    const data: RowsComparisonAccountingTableModel[] = useMemo(
        () =>
            itemData.sort((a, b) => {
                if (a.step === b.step) {
                    if (a.comparisonDate === b.comparisonDate) {
                        return 0;
                    }
                    return a.comparisonDate < b.comparisonDate ? -1 : 1;
                }
                return Number(a.step) - Number(b.step);
            }),
        [itemData],
    );

    const extendedData: RowsComparisonAccountingTableModel[] = useMemo(() => {
        const uniqueSteps = uniqElements(data, (item) => item.step);
        const resultData: RowsComparisonAccountingTableModel[] = [];

        uniqueSteps.forEach((step) => {
            const itemData = data.filter((item) => item.step === step);

            if (itemData.length === 2) {
                resultData.push(itemData[0]);
                resultData.push(itemData[1]);
                resultData.push({
                    ...itemData[0],
                    diff: true,
                    values: {
                        KAS: itemData[1].values.KAS - itemData[0].values.KAS,
                        CRB: itemData[1].values.CRB - itemData[0].values.CRB,
                        CRBA: itemData[1].values.CRBA - itemData[0].values.CRBA,
                        CRBX: itemData[1].values.CRBX - itemData[0].values.CRBX,
                        CRBY: itemData[1].values.CRBY - itemData[0].values.CRBY,
                        BRB: itemData[1].values.BRB - itemData[0].values.BRB,
                        BRBA: itemData[1].values.BRBA - itemData[0].values.BRBA,
                        BRBX: itemData[1].values.BRBX - itemData[0].values.BRBX,
                        BRBY: itemData[1].values.BRBY - itemData[0].values.BRBY,
                        MDB: itemData[1].values.MDB - itemData[0].values.MDB,
                        MDBA: itemData[1].values.MDBA - itemData[0].values.MDBA,
                        MDBX: itemData[1].values.MDBX - itemData[0].values.MDBX,
                    },
                });
            }
        });

        return resultData;
    }, [data]);

    return (
        <KasComparisonTable<RowsComparisonAccountingTableModel>
            columns={BookEmployeeRowsTableColumns as ColumnDef<RowsComparisonAccountingTableModel, unknown>[]}
            data={showRowDiff ? extendedData : data}
            activeView={activeButton}
            onChangeView={setActiveButton}>
            <FormControlLabel
                label='Show difference'
                control={
                    <Checkbox
                        size='small'
                        checked={showRowDiff}
                        onChange={() => setShowRowDiff(!showRowDiff)}
                        inputProps={{'aria-label': 'controlled'}}
                    />
                }
            />
        </KasComparisonTable>
    );
};
