import React, {useMemo} from 'react';
import {AccountingExternalSystemExportType, AccountingItemModel} from '@/interfaces';

interface ItemFormProps {
    item: AccountingItemModel;
}

export const ExternalSystemExportComparisonDetails = ({item}: ItemFormProps) => {
    const renderForm = useMemo(() => {
        switch (item.filterType) {
            case AccountingExternalSystemExportType.Xero:
                return null;
            case AccountingExternalSystemExportType.Xpay:
                return null;
            case AccountingExternalSystemExportType.Sage:
                return null;
            default:
                return null;
        }
    }, [item]);

    return <>{renderForm}</>;
};
