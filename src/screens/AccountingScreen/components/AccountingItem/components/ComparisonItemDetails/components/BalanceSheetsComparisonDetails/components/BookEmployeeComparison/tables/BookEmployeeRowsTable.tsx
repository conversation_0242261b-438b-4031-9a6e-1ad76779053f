import {AccessorFn, CellContext, createColumnHelper, DeepKeys} from '@tanstack/react-table';
import {RowsComparisonAccountingTableModel} from '@/interfaces';
import {AmountCell} from '@/components/table/cells';
import {
    accountingSubtotalKas,
    accountingSubtotalKs1,
    accountingSubtotalX,
    accountingSubtotalY,
    accountingTotal,
    createColumn,
} from '@/utils/TableUtils';

const columnHelper = createColumnHelper<RowsComparisonAccountingTableModel>();

const renderCell = (cell: CellContext<RowsComparisonAccountingTableModel, number | string | null>) => {
    return <AmountCell isMulticolor={!!cell.row.original.diff} data={Number(cell.getValue() || 0)} />;
};

const infoColumn = (
    accessor: AccessorFn<RowsComparisonAccountingTableModel> | DeepKeys<RowsComparisonAccountingTableModel>,
    header: string,
) => {
    return createColumn<RowsComparisonAccountingTableModel, string>(
        accessor,
        accessor as string,
        header,
        undefined,
        undefined,
        false,
    );
};

const amountColumn = (
    accessor: AccessorFn<RowsComparisonAccountingTableModel> | DeepKeys<RowsComparisonAccountingTableModel>,
    header: string,
) => {
    return createColumn<RowsComparisonAccountingTableModel, string>(
        accessor,
        accessor as string,
        header,
        renderCell,
        undefined,
        false,
    );
};

const accessorFnColumn = (
    key: keyof RowsComparisonAccountingTableModel | string,
    header: string,
    accessorFn: AccessorFn<RowsComparisonAccountingTableModel, number>,
) => ({
    id: key,
    header,
    accessorKey: key,
    accessorFn,
    cell: renderCell,
    enableSorting: false,
});

export const BookEmployeeRowsTableColumns = [
    infoColumn('step', 'Step'),
    infoColumn('title', 'Description'),
    amountColumn('values.KAS', 'KAS Originated'),
    columnHelper.group({
        id: 'CRB-originated',
        header: 'CRB Originated',
        columns: [
            amountColumn('values.CRB', 'Purchased'),
            amountColumn('values.CRBA', 'Purchased (SPV)'),
            amountColumn('values.CRBX', 'Owned (Non-Retained)'),
            amountColumn('values.CRBY', 'Owned (Retained)'),
        ],
    }),
    columnHelper.group({
        id: 'BRB-originated',
        header: 'BRB Originated',
        columns: [
            amountColumn('values.BRB', 'Purchased'),
            amountColumn('values.BRBA', 'Purchased (SPV)'),
            amountColumn('values.BRBX', 'Owned (Non-Retained)'),
            amountColumn('values.BRBY', 'Owned (Retained)'),
        ],
    }),
    columnHelper.group({
        id: 'MDB-originated',
        header: 'MDB Originated',
        columns: [
            amountColumn('values.MDB', 'Purchased'),
            amountColumn('values.MDBA', 'Purchased (KS1)'),
            amountColumn('values.MDBX', 'Owned (Non-Retained)'),
        ],
    }),
    columnHelper.group({
        id: 'subtotals',
        header: 'Subtotals',
        columns: [
            accessorFnColumn('subtotals-1', 'KAS Owned', accountingSubtotalKas),
            accessorFnColumn('subtotals-2', 'KS1 Owned', accountingSubtotalKs1),
            accessorFnColumn('subtotals-3', 'Partner Owned (Non-Retained)', accountingSubtotalX),
            accessorFnColumn('subtotals-4', 'Partner Owned (Retained)', accountingSubtotalY),
        ],
    }),
    accessorFnColumn('total', 'Total', accountingTotal),
];
