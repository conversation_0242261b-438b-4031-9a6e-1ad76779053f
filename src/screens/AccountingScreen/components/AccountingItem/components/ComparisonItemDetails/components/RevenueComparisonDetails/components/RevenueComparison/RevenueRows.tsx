import React, {useMemo, useState} from 'react';
import {KasComparisonTable} from '@/components';
import {ColumnDef} from '@tanstack/react-table';
import {ComparisonRevenueTableModel, RowsComparisonRevenueTableModel} from '@/interfaces';
import {RevenueRowsTableColumns} from './tables';
import {Checkbox, FormControlLabel} from '@mui/material';
import {useComparisonDetails} from '@/screens/AccountingScreen/components/AccountingItem/components';
import {ComparisonDetailsProps} from '@/screens/AccountingScreen/components/AccountingItem/components/ComparisonItemDetails/interface';
import {uniqElements} from '@/utils/ArrayUtils';

export const RevenueRows = ({itemData}: ComparisonDetailsProps<ComparisonRevenueTableModel>) => {
    const {activeButton, setActiveButton} = useComparisonDetails();
    const [showRowDiff, setShowRowDiff] = useState(false);

    const data: RowsComparisonRevenueTableModel[] = useMemo(
        () =>
            itemData.sort((a, b) => {
                if (a.entity_id === b.entity_id) {
                    if (a.comparisonDate === b.comparisonDate) {
                        return 0;
                    }
                    return a.comparisonDate < b.comparisonDate ? -1 : 1;
                }
                return Number(a.entity_id) - Number(b.entity_id);
            }),
        [itemData],
    );

    const extendedData: RowsComparisonRevenueTableModel[] = useMemo(() => {
        const uniqueEntityIDs = uniqElements(data, (item) => item.entity_id);
        const resultData: RowsComparisonRevenueTableModel[] = [];

        uniqueEntityIDs.forEach((entityID) => {
            const itemData = data.filter((item) => item.entity_id === entityID);

            if (itemData.length === 2) {
                resultData.push(itemData[0]);
                resultData.push(itemData[1]);
                resultData.push({
                    ...itemData[0],
                    diff: true,
                    unrealized_interest: itemData[1].unrealized_interest - itemData[0].unrealized_interest,
                    unrealized_origination_fee:
                        itemData[1].unrealized_origination_fee - itemData[0].unrealized_origination_fee,
                    unrealized_processing_fee:
                        itemData[1].unrealized_processing_fee - itemData[0].unrealized_processing_fee,
                    unrealized_other_fee: itemData[1].unrealized_other_fee - itemData[0].unrealized_other_fee,
                    realized_interest: itemData[1].realized_interest - itemData[0].realized_interest,
                    realized_origination_fee:
                        itemData[1].realized_origination_fee - itemData[0].realized_origination_fee,
                    realized_processing_fee:
                        itemData[1].realized_processing_fee - itemData[0].realized_processing_fee,
                    realized_other_fee: itemData[1].realized_other_fee - itemData[0].realized_other_fee,
                    account_credit: itemData[1].account_credit - itemData[0].account_credit,
                    other_income: itemData[1].other_income - itemData[0].other_income,
                    pepm_billed: itemData[1].pepm_billed - itemData[0].pepm_billed,
                    pepm_received: itemData[1].pepm_received - itemData[0].pepm_received,
                    reconciles: {
                        EFFECTIVE: itemData[1].reconciles.EFFECTIVE - itemData[0].reconciles.EFFECTIVE,
                    },
                });
            }
        });

        return resultData;
    }, [data]);

    return (
        <KasComparisonTable<RowsComparisonRevenueTableModel>
            columns={RevenueRowsTableColumns as ColumnDef<RowsComparisonRevenueTableModel, unknown>[]}
            data={showRowDiff ? extendedData : data}
            activeView={activeButton}
            onChangeView={setActiveButton}>
            <FormControlLabel
                label='Show difference'
                control={
                    <Checkbox
                        size='small'
                        checked={showRowDiff}
                        onChange={() => setShowRowDiff(!showRowDiff)}
                        inputProps={{'aria-label': 'controlled'}}
                    />
                }
            />
        </KasComparisonTable>
    );
};
