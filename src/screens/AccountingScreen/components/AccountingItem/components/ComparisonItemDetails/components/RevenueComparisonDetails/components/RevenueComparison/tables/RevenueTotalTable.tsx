import {createColumnHelper} from '@tanstack/react-table';
import {
    totalsAccessorFnColumn,
    totalsAmountColumn,
    defaultInfoColumn,
    revenueRealizedTotal,
    revenueTotal,
    revenueUnrealizedTotal,
} from '@/utils/TableUtils';
import {ComparisonRevenueTableModel} from '@/interfaces';

const columnHelper = createColumnHelper<ComparisonRevenueTableModel>();

const _defaultInfoColumn = defaultInfoColumn<ComparisonRevenueTableModel>;
const _totalsAmountColumn = totalsAmountColumn<ComparisonRevenueTableModel>;
const _totalsAccessorFnColumn = totalsAccessorFnColumn<ComparisonRevenueTableModel>;

export const RevenueTotalTableColumns = [
    _defaultInfoColumn('comparisonDate', 'Date', 'Difference:'),
    columnHelper.group({
        id: 'unrealized-tables',
        header: 'Unrealized Revenue',
        columns: [
            _totalsAmountColumn('unrealized_interest', 'Interest'),
            _totalsAmountColumn('unrealized_origination_fee', 'Orig Fee'),
            _totalsAmountColumn('unrealized_processing_fee', 'Proc Fee'),
            _totalsAmountColumn('unrealized_other_fee', 'Other Fee'),
            _totalsAmountColumn('unrealized_pepm', 'PEPM'),
            _totalsAccessorFnColumn('unrealized-subtotal', 'Subtotal', revenueUnrealizedTotal),
        ],
    }),
    columnHelper.group({
        id: 'realized-tables',
        header: 'Realized Revenue',
        columns: [
            _totalsAmountColumn('realized_interest', 'Interest'),
            _totalsAmountColumn('realized_origination_fee', 'Orig Fee'),
            _totalsAmountColumn('realized_processing_fee', 'Proc Fee'),
            _totalsAmountColumn('realized_other_fee', 'Other Fee'),
            _totalsAmountColumn('pepm_received', 'PEPM'),
            _totalsAccessorFnColumn('realized-subtotal', 'Subtotal', revenueRealizedTotal),
        ],
    }),
    columnHelper.group({
        id: 'other-tables',
        header: 'Other Revenue',
        columns: [
            _totalsAmountColumn('account_credit', 'Credit'),
            _totalsAmountColumn('other_income', 'Other Income'),
        ],
    }),
    _totalsAmountColumn('reconciles.EFFECTIVE', 'Deferrer'),
    _totalsAccessorFnColumn('total', 'Total', revenueTotal),
];
