import {
    totalsAmountColumn,
    defaultInfoColumn,
    totalsAccessorFnColumn,
    defaultAccessorFnColumn,
    accountingSubtotalKas,
    accountingSubtotalKs1,
    accountingSubtotalX,
    accountingSubtotalY,
    accountingTotal,
} from '@/utils/TableUtils';
import {ComparisonAccountingTableModel} from '@/interfaces';
import {createColumnHelper} from '@tanstack/react-table';

const columnHelper = createColumnHelper<ComparisonAccountingTableModel>();

const _defaultInfoColumn = defaultInfoColumn<ComparisonAccountingTableModel>;
const _totalsAmountColumn = totalsAmountColumn<ComparisonAccountingTableModel>;
const _defaultAccessorFnColumn = defaultAccessorFnColumn<ComparisonAccountingTableModel>;
const _totalsAccessorFnColumn = totalsAccessorFnColumn<ComparisonAccountingTableModel>;

export const BookEmployerTotalTableColumns = [
    _defaultInfoColumn('comparisonDate', 'Date', 'Difference:'),
    _totalsAmountColumn('values.KAS', 'KAS Originated'),
    columnHelper.group({
        id: 'CRB-originated',
        header: 'CRB Originated',
        columns: [
            _totalsAmountColumn('values.CRB', 'Purchased'),
            _totalsAmountColumn('values.CRBA', 'Purchased (SPV)'),
            _totalsAmountColumn('values.CRBX', 'Owned (Non-Retained)'),
            _totalsAmountColumn('values.CRBY', 'Owned (Retained)'),
        ],
    }),
    columnHelper.group({
        id: 'BRB-originated',
        header: 'BRB Originated',
        columns: [
            _totalsAmountColumn('values.BRB', 'Purchased'),
            _totalsAmountColumn('values.BRBA', 'Purchased (SPV)'),
            _totalsAmountColumn('values.BRBX', 'Owned (Non-Retained)'),
            _totalsAmountColumn('values.BRBY', 'Owned (Retained)'),
        ],
    }),
    columnHelper.group({
        id: 'MDB-originated',
        header: 'MDB Originated',
        columns: [
            _totalsAmountColumn('values.MDB', 'Purchased'),
            _totalsAmountColumn('values.MDBA', 'Purchased (SPV)'),
            _totalsAmountColumn('values.MDBX', 'Owned (Non-Retained)'),
        ],
    }),
    columnHelper.group({
        id: 'subtotals',
        header: 'Subtotals',
        columns: [
            _defaultAccessorFnColumn('subtotals-1', 'KAS Owned', accountingSubtotalKas),
            _defaultAccessorFnColumn('subtotals-2', 'KS1 Owned', accountingSubtotalKs1),
            _defaultAccessorFnColumn('subtotals-3', 'Partner Owned (Non-Retained)', accountingSubtotalX),
            _defaultAccessorFnColumn('subtotals-4', 'Partner Owned (Retained)', accountingSubtotalY),
        ],
    }),
    _totalsAccessorFnColumn('total', 'Total', accountingTotal),
];
