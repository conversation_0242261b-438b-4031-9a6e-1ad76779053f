import {CellContext, createColumnHelper} from '@tanstack/react-table';
import {ComparisonAccountingTableModel} from '@/interfaces';
import {
    accountingSubtotalKas,
    accountingSubtotalKs1,
    accountingSubtotalX,
    accountingSubtotalY,
    accountingTotal,
    defaultAccessorFnColumn,
    defaultAmountColumn,
    defaultInfoColumn,
} from '@/utils/TableUtils';
import dayjs from 'dayjs';
import {DownloadCell} from '@/components/table/cells';

const columnHelper = createColumnHelper<ComparisonAccountingTableModel>();

const _defaultInfoColumn = defaultInfoColumn<ComparisonAccountingTableModel>;
const _defaultAmountColumn = defaultAmountColumn<ComparisonAccountingTableModel>;
const _defaultAccessorFnColumn = defaultAccessorFnColumn<ComparisonAccountingTableModel>;

export const BookEmployeeMergedTableColumns = [
    _defaultInfoColumn('step', 'Step'),
    _defaultInfoColumn('title', 'Description'),
    _defaultAmountColumn('values.KAS', 'KAS Originated'),
    columnHelper.group({
        id: 'CRB-originated',
        header: 'CRB Originated',
        columns: [
            _defaultAmountColumn('values.CRB', 'Purchased'),
            _defaultAmountColumn('values.CRBA', 'Purchased (KS1)'),
            _defaultAmountColumn('values.CRBX', 'Owned (Non-Retained)'),
            _defaultAmountColumn('values.CRBY', 'Owned (Retained)'),
        ],
    }),
    columnHelper.group({
        id: 'BRB-originated',
        header: 'BRB Originated',
        columns: [
            _defaultAmountColumn('values.BRB', 'Purchased'),
            _defaultAmountColumn('values.BRBA', 'Purchased (KS1)'),
            _defaultAmountColumn('values.BRBX', 'Owned (Non-Retained)'),
            _defaultAmountColumn('values.BRBY', 'Owned (Retained)'),
        ],
    }),
    columnHelper.group({
        id: 'MDB-originated',
        header: 'MDB Originated',
        columns: [
            _defaultAmountColumn('values.MDB', 'Purchased'),
            _defaultAmountColumn('values.MDBA', 'Purchased (KS1)'),
            _defaultAmountColumn('values.MDBX', 'Owned (Non-Retained)'),
        ],
    }),
    columnHelper.group({
        id: 'subtotals',
        header: 'Subtotals',
        columns: [
            _defaultAccessorFnColumn('subtotals-1', 'KAS Owned', accountingSubtotalKas),
            _defaultAccessorFnColumn('subtotals-2', 'KS1 Owned', accountingSubtotalKs1),
            _defaultAccessorFnColumn('subtotals-3', 'Partner Owned (Non-Retained)', accountingSubtotalX),
            _defaultAccessorFnColumn('subtotals-4', 'Partner Owned (Retained)', accountingSubtotalY),
        ],
    }),
    _defaultAccessorFnColumn('total', 'Total', accountingTotal),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<ComparisonAccountingTableModel, string>) => {
            const {date, step} = props.row.original;
            const bucket = dayjs(date).format('YYYYMMDD');
            const params = {
                path: `/secured/accounting/book/empl/${bucket}/${step - 1}`,
            };

            return <DownloadCell params={JSON.stringify(params)} />;
        },
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
];
