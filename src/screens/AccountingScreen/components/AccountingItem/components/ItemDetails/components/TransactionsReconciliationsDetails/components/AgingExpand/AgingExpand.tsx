import './styles.scss';

import React, {Fragment, useEffect, useState} from 'react';
import {Typography, useTheme, Grid2} from '@mui/material';
import {toCurrency} from '@/utils/FormatUtils';
import {AgingTableDetailsModel, AgingTableModel} from '@/interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {KasLoading, KasLoadingError, KasNoResults, KasSwitch, KasSwitchWhen} from '@/components';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';

interface AgingExpandProps {
    data: AgingTableModel;
}

export const AgingExpand = ({data}: AgingExpandProps) => {
    const {palette} = useTheme();
    const [state, setState] = useState(getDefaultState<AgingTableDetailsModel[]>());

    const loadDetails = async () => {
        const url = `/api/secured/accounting/transactions-reconciliations/aging/${data.bucket}?date=${data.date}&vintage=${data.vintage}`;

        setState(getLoadingState(state));
        const response = await apiRequest(url);
        setState(getLoadedState(response));
    };

    useEffect(() => {
        loadDetails().then();
    }, []);

    return (
        <div className='kas-accounting-aging-expand'>
            <KasSwitch>
                <KasSwitchWhen condition={state.loading}>
                    <KasLoading />
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!state.error}>
                    <KasLoadingError error={state.error} />
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!state.data && !state.data.length}>
                    <KasNoResults text='No records found' />
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!state.data}>
                    {state.data?.length && (
                        <Grid2 container>
                            <Grid2 size={2} mb={1}>
                                <Typography variant='subtitle1' color={palette.action.disabled}>
                                    Loan ID
                                </Typography>
                            </Grid2>
                            <Grid2 size={2} mb={1}>
                                <Typography variant='subtitle1' color={palette.action.disabled}>
                                    Balance
                                </Typography>
                            </Grid2>
                            <Grid2 size={8} />
                            {state.data.map((component) => (
                                <Fragment key={component.bucket}>
                                    <Grid2 size={2}>
                                        <Typography variant='body1'>{component.loan_id}</Typography>
                                    </Grid2>
                                    <Grid2 size={2}>
                                        <Typography variant='body1'>
                                            {toCurrency(component.balance)}
                                        </Typography>
                                    </Grid2>
                                    <Grid2 size={8} />
                                </Fragment>
                            ))}
                        </Grid2>
                    )}
                </KasSwitchWhen>
            </KasSwitch>
        </div>
    );
};
