import {createColumnHelper} from '@tanstack/react-table';
import {
    defaultAccessorFnColumn,
    defaultInfoColumn,
    defaultAmountColumn,
    revenuePartnershipUnrealizedAdjustments,
    revenuePartnershipRealizedAdjustments,
    revenuePartnershipUnrealizedFees,
    revenuePartnershipRealizedFees,
    revenuePartnershipUnrealizedPEPM,
    revenuePartnershipRealizedPEPM,
    revenuePartnershipUnrealizedTotal,
    revenuePartnershipRealizedTotal,
    revenuePartnershipBankTotal,
    revenuePartnershipTotal,
} from '@/utils/TableUtils';
import {RevenuePartnershipTableModel} from '@/interfaces';

const columnHelper = createColumnHelper<RevenuePartnershipTableModel>();

const _defaultInfoColumn = defaultInfoColumn<RevenuePartnershipTableModel>;
const _defaultAmountColumn = defaultAmountColumn<RevenuePartnershipTableModel>;
const _defaultAccessorFnColumn = defaultAccessorFnColumn<RevenuePartnershipTableModel>;

export const RevenuePartnershipTableColumns = [
    _defaultInfoColumn('entity_id', 'ID', 'Total:'),
    _defaultInfoColumn('name', 'Name'),
    columnHelper.group({
        id: 'consumer-paid',
        header: 'Consumer Paid',
        columns: [
            columnHelper.group({
                id: 'unrealized-tables',
                header: 'Unrealized Revenue',
                columns: [
                    _defaultAmountColumn('unrealized_interest', 'Interest'),
                    _defaultAccessorFnColumn(
                        'unrealized-adjustments',
                        'Interest reclass',
                        revenuePartnershipUnrealizedAdjustments,
                    ),
                    _defaultAccessorFnColumn('unrealized-fees', 'Fees', revenuePartnershipUnrealizedFees),
                    _defaultAmountColumn('unrealized_chargeoff_adjustment', 'Adj for Chargeoffs'),
                    _defaultAccessorFnColumn('unrealized-pepm', 'PEPM', revenuePartnershipUnrealizedPEPM),
                    _defaultAccessorFnColumn(
                        'unrealized-total',
                        'Subtotal',
                        revenuePartnershipUnrealizedTotal,
                    ),
                ],
            }),
            columnHelper.group({
                id: 'realized-tables',
                header: 'Realized Revenue',
                columns: [
                    _defaultAmountColumn('realized_interest', 'Interest'),
                    _defaultAccessorFnColumn(
                        'realized-adjustments',
                        'Interest reclass',
                        revenuePartnershipRealizedAdjustments,
                    ),
                    _defaultAccessorFnColumn('realized-fees', 'Fees', revenuePartnershipRealizedFees),
                    _defaultAccessorFnColumn('realized-pepm', 'PEPM', revenuePartnershipRealizedPEPM),
                    _defaultAccessorFnColumn('realized-total', 'Subtotal', revenuePartnershipRealizedTotal),
                ],
            }),
            columnHelper.group({
                id: 'other-tables',
                header: 'Other Revenue',
                columns: [
                    _defaultAmountColumn('account_credit', 'Credit'),
                    _defaultAmountColumn('other_income', 'Other Income'),
                ],
            }),
        ],
    }),
    columnHelper.group({
        id: 'bank-partner-paid',
        header: 'Bank Partner Paid',
        columns: [
            columnHelper.group({
                id: 'bank-partner-paid',
                header: 'Partnership',
                columns: [
                    _defaultAccessorFnColumn('bank-partnership-fees', 'Fees', revenuePartnershipBankTotal),
                ],
                enableSorting: false,
            }),
        ],
        enableSorting: false,
    }),
    _defaultAmountColumn('reconciles.EFFECTIVE', 'Deferrer'),
    _defaultAccessorFnColumn('total', 'Total', revenuePartnershipTotal),
];
