import {CellContext, createColumnHelper} from '@tanstack/react-table';
import {AgingTableModel} from '@/interfaces';
import {AmountCell, DownloadCell} from '@/components/table/cells';
import {expandCell, totalSum} from '@/utils/TableUtils';
import {toCurrency} from '@/utils/FormatUtils';

const columnHelper = createColumnHelper<AgingTableModel>();

export const AgingTableDelinquencyColumns = [
    {
        id: 'expander',
        header: '',
        cell: (props: CellContext<AgingTableModel, string>) => expandCell(props),
        enableSorting: false,
        footer: 'Total:',
        meta: {
            notExport: true,
        },
    },
    columnHelper.accessor('bucket', {
        id: 'bucket',
        header: 'Bucket',
        cell: (info) => (
            <abbr title={`${info.getValue() - 89} - ${info.getValue()} Days`}>{info.getValue()}</abbr>
        ),
    }),
    columnHelper.accessor('count', {
        id: 'count',
        header: 'Count',
        footer: (props) => totalSum({...props, columnId: 'count'}),
    }),
    columnHelper.accessor('balance', {
        id: 'balance',
        header: 'Balance',
        cell: (info) => {
            const {balance_nopay, balance} = info.row.original;

            return balance_nopay !== null ? (
                <>
                    {toCurrency(balance)} /{' '}
                    <abbr title='Recency Check Balance'>{toCurrency(balance_nopay)}</abbr>
                </>
            ) : (
                <AmountCell data={balance} />
            );
        },
        footer: (props) => <AmountCell data={totalSum({...props, columnId: 'balance'})} />,
    }),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<AgingTableModel, string>) => {
            const {date, bucket, vintage} = props.row.original;
            const params = {
                path: `/secured/accounting/vintage/${vintage}/${date}/${bucket}/export`,
            };

            return <DownloadCell params={JSON.stringify(params)} />;
        },
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
];
