import {createColumnHelper} from '@tanstack/react-table';
import {
    accountingTotal,
    defaultAccessorFnColumn,
    defaultAmountColumn,
    defaultInfoColumn,
} from '@/utils/TableUtils';
import {AccountingTableModel} from '@/interfaces';

const columnHelper = createColumnHelper<AccountingTableModel>();

const _defaultInfoColumn = defaultInfoColumn<AccountingTableModel>;
const _defaultAmountColumn = defaultAmountColumn<AccountingTableModel>;
const _defaultAccessorFnColumn = defaultAccessorFnColumn<AccountingTableModel>;

export const DebitCardTableColumns = [
    _defaultInfoColumn('step', 'Step'),
    _defaultInfoColumn('title', 'Description'),
    _defaultAmountColumn('values.KAS', 'KAS Originated'),
    columnHelper.group({
        id: 'CRB-originated',
        header: 'CRB Originated',
        columns: [
            _defaultAmountColumn('values.CRB', 'Purchased'),
            _defaultAmountColumn('values.CRBX', 'Owned (Non-Retained)'),
            _defaultAmountColumn('values.CRBY', 'Owned (Retained)'),
        ],
    }),
    columnHelper.group({
        id: 'BRB-originated',
        header: 'BRB Originated',
        columns: [
            _defaultAmountColumn('values.BRB', 'Purchased'),
            _defaultAmountColumn('values.BRBX', 'Owned (Non-Retained)'),
            _defaultAmountColumn('values.BRBY', 'Owned (Retained)'),
        ],
    }),
    columnHelper.group({
        id: 'MDB-originated',
        header: 'MDB Originated',
        columns: [
            _defaultAmountColumn('values.MDB', 'Purchased'),
            _defaultAmountColumn('values.MDBX', 'Owned (Non-Retained)'),
        ],
    }),
    _defaultAccessorFnColumn('total', 'Total', accountingTotal),
];
