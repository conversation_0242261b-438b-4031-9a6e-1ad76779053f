import {CellContext, createColumnHelper} from '@tanstack/react-table';
import {expandCell, generateAmountCell} from '@/utils/TableUtils';
import {BrokerCommissionSummaryTableModel} from '@/interfaces';
import {CommissionEmailActionButton} from '@/screens/AccountingScreen/components/AccountingItem/components/ItemDetails/components/CommissionEmailActionButton/CommissionEmailActionButton';

const columnHelper = createColumnHelper<BrokerCommissionSummaryTableModel>();

export const BrokerCommissionSummaryTableColumns = [
    {
        id: 'expander',
        header: '',
        cell: (props: CellContext<BrokerCommissionSummaryTableModel, string>) => expandCell(props),
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
    columnHelper.accessor('broker_name', {
        id: 'broker_name',
        header: 'Broker',
    }),
    columnHelper.accessor('producer_name', {
        id: 'producer_name',
        header: 'Producer',
    }),
    columnHelper.accessor('amount', {
        id: 'amount',
        header: 'Amount',
        cell: generateAmountCell<BrokerCommissionSummaryTableModel, number>(false),
    }),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<BrokerCommissionSummaryTableModel, number>) => {
            return <CommissionEmailActionButton data={props.row.original} />;
        },
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
];
