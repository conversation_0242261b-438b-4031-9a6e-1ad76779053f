import React, {useMemo} from 'react';
import {KasDefaultTable} from '@/components';
import {CellContext, ColumnDef, Row} from '@tanstack/react-table';
import {
    AccountingItemModel,
    AccountingTableModel,
    AccountingTransactionsReconciliationsType,
    AgingTableModel,
    DisbursementsTableModel,
    EmployeeTransactionReconciliationTableModel,
    EmployerARBalancesTableModel,
    EmployerTransactionReconciliationTableModel,
    LOSCollectionsTableModel,
    LOSPremiumsTableModel,
    OriginalCostsTableModel,
    OriginationListTableModel,
    OwnershipListTableModel,
    TransformedLOSCommonTableModel,
    TransformedLOSPurchasePartyTableModel,
} from '@/interfaces';
import {
    AgingTableDelinquencyColumns,
    AgingTableLoanColumns,
    DebitCardTableColumns,
    DisbursementsTableColumns,
    EmployeeTransactionReconciliationTableColumns,
    EmployerARBalancesTableColumns,
    EmployerTransactionReconciliationTableColumns,
    LOSAdjustmentsTableColumns,
    LOSCollectionsTableColumns,
    LOSFeesTableColumns,
    LOSPremiumsTableColumns,
    OriginalCostsTableColumns,
    OriginationListTableColumns,
    OwnershipListTableColumns,
    AccountingCollectionsTableColumns,
} from './tables';
import {AgingExpand, EmployeeTransactionReconciliationExpand} from './components';
import {AccountingTableView} from '@/views';
import {DownloadCell} from '@/components/table/cells';

interface ItemDetailsProps {
    item: AccountingItemModel;
}

export const TransactionsReconciliationsDetails = ({item}: ItemDetailsProps) => {
    const generateActionColumn = (pathPrefix: string): ColumnDef<AccountingTableModel, string> => ({
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<AccountingTableModel, string>) => {
            const {dateFrom, dateTo, step} = props.row.original;
            const params = {
                path: `${pathPrefix}/${step - 1}`,
                params: {start_date: dateFrom, end_date: dateTo},
            };
            return <DownloadCell params={JSON.stringify(params)} />;
        },
        enableSorting: false,
        meta: {
            notExport: true,
        },
    });

    const renderDetails = useMemo(() => {
        switch (item.filterType) {
            case AccountingTransactionsReconciliationsType.Employee:
                return (
                    <AccountingTableView
                        data={item.data}
                        actionColumn={generateActionColumn('/secured/accounting/reconcile/cash/employee')}
                    />
                );
            case AccountingTransactionsReconciliationsType.Employer:
                return (
                    <AccountingTableView
                        data={item.data}
                        actionColumn={generateActionColumn('/secured/accounting/reconcile/cash/employer')}
                    />
                );
            case AccountingTransactionsReconciliationsType.Employer_Transaction_Reconciliation:
                return (
                    <KasDefaultTable<EmployerTransactionReconciliationTableModel>
                        columns={
                            EmployerTransactionReconciliationTableColumns as ColumnDef<
                                EmployerTransactionReconciliationTableModel,
                                unknown
                            >[]
                        }
                        data={item.data as EmployerTransactionReconciliationTableModel[]}
                        tableName='Employer Transaction Reconciliation'
                    />
                );
            case AccountingTransactionsReconciliationsType.Employee_Transaction_Reconciliation:
                return (
                    <KasDefaultTable<EmployeeTransactionReconciliationTableModel>
                        columns={
                            EmployeeTransactionReconciliationTableColumns as ColumnDef<
                                EmployeeTransactionReconciliationTableModel,
                                unknown
                            >[]
                        }
                        data={item.data as EmployeeTransactionReconciliationTableModel[]}
                        renderExpand={(row: Row<EmployeeTransactionReconciliationTableModel>) => (
                            <EmployeeTransactionReconciliationExpand data={row.original} />
                        )}
                        tableName='Employee Transaction Reconciliation'
                    />
                );
            case AccountingTransactionsReconciliationsType.Employer_AR_Balances:
                return (
                    <KasDefaultTable<EmployerARBalancesTableModel>
                        columns={
                            EmployerARBalancesTableColumns as ColumnDef<
                                EmployerARBalancesTableModel,
                                unknown
                            >[]
                        }
                        data={item.data as EmployerARBalancesTableModel[]}
                        tableName='Employer AR Balances'
                    />
                );
            case AccountingTransactionsReconciliationsType.Disbursements:
                return (
                    <KasDefaultTable<DisbursementsTableModel>
                        columns={DisbursementsTableColumns as ColumnDef<DisbursementsTableModel, unknown>[]}
                        data={item.data as DisbursementsTableModel[]}
                        tableName='Disbursements'
                    />
                );
            case AccountingTransactionsReconciliationsType.Aging:
                if (item.data.length && item.data[0].vintage === 'delinquency') {
                    return (
                        <KasDefaultTable<AgingTableModel>
                            columns={AgingTableDelinquencyColumns as ColumnDef<AgingTableModel, unknown>[]}
                            data={item.data as AgingTableModel[]}
                            renderExpand={(row: Row<AgingTableModel>) => <AgingExpand data={row.original} />}
                            tableName='Aging'
                        />
                    );
                } else {
                    return (
                        <KasDefaultTable<AgingTableModel>
                            columns={AgingTableLoanColumns as ColumnDef<AgingTableModel, unknown>[]}
                            data={item.data as AgingTableModel[]}
                            tableName='Aging'
                        />
                    );
                }
            case AccountingTransactionsReconciliationsType.Origination_List:
                return (
                    <KasDefaultTable<OriginationListTableModel>
                        columns={
                            OriginationListTableColumns as ColumnDef<OriginationListTableModel, unknown>[]
                        }
                        data={item.data as OriginationListTableModel[]}
                        tableName='Origination List'
                    />
                );
            case AccountingTransactionsReconciliationsType.Origination_Costs:
                return (
                    <KasDefaultTable<OriginalCostsTableModel>
                        columns={OriginalCostsTableColumns as ColumnDef<OriginalCostsTableModel, unknown>[]}
                        data={item.data as OriginalCostsTableModel[]}
                        tableName='Origination Costs'
                    />
                );
            case AccountingTransactionsReconciliationsType.LOS_Fees:
                return (
                    <KasDefaultTable<TransformedLOSPurchasePartyTableModel>
                        columns={LOSFeesTableColumns as ColumnDef<TransformedLOSPurchasePartyTableModel, unknown>[]}
                        data={item.data as TransformedLOSPurchasePartyTableModel[]}
                        tableName='LOS Fees'
                    />
                );
            case AccountingTransactionsReconciliationsType.LOS_Adjustments:
                return (
                    <KasDefaultTable<TransformedLOSPurchasePartyTableModel>
                        columns={
                            LOSAdjustmentsTableColumns as ColumnDef<TransformedLOSPurchasePartyTableModel, unknown>[]
                        }
                        data={item.data as TransformedLOSPurchasePartyTableModel[]}
                        tableName='LOS Adjustments'
                    />
                );
            case AccountingTransactionsReconciliationsType.LOS_Collections:
                return (
                    <KasDefaultTable<LOSCollectionsTableModel>
                        columns={LOSCollectionsTableColumns as ColumnDef<LOSCollectionsTableModel, unknown>[]}
                        data={item.data as LOSCollectionsTableModel[]}
                        tableName='LOS Collections'
                    />
                );
            case AccountingTransactionsReconciliationsType.Total_Collections:
                return (
                    <KasDefaultTable<LOSCollectionsTableModel>
                        columns={
                            AccountingCollectionsTableColumns as ColumnDef<
                                LOSCollectionsTableModel,
                                unknown
                            >[]
                        }
                        data={item.data as LOSCollectionsTableModel[]}
                    />
                );
            case AccountingTransactionsReconciliationsType.LOS_Premiums:
                return (
                    <KasDefaultTable<LOSPremiumsTableModel>
                        columns={LOSPremiumsTableColumns as ColumnDef<LOSPremiumsTableModel, unknown>[]}
                        data={item.data as LOSPremiumsTableModel[]}
                        tableName='LOS Premiums'
                    />
                );
            case AccountingTransactionsReconciliationsType.Vendor_Costs:
                return null; // Download file item
            case AccountingTransactionsReconciliationsType.Debit_Card:
                return (
                    <KasDefaultTable<AccountingTableModel>
                        columns={DebitCardTableColumns as ColumnDef<AccountingTableModel, unknown>[]}
                        data={item.data as AccountingTableModel[]}
                        tableName='Debit Card'
                    />
                );
            case AccountingTransactionsReconciliationsType.Ownership_List:
                return (
                    <KasDefaultTable<OwnershipListTableModel>
                        columns={OwnershipListTableColumns as ColumnDef<OwnershipListTableModel, unknown>[]}
                        data={item.data as OwnershipListTableModel[]}
                        tableName='Ownership List'
                    />
                );
            default:
                return null;
        }
    }, [item]);

    return <>{renderDetails}</>;
};
