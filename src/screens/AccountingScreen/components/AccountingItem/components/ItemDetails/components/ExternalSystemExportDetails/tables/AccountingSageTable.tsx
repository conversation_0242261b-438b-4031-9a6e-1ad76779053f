import {createColumnHelper} from '@tanstack/react-table';
import {SageTableModel} from '@/interfaces';

const columnHelper = createColumnHelper<SageTableModel>();

export const SageTableColumns = [
    columnHelper.accessor('donotimport', {
        id: 'donotimport',
        header: 'DONOTIMPORT',
    }),
    columnHelper.accessor('journal', {
        id: 'journal',
        header: 'JOURNAL',
    }),
    columnHelper.accessor('date', {
        id: 'date',
        header: 'DATE',
    }),
    columnHelper.accessor('description', {
        id: 'description',
        header: 'DESCRIPTION',
    }),
    columnHelper.accessor('reference_no', {
        id: 'reference_no',
        header: 'REFERENCE_NO',
    }),
    columnHelper.accessor('line_no', {
        id: 'line_no',
        header: 'LINE_NO',
    }),
    columnHelper.accessor('acct_no', {
        id: 'acct_no',
        header: 'ACCT_NO',
    }),
    columnHelper.accessor('location_id', {
        id: 'location_id',
        header: 'LOCATION_ID',
    }),
    columnHelper.accessor('memo', {
        id: 'memo',
        header: 'MEMO',
    }),
    columnHelper.accessor('debit', {
        id: 'debit',
        header: 'DEBIT',
    }),
    columnHelper.accessor('glentry_customerid', {
        id: 'glentry_customerid',
        header: 'GLENTRY_CUSTOMERID',
    }),
    columnHelper.accessor('entry_date', {
        id: 'entry_date',
        header: 'DOCUMENT',
    }),
];
