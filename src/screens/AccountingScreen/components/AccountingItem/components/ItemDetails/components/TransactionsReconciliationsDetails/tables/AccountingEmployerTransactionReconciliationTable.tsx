import {createColumnHelper} from '@tanstack/react-table';
import {EmployerTransactionReconciliationTableModel} from '@/interfaces';
import {defaultAmountColumn, defaultInfoColumn, generateAmountCell} from '@/utils/TableUtils';

const columnHelper = createColumnHelper<EmployerTransactionReconciliationTableModel>();

const _defaultInfoColumn = defaultInfoColumn<EmployerTransactionReconciliationTableModel>;
const _defaultAmountColumn = defaultAmountColumn<EmployerTransactionReconciliationTableModel>;
const _defaultAmountCell = generateAmountCell<EmployerTransactionReconciliationTableModel, number>(false);

export const EmployerTransactionReconciliationTableColumns = [
    _defaultInfoColumn('vid', 'ID', 'Total:'),
    columnHelper.accessor('entity_class', {
        id: 'entity_class',
        header: 'Entity',
        cell: (props) => {
            const {entity_class, entity_id} = props.row.original;

            return `${entity_class} [${entity_id}]`;
        },
    }),
    _defaultInfoColumn('entity_name', 'Name'),
    _defaultInfoColumn('type', 'Description'),
    _defaultInfoColumn('effective_date', 'Effective Date'),
    _defaultInfoColumn('intended_date', 'Intended Date'),
    _defaultAmountColumn('amount', 'Amount'),
    columnHelper.accessor('balance', {
        id: 'balance',
        header: 'Balance',
        cell: _defaultAmountCell,
    }),
];
