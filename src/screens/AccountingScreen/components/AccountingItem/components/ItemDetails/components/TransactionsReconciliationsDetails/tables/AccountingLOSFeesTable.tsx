import {CellContext, createColumnHelper} from '@tanstack/react-table';
import {TransformedLOSPurchasePartyTableModel} from '@/interfaces';
import {AmountCell} from '@/components/table/cells';
import {KasDownloadLink} from '@/components';

const columnHelper = createColumnHelper<TransformedLOSPurchasePartyTableModel>();

const _renderCell = (los: string, purchaser: string) => {
    return (cell: CellContext<TransformedLOSPurchasePartyTableModel, number>) => {
        const {type, dateFrom, dateTo} = cell.row.original;
        const params = {
            path: '/secured/accounting/origination/fees/export',
            params: {
                start_date: dateFrom,
                end_date: dateTo,
                type,
                los,
                purchaser
            },
        };

        return (
            cell.getValue() > 0 ? (
                <KasDownloadLink params={JSON.stringify(params)}>
                    <AmountCell data={cell.getValue()} />
                </KasDownloadLink>
            ) : (
                <AmountCell data={cell.getValue()} />
            )
        );
    };
};

export const LOSFeesTableColumns = [
    columnHelper.accessor('title', {
        id: 'title',
        header: 'Description',
    }),
    columnHelper.group({
        id: 'crb_fee',
        header: 'CRB',
        columns: [
            columnHelper.accessor('values.CRB_KAS', {
                id: 'CRB_KAS',
                header: 'KAS',
                cell: _renderCell('CRB', 'KAS'),
            }),
            columnHelper.accessor('values.CRB_KS1', {
                id: 'CRB_KS1',
                header: 'KS1',
                cell: _renderCell('CRB', 'KS1'),
            })
        ],
    }),

    columnHelper.group({
        id: 'brb_fee',
        header: 'BRB',
        columns: [
            columnHelper.accessor('values.BRB_KAS', {
                id: 'BRB_KAS',
                header: 'KAS',
                cell: _renderCell('BRB', 'KAS'),
            }),
            columnHelper.accessor('values.BRB_KS1', {
                id: 'BRB_KS1',
                header: 'KS1',
                cell: _renderCell('BRB', 'KS1'),
            })
        ],
    }),

    columnHelper.group({
        id: 'mdb_fee',
        header: 'MDB',
        columns: [
            columnHelper.accessor('values.MDB_KAS', {
                id: 'MDB_KAS',
                header: 'KAS',
                cell: _renderCell('MDB', 'KAS'),
            }),
            columnHelper.accessor('values.MDB_KS1', {
                id: 'MDB_KS1',
                header: 'KS1',
                cell: _renderCell('MDB' ,'KS1'),
            }) 
        ],
    }),

];
