import {CellContext, createColumnHelper} from '@tanstack/react-table';
import {EmployeeTransactionReconciliationTableModel} from '@/interfaces';
import {defaultInfoColumn, expandCell, generateAmountCell, generateTotalAmountCell} from '@/utils/TableUtils';
import {DownloadCell} from '@/components/table/cells';

const columnHelper = createColumnHelper<EmployeeTransactionReconciliationTableModel>();

const _defaultInfoColumn = defaultInfoColumn<EmployeeTransactionReconciliationTableModel>;

export const EmployeeTransactionReconciliationTableColumns = [
    {
        id: 'expander',
        header: '',
        cell: (props: CellContext<EmployeeTransactionReconciliationTableModel, string>) => expandCell(props),
        enableSorting: false,
        footer: 'Total:',
        meta: {
            notExport: true,
        },
    },
    _defaultInfoColumn('date', 'Date'),
    columnHelper.group({
        id: 'employer',
        header: 'Employer',
        columns: [_defaultInfoColumn('employer_id', 'ID'), _defaultInfoColumn('employer_name', 'Name')],
    }),
    columnHelper.group({
        id: 'parent',
        header: 'Parent',
        columns: [_defaultInfoColumn('root_id', 'ID'), _defaultInfoColumn('root_name', 'Name')],
    }),
    _defaultInfoColumn('description', 'Type'),
    columnHelper.accessor('amount', {
        id: 'amount',
        header: 'Amount',
        cell: generateAmountCell<EmployeeTransactionReconciliationTableModel, number>(false),
        footer: generateTotalAmountCell<EmployeeTransactionReconciliationTableModel>('amount'),
    }),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<EmployeeTransactionReconciliationTableModel, string>) => {
            const {date, employer_id, description} = props.row.original;
            const params = {
                path: `/secured/accounting/empr/transactions/${employer_id}/${date}/export`,
                params: {
                    q: description,
                },
            };

            return <DownloadCell params={JSON.stringify(params)} />;
        },
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
];
