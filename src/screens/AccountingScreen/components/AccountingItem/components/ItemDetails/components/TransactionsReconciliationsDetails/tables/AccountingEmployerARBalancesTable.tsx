import {CellContext, createColumnHelper} from '@tanstack/react-table';
import {EmployerARBalancesTableModel} from '@/interfaces';
import {AmountCell} from '@/components/table/cells';
import {KasDownloadLink} from '@/components';
import {defaultInfoColumn, generateTotalAmountCell} from '@/utils/TableUtils';

const columnHelper = createColumnHelper<EmployerARBalancesTableModel>();

const _renderCell = (type: string) => {
    return (cell: CellContext<EmployerARBalancesTableModel, number>) => {
        const {primary_client_id, dateFrom, dateTo} = cell.row.original;
        const params = {
            path: `/secured/accounting/employer/${primary_client_id}/${type}`,
            params: {
                start_date: dateFrom,
                end_date: dateTo,
            },
        };

        return (
            <KasDownloadLink params={JSON.stringify(params)}>
                <AmountCell data={cell.getValue()} />
            </KasDownloadLink>
        );
    };
};
const _defaultInfoColumn = defaultInfoColumn<EmployerARBalancesTableModel>;

export const EmployerARBalancesTableColumns = [
    _defaultInfoColumn('primary_client_id', 'ID', 'Total:'),
    _defaultInfoColumn('primary_client_name', 'Name'),
    columnHelper.accessor('billed', {
        id: 'billed',
        header: 'Deduction Payment Billed',
        cell: _renderCell('billed'),
        footer: generateTotalAmountCell<EmployerARBalancesTableModel>('billed'),
    }),
    columnHelper.accessor('received', {
        id: 'received',
        header: 'Deduction Payment Received',
        cell: _renderCell('paid'),
        footer: generateTotalAmountCell<EmployerARBalancesTableModel>('received'),
    }),
    columnHelper.accessor('misc', {
        id: 'misc',
        header: 'Misc Received',
        cell: _renderCell('misc'),
        footer: generateTotalAmountCell<EmployerARBalancesTableModel>('misc'),
    }),
    columnHelper.accessor('balance', {
        id: 'balance',
        header: 'Balance',
        cell: _renderCell('all'),
        footer: generateTotalAmountCell<EmployerARBalancesTableModel>('balance'),
    }),
];
