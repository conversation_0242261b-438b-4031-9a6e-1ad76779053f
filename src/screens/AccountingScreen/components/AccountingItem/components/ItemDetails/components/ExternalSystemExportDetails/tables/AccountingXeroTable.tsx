import {createColumnHelper} from '@tanstack/react-table';
import {XeroTableModel} from '@/interfaces';
import {generateAmountCell} from '@/utils/TableUtils';

const columnHelper = createColumnHelper<XeroTableModel>();

export const XeroTableColumns = [
    columnHelper.accessor('narration', {
        id: 'narration',
        header: '*Narration',
    }),
    columnHelper.accessor('date', {
        id: 'date',
        header: '*Date',
    }),
    columnHelper.accessor('description', {
        id: 'description',
        header: 'Description',
    }),
    columnHelper.accessor('account_code', {
        id: 'account_code',
        header: '*AccountCode',
    }),
    columnHelper.accessor('tax_rate', {
        id: 'tax_rate',
        header: '*TaxRate',
    }),
    columnHelper.accessor('amount', {
        id: 'amount',
        header: '*Amount',
        cell: generateAmountCell<XeroTableModel, string>(false),
    }),
    columnHelper.group({
        id: 'tracking-1',
        header: 'Tracking 1',
        columns: [
            columnHelper.accessor('description', {
                id: 'tracking-name-1',
                header: 'Name',
            }),
            columnHelper.accessor('description', {
                id: 'tracking-option-1',
                header: 'Option',
            }),
        ],
    }),
    columnHelper.group({
        id: 'tracking-2',
        header: 'Tracking 2',
        columns: [
            columnHelper.accessor('description', {
                id: 'tracking-name-2',
                header: 'Name',
            }),
            columnHelper.accessor('description', {
                id: 'tracking-option-2',
                header: 'Option',
            }),
        ],
    }),
];
