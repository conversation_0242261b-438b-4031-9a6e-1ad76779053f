import {_rollForwardAmountColumn, _rollForwardFnColumn, _tableColumn, columnHelper} from './utils';
import {accountingSubtotalKas, accountingSubtotalKs1} from '@/utils/TableUtils';

export const KashableOwnedColumns = [
    _tableColumn('description', 'Description', false),
    _tableColumn('values.KAS', 'KAS Originated'),
    columnHelper.group({
        id: 'purchased',
        header: 'Purchased',
        columns: [
            _rollForwardAmountColumn('values.CRB', 'CRB Originated'),
            _rollForwardAmountColumn('values.BRB', 'BRB Originated'),
            _rollForwardAmountColumn('values.MDB', 'MDB Originated'),
        ],
    }),
    columnHelper.group({
        id: 'subtotals',
        header: 'Subtotals',
        columns: [
            _rollForwardFnColumn('KAS Owned', accountingSubtotalKas),
            _rollForwardFnColumn('KS1 Owned', accountingSubtotalKs1)
        ],
    }),
];
