import React, {useMemo} from 'react';
import {useAccounting} from '@/screens/AccountingScreen';
import {AccountingCompensationType, AccountingItemModel} from '@/interfaces';
import {BrokerCommissionForm, EmployerParticipationForm, EmployersForm, ReferralsForm} from './components';
import {AccountingFormProps} from '@/screens/AccountingScreen/components/AccountingItem/interfaces';

interface ItemFormProps {
    item: AccountingItemModel;
}

export const CompensationItemForm = ({item}: ItemFormProps) => {
    const {handleSearch} = useAccounting();

    const renderForm = useMemo(() => {
        const props: AccountingFormProps = {
            disabled: item.loading,
            onSearch: (url) => handleSearch(url, item.id),
        };

        switch (item.filterType) {
            case AccountingCompensationType.Broker_Commissions:
                return <BrokerCommissionForm {...props} />;
            case AccountingCompensationType.Employers:
                return <EmployersForm {...props} />;
            case AccountingCompensationType.Referrals:
                return <ReferralsForm {...props} />;
            case AccountingCompensationType.Employer_Participation:
                return <EmployerParticipationForm {...props} />;
            default:
                return null;
        }
    }, [item]);

    return <>{renderForm}</>;
};
