import {PickersShortcutsItem} from '@mui/x-date-pickers/PickersShortcuts';
import {DateRange} from '@mui/x-date-pickers-pro';
import dayjs, {Dayjs} from 'dayjs';

export const SHORTCUTS_RANGE_ITEMS: PickersShortcutsItem<DateRange<Dayjs>>[] = [
    {
        label: 'Last Month',
        getValue: () => {
            const prevMonth = dayjs().subtract(1, 'month');
            return [prevMonth.startOf('month'), prevMonth.endOf('month')];
        },
    },
    {
        label: 'Last Year',
        getValue: () => {
            const prevYear = dayjs().subtract(1, 'year');
            return [prevYear.startOf('year'), prevYear.endOf('year')];
        },
    },
];
