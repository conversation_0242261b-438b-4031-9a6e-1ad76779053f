import React, {useState} from 'react';
import {Button, Grid2} from '@mui/material';
import {AccountingFormProps} from '@/screens/AccountingScreen/components/AccountingItem/interfaces';
import {COMMON_SHORTCUTS} from './data';
import {KasDateRangePicker} from '@/components';
import {DayJsRangeModel} from '@/interfaces';
import {isValidRangeDate} from '@/utils/DateUtils';

export const CashEmployerForm = ({disabled, onSearch}: AccountingFormProps) => {
    const [dateRange, setDateRange] = useState<DayJsRangeModel>({start: null, end: null});

    const handleSearch = async () => {
        const dateFrom = dateRange.start?.format('YYYYMMDD');
        const dateTo = dateRange.end?.format('YYYYMMDD');
        const url = `/api/secured/accounting/transactions-reconciliations/cash-employer?dateFrom=${dateFrom}&dateTo=${dateTo}`;

        await onSearch(url);
    };

    return (
        <>
            <Grid2 size={3}>
                <KasDateRangePicker
                    value={dateRange}
                    disabled={disabled}
                    slotPropsItems={COMMON_SHORTCUTS}
                    onChange={setDateRange}
                />
            </Grid2>
            <Grid2 size={2.5}>
                <Button
                    fullWidth
                    variant='contained'
                    disabled={disabled || !isValidRangeDate(dateRange)}
                    onClick={handleSearch}>
                    Search
                </Button>
            </Grid2>
        </>
    );
};
