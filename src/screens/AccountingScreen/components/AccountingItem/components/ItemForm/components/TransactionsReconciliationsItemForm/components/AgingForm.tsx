import React, {useState} from 'react';
import {Button, SelectChangeEvent, Grid2} from '@mui/material';
import MenuItem from '@mui/material/MenuItem';
import {Dayjs} from 'dayjs';
import {SelectModel} from '@/interfaces';
import {AGING_VINTAGS_LIST} from './data';
import {AccountingFormProps} from '@/screens/AccountingScreen/components/AccountingItem/interfaces';
import {KasDatePicker, KasDownloadIconButton, KasSelect} from '@/components';

export const AgingForm = ({disabled, onSearch}: AccountingFormProps) => {
    const [date, setDate] = useState<Dayjs | null>(null);
    const [activeVintage, setActiveVintage] = useState<SelectModel<string>>(AGING_VINTAGS_LIST[0]);
    const params = JSON.stringify({
        path: `/secured/accounting/vintage/${activeVintage.value}/${date?.format('YYYYMMDD')}/export`,
    });

    const handleChangeVintage = (event: SelectChangeEvent) => {
        const newValue = event.target.value as string;
        const newActiveVintage = AGING_VINTAGS_LIST.find(({value}) => value === newValue);

        newActiveVintage && setActiveVintage(newActiveVintage);
    };

    const handleSearch = async () => {
        const dateFull = date?.format('YYYYMMDD');
        const url = `/api/secured/accounting/transactions-reconciliations/aging?dateFull=${dateFull}&vintage=${activeVintage.value}`;

        await onSearch(url);
    };

    return (
        <>
            <Grid2 size={3}>
                <KasDatePicker disabled={disabled} value={date} onChange={setDate} />
            </Grid2>
            <Grid2 size={3}>
                <KasSelect
                    labelId='kas-accounting-aging-vintage'
                    value={activeVintage.value}
                    disabled={disabled}
                    label='Vintage'
                    onChange={handleChangeVintage}>
                    {AGING_VINTAGS_LIST.map(({id, value, label}) => (
                        <MenuItem key={id} value={value}>
                            {label}
                        </MenuItem>
                    ))}
                </KasSelect>
            </Grid2>
            <Grid2 size={2.5}>
                <Button fullWidth variant='contained' disabled={disabled || !date} onClick={handleSearch}>
                    Search
                </Button>
            </Grid2>
            <Grid2 size={0.5}>
                <KasDownloadIconButton disabled={disabled || !date} params={params} />
            </Grid2>
        </>
    );
};
