import React, {useState} from 'react';
import {<PERSON>ton, Grid2} from '@mui/material';
import {AccountingFormProps} from '@/screens/AccountingScreen/components/AccountingItem/interfaces';
import {SelectModel} from '@/interfaces';
import {KasDateRangePicker, KasDownloadIconButton} from '@/components';
import {KasOwnerSelect} from '@/components/KasOwnerSelect/KasOwnerSelect';
import {OWNERS_LIST} from '@/components/KasOwnerSelect/data';
import {DayJsRangeModel} from '@/interfaces';
import {isValidRangeDate} from '@/utils/DateUtils';

export const OwnerListForm = ({disabled, onSearch}: AccountingFormProps) => {
    const [dateRange, setDateRange] = useState<DayJsRangeModel>({start: null, end: null});
    const [selectedOwner, setSelectedOwner] = useState<SelectModel<string>>(OWNERS_LIST[0]);
    const disableButtons = disabled || !isValidRangeDate(dateRange);
    const params = JSON.stringify({
        path: `/secured/accounting/owner/export`,
        params: {
            start_date: dateRange.start?.format('YYYYMMDD'),
            end_date: dateRange.end?.format('YYYYMMDD'),
            ow: selectedOwner?.value || '',
        },
    });

    const handleSearch = async () => {
        const dateFrom = dateRange.start?.format('YYYYMMDD');
        const dateTo = dateRange.end?.format('YYYYMMDD');
        const url = `/api/secured/accounting/transactions-reconciliations/ownership?dateFrom=${dateFrom}&dateTo=${dateTo}&owners=${selectedOwner?.value || ''}`;

        await onSearch(url);
    };

    return (
        <>
            <Grid2 size={3}>
                <KasDateRangePicker value={dateRange} disabled={disabled} onChange={setDateRange} />
            </Grid2>
            <Grid2 size={3}>
                <KasOwnerSelect
                    disabled={disabled}
                    labelId='accounting-acquisition-owner-select'
                    onOwnerSelect={setSelectedOwner}
                />
            </Grid2>
            <Grid2 size={2.5}>
                <Button fullWidth variant='contained' disabled={disableButtons} onClick={handleSearch}>
                    Search
                </Button>
            </Grid2>
            <Grid2 size={0.5}>
                <KasDownloadIconButton disabled={disableButtons} params={params} />
            </Grid2>
        </>
    );
};
