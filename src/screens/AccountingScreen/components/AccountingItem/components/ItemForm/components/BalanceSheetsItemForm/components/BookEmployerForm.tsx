import React, {useState} from 'react';
import {Button, Grid2} from '@mui/material';
import {Dayjs} from 'dayjs';
import {AccountingFormProps} from '@/screens/AccountingScreen/components/AccountingItem/interfaces';
import {SelectModel} from '@/interfaces';
import {KasAsyncSearchAutocompleteField, KasDatePicker} from '@/components';

export const BookEmployerForm = ({disabled, onSearch}: AccountingFormProps) => {
    const [date, setDate] = useState<Dayjs | null>(null);
    const [selectedEmployer, setSelectedEmployer] = useState<SelectModel<string> | null>(null);

    const handleSearch = async () => {
        const dateFull = date?.format('YYYYMMDD');
        const url = `/api/secured/accounting/balance-sheets/book-employer?dateFull=${dateFull}&name=${selectedEmployer?.id || ''}`;

        await onSearch(url);
    };

    return (
        <>
            <Grid2 size={3}>
                <KasDatePicker disabled={disabled} value={date} onChange={setDate} />
            </Grid2>
            <Grid2 size={3}>
                <KasAsyncSearchAutocompleteField
                    value={selectedEmployer}
                    label='Select Employer'
                    searchUrl={'/api/secured/ui/lookup/employer/name?flow=employers&name='}
                    onSelect={setSelectedEmployer}
                />
            </Grid2>
            <Grid2 size={2.5}>
                <Button
                    fullWidth
                    variant='contained'
                    disabled={disabled || !date || !selectedEmployer}
                    onClick={handleSearch}>
                    Search
                </Button>
            </Grid2>
        </>
    );
};
