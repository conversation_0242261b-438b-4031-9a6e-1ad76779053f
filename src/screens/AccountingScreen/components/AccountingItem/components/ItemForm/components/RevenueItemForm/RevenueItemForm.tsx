import React, {useMemo} from 'react';
import {useAccounting} from '@/screens/AccountingScreen';
import {AccountingItemModel, AccountingRevenueType} from '@/interfaces';
import {BillingForm, ChargeOffForm, ProjectedForm, RevenueForm, RevenueBankForm} from './components';
import {AccountingFormProps} from '@/screens/AccountingScreen/components/AccountingItem/interfaces';

interface ItemFormProps {
    item: AccountingItemModel;
}

export const RevenueItemForm = ({item}: ItemFormProps) => {
    const {handleSearch} = useAccounting();

    const renderForm = useMemo(() => {
        const props: AccountingFormProps = {
            disabled: item.loading,
            onSearch: (url) => handleSearch(url, item.id),
        };

        switch (item.filterType) {
            case AccountingRevenueType.Revenue:
                return <RevenueForm {...props} />;
            case AccountingRevenueType.Revenue_Bank:
                return <RevenueBankForm {...props} />;
            case AccountingRevenueType.Tables_Projected:
                return <ProjectedForm {...props} />;
            case AccountingRevenueType.Charge_Off:
                return <ChargeOffForm {...props} />;
            case AccountingRevenueType.Billing:
                return <BillingForm {...props} />;
            default:
                return null;
        }
    }, [item]);

    return <>{renderForm}</>;
};
