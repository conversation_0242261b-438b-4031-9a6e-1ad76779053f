import React, {useState} from 'react';
import {Button, SelectChangeEvent, Grid2} from '@mui/material';
import MenuItem from '@mui/material/MenuItem';
import {AccountingFormProps} from '@/screens/AccountingScreen/components/AccountingItem/interfaces';
import {SelectModel} from '@/interfaces';
import {REVENUE_GROUPING_LIST, SHORTCUTS_RANGE_ITEMS} from './data';
import {KasDateRangePicker, KasSelect} from '@/components';
import {DayJsRangeModel} from '@/interfaces';
import {isValidRangeDate} from '@/utils/DateUtils';

export const RevenueForm = ({disabled, onSearch}: AccountingFormProps) => {
    const [group, setGroup] = useState<SelectModel<string>>(REVENUE_GROUPING_LIST[0]);
    const [dateRange, setDateRange] = useState<DayJsRangeModel>({start: null, end: null});

    const handleSearch = async () => {
        const dateFrom = dateRange.start?.format('YYYYMMDD');
        const dateTo = dateRange.end?.format('YYYYMMDD');
        const url = `/api/secured/accounting/revenue/revenue?dateFrom=${dateFrom}&dateTo=${dateTo}&g=${group.id}`;

        await onSearch(url);
    };

    const handleChangeGrouping = (event: SelectChangeEvent) => {
        const newValue = event.target.value as string;
        const newGroup = REVENUE_GROUPING_LIST.find(({value}) => value === newValue);

        newGroup && setGroup(newGroup);
    };

    return (
        <>
            <Grid2 size={3}>
                <KasDateRangePicker
                    value={dateRange}
                    disabled={disabled}
                    slotPropsItems={SHORTCUTS_RANGE_ITEMS}
                    onChange={setDateRange}
                />
            </Grid2>
            <Grid2 size={3}>
                <KasSelect
                    labelId='kas-accounting-revenue-filter-grouping'
                    value={group.value}
                    disabled={disabled}
                    label='Grouping'
                    onChange={handleChangeGrouping}>
                    {REVENUE_GROUPING_LIST.map(({id, value, label}) => (
                        <MenuItem key={id} value={value}>
                            {label}
                        </MenuItem>
                    ))}
                </KasSelect>
            </Grid2>
            <Grid2 size={2.5}>
                <Button
                    fullWidth
                    variant='contained'
                    disabled={disabled || !isValidRangeDate(dateRange)}
                    onClick={handleSearch}>
                    Search
                </Button>
            </Grid2>
        </>
    );
};
