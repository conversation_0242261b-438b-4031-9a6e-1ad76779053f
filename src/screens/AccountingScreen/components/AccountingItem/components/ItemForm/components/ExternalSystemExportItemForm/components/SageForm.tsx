import React, {useState} from 'react';
import {AccountingFormProps} from '@/screens/AccountingScreen/components/AccountingItem/interfaces';
import {KasDateRangePicker, KasSelect} from '@/components';
import {SAGE_DATE_TYPE} from './data';
import {Button, SelectChangeEvent, MenuItem, Grid2} from '@mui/material';
import {SelectModel} from '@/interfaces';
import {DayJsRangeModel} from '@/interfaces';
import {isValidRangeDate} from '@/utils/DateUtils';

export const SageForm = ({disabled, onSearch}: AccountingFormProps) => {
    const [dateType, setDateType] = useState<SelectModel<string>>(SAGE_DATE_TYPE[0]);
    const [dateRange, setDateRange] = useState<DayJsRangeModel>({start: null, end: null});

    const handleChangeDateType = (event: SelectChangeEvent) => {
        const newValue = event.target.value as string;
        const newDateType = SAGE_DATE_TYPE.find(({value}) => value === newValue);

        newDateType && setDateType(newDateType);
    };

    const handleSearch = async () => {
        const dateFrom = dateRange.start?.format('YYYYMMDD');
        const dateTo = dateRange.end?.format('YYYYMMDD');
        const url = `/api/secured/accounting/external-system-export/sage?dateType=${dateType.value}&dateFrom=${dateFrom}&dateTo=${dateTo}`;

        await onSearch(url);
    };

    return (
        <>
            <Grid2 size={3}>
                <KasSelect
                    labelId='kas-accounting-export-sage-date-type'
                    value={dateType.value}
                    disabled={disabled}
                    label='Date Type'
                    onChange={handleChangeDateType}>
                    {SAGE_DATE_TYPE.map(({id, value, label}) => (
                        <MenuItem key={id} value={value}>
                            {label}
                        </MenuItem>
                    ))}
                </KasSelect>
            </Grid2>
            <Grid2 size={3}>
                <KasDateRangePicker value={dateRange} disabled={disabled} onChange={setDateRange} />
            </Grid2>
            <Grid2 size={2.5}>
                <Button
                    fullWidth
                    variant='contained'
                    disabled={disabled || !isValidRangeDate(dateRange)}
                    onClick={handleSearch}>
                    Search
                </Button>
            </Grid2>
        </>
    );
};
