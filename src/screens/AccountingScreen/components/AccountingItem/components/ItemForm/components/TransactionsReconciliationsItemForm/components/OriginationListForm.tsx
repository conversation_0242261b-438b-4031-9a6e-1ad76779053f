import React, {useState} from 'react';
import {Button, Grid2} from '@mui/material';
import {AccountingFormProps} from '@/screens/AccountingScreen/components/AccountingItem/interfaces';
import {SelectModel} from '@/interfaces';
import {KasAsyncSearchAutocompleteField, KasDateRangePicker} from '@/components';
import {DayJsRangeModel} from '@/interfaces';
import {isValidRangeDate} from '@/utils/DateUtils';

export const OriginationListForm = ({disabled, onSearch}: AccountingFormProps) => {
    const [dateRange, setDateRange] = useState<DayJsRangeModel>({start: null, end: null});
    const [selectedEmployer, setSelectedEmployer] = useState<SelectModel<string> | null>(null);

    const handleSearch = async () => {
        const dateFrom = dateRange.start?.format('YYYYMMDD');
        const dateTo = dateRange.end?.format('YYYYMMDD');
        const url = `/api/secured/accounting/transactions-reconciliations/origination-list?dateFrom=${dateFrom}&dateTo=${dateTo}&name=${selectedEmployer?.id || ''}`;

        await onSearch(url);
    };

    return (
        <>
            <Grid2 size={3}>
                <KasDateRangePicker value={dateRange} disabled={disabled} onChange={setDateRange} />
            </Grid2>
            <Grid2 size={3}>
                <KasAsyncSearchAutocompleteField
                    value={selectedEmployer}
                    label='Select Employer'
                    searchUrl={'/api/secured/ui/lookup/employer/name?flow=employers&name='}
                    onSelect={setSelectedEmployer}
                />
            </Grid2>
            <Grid2 size={2.5}>
                <Button
                    fullWidth
                    variant='contained'
                    disabled={disabled || !isValidRangeDate(dateRange)}
                    onClick={handleSearch}>
                    Search
                </Button>
            </Grid2>
        </>
    );
};
