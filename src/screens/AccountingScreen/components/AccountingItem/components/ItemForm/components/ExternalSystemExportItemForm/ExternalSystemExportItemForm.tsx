import React, {useMemo} from 'react';
import {useAccounting} from '@/screens/AccountingScreen';
import {AccountingExternalSystemExportType, AccountingItemModel} from '@/interfaces';
import {SageForm, XeroForm, XpayForm} from './components';
import {AccountingFormProps} from '@/screens/AccountingScreen/components/AccountingItem/interfaces';

interface ItemFormProps {
    item: AccountingItemModel;
}

export const ExternalSystemExportItemForm = ({item}: ItemFormProps) => {
    const {handleSearch} = useAccounting();

    const renderForm = useMemo(() => {
        const props: AccountingFormProps = {
            disabled: item.loading,
            onSearch: (url) => handleSearch(url, item.id),
        };

        switch (item.filterType) {
            case AccountingExternalSystemExportType.Xero:
                return <XeroForm {...props} />;
            case AccountingExternalSystemExportType.Xpay:
                return <XpayForm {...props} />;
            case AccountingExternalSystemExportType.Sage:
                return <SageForm {...props} />;
            default:
                return null;
        }
    }, [item]);

    return <>{renderForm}</>;
};
