import {PickersShortcutsItem} from '@mui/x-date-pickers/PickersShortcuts';
import {DateRange} from '@mui/x-date-pickers-pro';
import dayjs, {Dayjs} from 'dayjs';
import {SelectModel} from '@/interfaces';

export const SHORTCUTS_RANGE_ITEMS: PickersShortcutsItem<DateRange<Dayjs>>[] = [
    {
        label: 'Last Month',
        getValue: () => {
            const prevMonth = dayjs().subtract(1, 'month');
            return [prevMonth.startOf('month'), prevMonth.endOf('month')];
        },
    },
    {
        label: 'This Month',
        getValue: () => {
            const today = dayjs();
            return [today.startOf('month'), today.endOf('month')];
        },
    },
    {
        label: 'Next Month',
        getValue: () => {
            const nextMonth = dayjs().add(1, 'month');
            return [nextMonth.startOf('month'), nextMonth.endOf('month')];
        },
    },
    {
        label: 'Last Year',
        getValue: () => {
            const prevYear = dayjs().subtract(1, 'year');
            return [prevYear.startOf('year'), prevYear.endOf('year')];
        },
    },
    {
        label: 'This Year',
        getValue: () => {
            const today = dayjs();
            return [today.startOf('year'), today.endOf('year')];
        },
    },
    {
        label: 'Next Year',
        getValue: () => {
            const nextYear = dayjs().add(1, 'year');
            return [nextYear.startOf('year'), nextYear.endOf('year')];
        },
    },
];

export const REVENUE_GROUPING_LIST: SelectModel<string>[] = [
    {
        id: 'none',
        value: 'none',
        label: 'NONE',
    },
    {
        id: 'origination',
        value: 'origination',
        label: 'Origination Source',
    },
    {
        id: 'primary',
        value: 'primary',
        label: 'Primary Clients',
    },
    {
        id: 'employer',
        value: 'employer',
        label: 'Employer',
    },
    {
        id: 'intermediary',
        value: 'intermediary',
        label: 'Intermediary',
    },
    {
        id: 'state',
        value: 'state',
        label: 'State',
    },
];

export const PROJECTED_METHODS_LIST: SelectModel<string>[] = [
    {
        id: 'DEFAULT',
        value: 'DEFAULT',
        label: 'Default Method',
    },
    {
        id: 'EFFECTIVE',
        value: 'EFFECTIVE',
        label: 'Effective Int. Method',
    },
];
