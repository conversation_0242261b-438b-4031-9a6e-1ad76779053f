import {PickersShortcutsItem} from '@mui/x-date-pickers/PickersShortcuts';
import {DateRange} from '@mui/x-date-pickers-pro';
import dayjs, {Dayjs} from 'dayjs';
import {getPreviousWorkingDay} from '@/utils/DateUtils';
import {SelectModel} from '@/interfaces';

export const COMMON_SHORTCUTS: PickersShortcutsItem<DateRange<Dayjs>>[] = [
    {
        label: '-1 BD',
        getValue: () => {
            const day = getPreviousWorkingDay(1);
            return [day, day];
        },
    },
    {
        label: '-2 BD',
        getValue: () => {
            const day = getPreviousWorkingDay(2);
            return [day, day];
        },
    },
    {
        label: '-5 BD',
        getValue: () => {
            const day = getPreviousWorkingDay(5);
            return [day, day];
        },
    },
    {
        label: 'Last Month',
        getValue: () => {
            const prevMonth = dayjs().subtract(1, 'month');
            return [prevMonth.startOf('month'), prevMonth.endOf('month')];
        },
    },
    {
        label: 'Last Year',
        getValue: () => {
            const prevYear = dayjs().subtract(1, 'year');
            return [prevYear.startOf('year'), prevYear.endOf('year')];
        },
    },
];

export const AGING_VINTAGS_LIST: SelectModel<string>[] = [
    {
        id: 'loan',
        value: 'loan',
        label: 'Loan',
    },
    {
        id: 'delinquency',
        value: 'delinquency',
        label: 'Delinquency',
    },
];

export const VENDOR_COSTS_LIST: SelectModel<string>[] = [
    {
        id: 'experian',
        value: 'experian',
        label: 'Experian',
    },
    {
        id: 'equifax',
        value: 'equifax',
        label: 'Equifax',
    },
    {
        id: 'clarity',
        value: 'clarity',
        label: 'Clarity',
    },
    {
        id: 'plaid',
        value: 'plaid',
        label: 'Plaid',
    },
    {
        id: 'accurint',
        value: 'accurint',
        label: 'Accurint',
    },
    {
        id: 'pacer',
        value: 'pacer',
        label: 'Pacer',
    },
    {
        id: 'brightdime',
        value: 'brightdime',
        label: 'BrightDime',
    },
];
