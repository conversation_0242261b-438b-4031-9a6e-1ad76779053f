import React, {useMemo} from 'react';
import {AccountingItemModel, AccountingItemType} from '@/interfaces';
import {
    BalanceSheetsComparisonItemForm,
    CompensationComparisonItemForm,
    ExternalSystemExportComparisonItemForm,
    RevenueComparisonItemForm,
    TransactionsReconciliationsComparisonItemForm,
} from './components';

interface ComparisonItemFormProps {
    item: AccountingItemModel;
}

export const ComparisonItemForm = ({item}: ComparisonItemFormProps) => {
    const renderForm = useMemo(() => {
        switch (item.type) {
            case AccountingItemType.Revenue:
                return <RevenueComparisonItemForm item={item} />;
            case AccountingItemType.Compensation:
                return <CompensationComparisonItemForm item={item} />;
            case AccountingItemType.Balance_Sheets:
                return <BalanceSheetsComparisonItemForm item={item} />;
            case AccountingItemType.Transactions_Reconciliations:
                return <TransactionsReconciliationsComparisonItemForm item={item} />;
            case AccountingItemType.External_System_Export:
                return <ExternalSystemExportComparisonItemForm item={item} />;
            default:
                return null;
        }
    }, [item]);

    return <>{renderForm}</>;
};
