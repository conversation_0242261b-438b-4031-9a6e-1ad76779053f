import React, {useMemo} from 'react';
import {useAccounting} from '@/screens/AccountingScreen';
import {AccountingItemModel, AccountingRevenueType} from '@/interfaces';
import {AccountingFormProps} from '@/screens/AccountingScreen/components/AccountingItem/interfaces';
import {ProjectedComparisonForm, RevenueComparisonForm} from './components';

interface ItemFormProps {
    item: AccountingItemModel;
}

export const RevenueComparisonItemForm = ({item}: ItemFormProps) => {
    const {handleSearch} = useAccounting();

    const renderForm = useMemo(() => {
        const props: AccountingFormProps = {
            disabled: item.loading,
            onSearch: (url) => handleSearch(url, item.id),
        };

        switch (item.filterType) {
            case AccountingRevenueType.Revenue:
                return <RevenueComparisonForm {...props} />;
            case AccountingRevenueType.Tables_Projected:
                return <ProjectedComparisonForm {...props} />;
            case AccountingRevenueType.Charge_Off:
                return null;
            case AccountingRevenueType.Billing:
                return null;
            default:
                return null;
        }
    }, [item]);

    return <>{renderForm}</>;
};
