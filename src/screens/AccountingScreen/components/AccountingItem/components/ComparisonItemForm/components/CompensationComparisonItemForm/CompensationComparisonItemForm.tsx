import React, {useMemo} from 'react';
import {AccountingCompensationType, AccountingItemModel} from '@/interfaces';

interface ItemFormProps {
    item: AccountingItemModel;
}

export const CompensationComparisonItemForm = ({item}: ItemFormProps) => {
    const renderForm = useMemo(() => {
        switch (item.filterType) {
            case AccountingCompensationType.Broker_Commissions:
                return null;
            case AccountingCompensationType.Employers:
                return null;
            case AccountingCompensationType.Referrals:
                return null;
            default:
                return null;
        }
    }, [item]);

    return <>{renderForm}</>;
};
