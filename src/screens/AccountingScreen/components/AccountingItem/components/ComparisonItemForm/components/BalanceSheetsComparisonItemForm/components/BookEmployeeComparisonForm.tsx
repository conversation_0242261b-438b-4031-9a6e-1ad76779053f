import React, {useState} from 'react';
import {Button, Grid2} from '@mui/material';
import {Dayjs} from 'dayjs';
import {AccountingFormProps} from '@/screens/AccountingScreen/components/AccountingItem/interfaces';
import {KasDatePicker} from '@/components';

export const BookEmployeeComparisonForm = ({disabled, onSearch}: AccountingFormProps) => {
    const [dateFirst, setDateFirst] = useState<Dayjs | null>(null);
    const [dateSecond, setDateSecond] = useState<Dayjs | null>(null);

    const handleSearch = async () => {
        const dateFullFirst = dateFirst?.format('YYYYMMDD');
        const dateFullSecond = dateSecond?.format('YYYYMMDD');
        const url = `/api/secured/accounting-comparison/balance-sheets/book-employee?dateFullFirst=${dateFullFirst}&dateFullSecond=${dateFullSecond}`;

        await onSearch(url);
    };

    return (
        <>
            <Grid2 size={2}>
                <KasDatePicker disabled={disabled} value={dateFirst} onChange={setDateFirst} />
            </Grid2>
            <Grid2 size={2}>
                <KasDatePicker disabled={disabled} value={dateSecond} onChange={setDateSecond} />
            </Grid2>
            <Grid2 size={2.5}>
                <Button
                    fullWidth
                    variant='contained'
                    disabled={disabled || !dateFirst || !dateSecond}
                    onClick={handleSearch}>
                    Search
                </Button>
            </Grid2>
        </>
    );
};
