import React, {useMemo} from 'react';
import {useAccounting} from '@/screens/AccountingScreen';
import {AccountingBalanceSheetsType, AccountingItemModel} from '@/interfaces';
import {AccountingFormProps} from '@/screens/AccountingScreen/components/AccountingItem/interfaces';
import {BookEmployeeComparisonForm, BookEmployerComparisonForm} from './components';

interface ItemFormProps {
    item: AccountingItemModel;
}

export const BalanceSheetsComparisonItemForm = ({item}: ItemFormProps) => {
    const {handleSearch} = useAccounting();

    const renderForm = useMemo(() => {
        const props: AccountingFormProps = {
            disabled: item.loading,
            onSearch: (url) => handleSearch(url, item.id),
        };

        switch (item.filterType) {
            case AccountingBalanceSheetsType.Employee:
                return <BookEmployeeComparisonForm {...props} />;
            case AccountingBalanceSheetsType.Employer:
                return <BookEmployerComparisonForm {...props} />;
            case AccountingBalanceSheetsType.AR_Roll_Forward:
                return null;
            default:
                return null;
        }
    }, [item]);

    return <>{renderForm}</>;
};
