import React, {useState} from 'react';
import {Button, SelectChangeEvent, Grid2} from '@mui/material';
import MenuItem from '@mui/material/MenuItem';
import {Dayjs} from 'dayjs';
import {AccountingFormProps} from '@/screens/AccountingScreen/components/AccountingItem/interfaces';
import {SelectModel} from '@/interfaces';
import {PROJECTED_METHODS_LIST} from './data';
import {KasDatePicker, KasSelect} from '@/components';

export const ProjectedComparisonForm = ({disabled, onSearch}: AccountingFormProps) => {
    const [activeMethod, setActiveMethod] = useState<SelectModel<string>>(PROJECTED_METHODS_LIST[0]);
    const [dateFirst, setDateFirst] = useState<Dayjs | null>(null);
    const [dateSecond, setDateSecond] = useState<Dayjs | null>(null);

    const handleSearch = async () => {
        const dateMonthFirst = dateFirst?.format('YYYYMM');
        const dateMonthSecond = dateSecond?.format('YYYYMM');
        const url = `/api/secured/accounting-comparison/revenue/projected?dateMonthFirst=${dateMonthFirst}&dateMonthSecond=${dateMonthSecond}&mode=${activeMethod.value}`;

        await onSearch(url);
    };

    const handleChangeMethod = (event: SelectChangeEvent) => {
        const newValue = event.target.value as string;
        const newActiveMethod = PROJECTED_METHODS_LIST.find(({value}) => value === newValue);

        newActiveMethod && setActiveMethod(newActiveMethod);
    };

    return (
        <>
            <Grid2 size={2}>
                <KasDatePicker
                    disabled={disabled}
                    value={dateFirst}
                    label='Month'
                    views={['month', 'year']}
                    onChange={setDateFirst}
                />
            </Grid2>
            <Grid2 size={2}>
                <KasDatePicker
                    disabled={disabled}
                    value={dateSecond}
                    label='Month'
                    views={['month', 'year']}
                    onChange={setDateSecond}
                />
            </Grid2>
            <Grid2 size={2}>
                <KasSelect
                    labelId='kas-accounting-revenue-filter-method'
                    value={activeMethod.value}
                    disabled={disabled}
                    label='Method'
                    onChange={handleChangeMethod}>
                    {PROJECTED_METHODS_LIST.map(({id, value, label}) => (
                        <MenuItem key={id} value={value}>
                            {label}
                        </MenuItem>
                    ))}
                </KasSelect>
            </Grid2>
            <Grid2 size={2.5}>
                <Button
                    fullWidth
                    variant='contained'
                    disabled={disabled || !dateFirst || !dateSecond}
                    onClick={handleSearch}>
                    Search
                </Button>
            </Grid2>
        </>
    );
};
