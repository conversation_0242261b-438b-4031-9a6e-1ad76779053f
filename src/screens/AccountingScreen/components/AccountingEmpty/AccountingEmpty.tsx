import './styles.scss';

import React from 'react';
import {Button, Stack} from '@mui/material';
import emptyImage from './sources/empty-image.svg';
import Image from 'next/image';
import {MessageView} from '@/views';
import {useAccounting} from '@/screens/AccountingScreen';

export const AccountingEmpty = () => {
    const {availableItems, addActiveItem} = useAccounting();

    return (
        <div className='kas-accounting-empty'>
            <MessageView
                title='You don’t have any generated reports'
                subtitle='Please choose section to start'
                Image={<Image src={emptyImage} priority alt='You don’t have any generated reports' />}>
                <Stack direction='row' justifyContent='center' spacing={1}>
                    {availableItems.map((item) => (
                        <Button key={item.id} onClick={() => addActiveItem(item)} variant={'outlined'}>
                            {item.content}
                        </Button>
                    ))}
                </Stack>
            </MessageView>
        </div>
    );
};
