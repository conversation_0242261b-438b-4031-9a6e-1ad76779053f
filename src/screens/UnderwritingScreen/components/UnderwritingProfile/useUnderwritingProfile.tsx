import React, {createContext, useCallback, useContext, useEffect, useRef, useState} from 'react';
import {UnderwritingSearchDTO} from '@/models';
import {underwritingId, underwritingTyp} from '@/utils/UnderwritingUtils';
import {
    UnderwritingActionsModel,
    UnderwritingDirectDepositsModel,
    UnderwritingEmployeeProfileItemModel,
    UnderwritingEmployerModel,
    UnderwritingReferralsModel,
    UnderwritingUserProfileItemModel,
} from '@/screens/UnderwritingScreen/interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {Completable} from '@/interfaces';
import {useSnackbar} from '@/hooks/useSnackbar';
import {
    UnderwritingProfileAction,
    UnderwritingProfileActionModalProps,
    UnderwritingProfileLoanAlertsModel,
} from '@/screens/UnderwritingScreen/components/UnderwritingProfile/interfaces';
import {DataStateInterface} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {DEFAULT_ERROR_MSG, DEFAULT_SUCCESS_MSG} from '@/constants';
import {useSecured} from '@/hooks/useSecured';
import {useAppDispatch} from '@/lib/hooks';
import {updateProfile, updateSearch} from '@/lib/slices/underwritingSlice';
import {RestrictPermissionKey, RestrictPermissionType} from '@/models/restrictDTO';
import {useLockedAsync} from '@/hooks/useLockedAsync';

interface UnderwritingProfileContextModel {
    typ: string;
    gid: number;
    profile: UnderwritingSearchDTO;
    actionsState: DataStateInterface<UnderwritingActionsModel[]>;
    loadActionsData: () => Promise<void>;
    employeeProfileState: DataStateInterface<UnderwritingEmployeeProfileItemModel>;
    loadEmployeeProfileData: () => Promise<void>;
    employerState: DataStateInterface<UnderwritingEmployerModel>;
    loadEmployerData: () => Promise<void>;
    directDepositState: DataStateInterface<UnderwritingDirectDepositsModel[]>;
    loadDirectDepositData: () => Promise<void>;
    referralsState: DataStateInterface<UnderwritingReferralsModel[]>;
    loadReferralsData: () => Promise<void>;
    userProfileState: DataStateInterface<UnderwritingUserProfileItemModel>;
    loadUserProfileData: () => Promise<void>;
    openActionModal: UnderwritingProfileActionModalProps | null;
    setOpenActionModal: (value: UnderwritingProfileActionModalProps | null) => void;
    submittingAction: boolean;
    actionError: string;
    clearActionError: () => void;
    onSubmitAction: (
        url: string,
        body: string | FormData,
        method: 'post' | 'put',
        updateData?: boolean,
    ) => Promise<Completable<any>>;
    letterLoanId: string;
    setLetterLoanId: (value: string) => void;
    loanAlerts: UnderwritingProfileLoanAlertsModel;
    confirmLoanAlert: (alert: 'bankruptcyAlert' | 'scraAlert') => void;
    checkIsContactRestricted: (key: keyof RestrictPermissionType) => string | null;
}

const UnderwritingProfileContext = createContext<UnderwritingProfileContextModel | undefined>(undefined);

interface UnderwritingProfileProviderProps {
    children: React.ReactNode;
    profile: UnderwritingSearchDTO;
}

export const UnderwritingProfileProvider: React.FC<UnderwritingProfileProviderProps> = ({
    children,
    profile,
}) => {
    const dispatch = useAppDispatch();
    const {showMessage} = useSnackbar();
    const {hasAnyRole} = useSecured();
    const {runLockedFunc} = useLockedAsync();
    const abortControllerRef = useRef<AbortController | null>(null);
    const typ = underwritingTyp(profile);
    const gid = underwritingId(profile);
    const [actionsState, setActionsState] = useState(getDefaultState<UnderwritingActionsModel[]>());
    const [employeeProfileState, setEmployeeProfileState] =
        useState(getDefaultState<UnderwritingEmployeeProfileItemModel>());
    const [employerState, setEmployerState] = useState(getDefaultState<UnderwritingEmployerModel>());
    const [directDepositState, setDirectDepositState] =
        useState(getDefaultState<UnderwritingDirectDepositsModel[]>());
    const [referralsState, setReferralsState] = useState(getDefaultState<UnderwritingReferralsModel[]>());
    const [userProfileState, setUserProfileState] =
        useState(getDefaultState<UnderwritingUserProfileItemModel>());
    const [openActionModal, setOpenActionModal] = useState<UnderwritingProfileActionModalProps | null>(null);
    const [submittingAction, setSubmittingAction] = useState(false);
    const [actionError, setActionError] = useState('');
    const [letterLoanId, setLetterLoanId] = useState<string>('');
    const [loanAlerts, setLoanAlerts] = useState<UnderwritingProfileLoanAlertsModel>({
        bankruptcyAlert: null,
        scraAlert: null,
    });

    const checkIsContactRestricted = useCallback(
        (key: keyof RestrictPermissionType) => {
            if (!employeeProfileState.data?.restrictions) {
                return null;
            }

            const enumKeys = Object.values(RestrictPermissionKey) as string[];

            return enumKeys.includes(key) ? employeeProfileState.data.restrictions[key] : null;
        },
        [employeeProfileState.data?.restrictions],
    );

    const loadActionsData = async () => {
        const route = typ === 'USER' ? 'user' : 'employee';
        const url = `/api/secured/underwriting/actions?id=${gid}&route=${route}`;

        setActionsState(getLoadingState(actionsState));

        const response = await apiRequest(url);

        setActionsState(getLoadedState(response));
    };

    const loadEmployeeProfileData = async () => {
        if (typ === 'EMPL') {
            await runLockedFunc('loadEmployeeProfileData', async () => {
                const url = `/api/secured/underwriting/employee-profile/${gid}`;

                setEmployeeProfileState(getLoadingState(employeeProfileState));

                const response = await apiRequest(url);
                const employeeState: DataStateInterface<UnderwritingEmployeeProfileItemModel> =
                    getLoadedState(response);

                setEmployeeProfileState(employeeState);

                if (employeeState.data) {
                    const profileData: UnderwritingSearchDTO = {
                        ...profile,
                        application_status: employeeState.data.application_status,
                        employee_first_name: employeeState.data.first_name,
                        employee_id: employeeState.data.gid,
                        employee_last_name: employeeState.data.last_name,
                        open_bankruptcy_petition: employeeState.data.open_bankruptcy_petition,
                        user_id: employeeState.data.user_id,
                        current_loan_id: employeeState.data.current_loan_id,
                        employer_name: employeeState.data.employer_name,
                    };

                    updateLoanAlert(employeeState.data);
                    dispatch(updateProfile(profileData));
                }
            });
        }
    };

    const updateLoanAlert = (employeeState: UnderwritingEmployeeProfileItemModel) => {
        const bankruptcyAlertCondition =
            employeeState.open_bankruptcy_petition && !hasAnyRole(['KASH_COMPLIANCE'], true);
        const scraAlertCondition = !!employeeState.scra_open_date || !!employeeState.scra_close_date;

        setLoanAlerts(({bankruptcyAlert, scraAlert}) => ({
            bankruptcyAlert: bankruptcyAlert === null ? bankruptcyAlertCondition : bankruptcyAlert,
            scraAlert: scraAlert === null ? scraAlertCondition : scraAlert,
        }));
    };

    const confirmLoanAlert = (alert: 'bankruptcyAlert' | 'scraAlert') => {
        setLoanAlerts((prevState) => ({
            ...prevState,
            [alert]: false,
        }));
    };

    const loadEmployerData = async () => {
        const url = `/api/secured/underwriting/employer/${gid}?f=ELIGIBILITY`;

        setEmployerState(getLoadingState(employerState));

        const response = await apiRequest(url);

        setEmployerState(getLoadedState(response));
    };

    const loadDirectDepositData = async () => {
        const url = `/api/secured/underwriting/direct-deposits?id=${gid}`;

        setDirectDepositState(getLoadingState(directDepositState));

        const response = await apiRequest(url);

        setDirectDepositState(getLoadedState(response));
    };

    const loadReferralsData = async () => {
        const url = `/api/secured/underwriting/referrals?id=${gid}`;

        setReferralsState(getLoadingState(referralsState));

        const response = await apiRequest(url);

        setReferralsState(getLoadedState(response));
    };

    const loadUserProfileData = async () => {
        await runLockedFunc('loadUserProfileData', async () => {
            const url = `/api/secured/underwriting/user-profile?typ=${typ}&gid=${gid}`;

            setUserProfileState(getLoadingState(userProfileState));

            const response = await apiRequest(url);

            setUserProfileState(getLoadedState(response));
        });
    };

    const clearActionError = () => {
        setActionError('');
    };

    const onSubmitAction = async (url: string, body: string | FormData, method: 'post' | 'put') => {
        abortControllerRef.current = new AbortController();
        const signal = abortControllerRef.current.signal;
        setSubmittingAction(true);
        clearActionError();
        const response = await apiRequest(url, {method, body, signal});

        if (response.error) {
            setActionError(response.error);
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            showMessage(DEFAULT_SUCCESS_MSG, 'success');
            setOpenActionModal(null);
            await loadActionsData();
            await updateContentData();
        }

        setSubmittingAction(false);
        return response;
    };

    const updateContentData = async () => {
        dispatch(updateSearch());
        switch (openActionModal?.type) {
            case UnderwritingProfileAction.DIRECT_DEPOSIT_REPORT:
            case UnderwritingProfileAction.REQUEST_LETTER:
            case UnderwritingProfileAction.SET_TERMINATED:
            case UnderwritingProfileAction.SET_DECEASED:
            case UnderwritingProfileAction.UNFLAG_DECEASED:
            case UnderwritingProfileAction.PAID_LEAVE:
            case UnderwritingProfileAction.UNPAID_LEAVE:
            case UnderwritingProfileAction.PAYROLL_DEDUCTION_REVOKE:
            case UnderwritingProfileAction.PAYROLL_DEDUCTION_AUTHORIZE:
            case UnderwritingProfileAction.ADD_LOAN_HARDSHIP:
            case UnderwritingProfileAction.REMOVE_LOAN_HARDSHIP:
            case UnderwritingProfileAction.UNFLAG_INCOME:
            case UnderwritingProfileAction.CLOSE_BANKRUPTCY:
            case UnderwritingProfileAction.ADD_REFERRAL:
            case UnderwritingProfileAction.FLAGGING_SCRA_MODAL:
            case UnderwritingProfileAction.CANCEL_ACH_PAYMENT:
            case UnderwritingProfileAction.MANUAL_DEBIT_CARD_PAYMENT:
            case UnderwritingProfileAction.UNENROLL_CREDIT_MONITORING:
                await loadEmployeeProfileData();
                break;
            case UnderwritingProfileAction.VOID_LOAN:
            case UnderwritingProfileAction.RUN_FULFILLMENT:
            case UnderwritingProfileAction.RUN_FULFILLMENT_BF:
            case UnderwritingProfileAction.CHARGEOFF_LOAN:
            case UnderwritingProfileAction.SETTLE_LOAN:
                // TODO: load loan-history
                await loadEmployeeProfileData();
                break;
            case UnderwritingProfileAction.VERIFICATION_STATUS:
                await loadUserProfileData();
                break;
            case UnderwritingProfileAction.UNFLAG_REVIEW:
            case UnderwritingProfileAction.CLEAR_EMPLOYMENT:
                if (typ === 'USER') {
                    await loadUserProfileData();
                } else {
                    await loadEmployeeProfileData();
                }
                break;
            case UnderwritingProfileAction.FLAGGING_MODAL:
                // TODO: if !(response && response.state === "ACTION_ABORTED")
                await loadEmployeeProfileData();
                break;
            case UnderwritingProfileAction.DISBURSEMENT_REDISBURSE_LOAN:
                // TODO: load transaction
                break;
            case UnderwritingProfileAction.FLAG_REVIEW:
                await loadUserProfileData();
                break;

            case UnderwritingProfileAction.ADD_COMMENT:
                break;
            case UnderwritingProfileAction.UPLOAD_ACH:
                break;
            case UnderwritingProfileAction.DECLINE_APPLICATION:
                break;
            case UnderwritingProfileAction.LETTER_PREVIEW:
                break;
            case UnderwritingProfileAction.SEND_CTA_EMAIL:
                break;
            case UnderwritingProfileAction.FLAG_FOR_BANKRUPTCY:
            case UnderwritingProfileAction.CLEAR_BANKRUPTCY:
            case UnderwritingProfileAction.IDENTITY_VERIFIED:
            case UnderwritingProfileAction.RESTRICT_CONTACT:
                await loadEmployeeProfileData();
                break;
            case UnderwritingProfileAction.EDIT_EMPLOYEE_PROFILE:
                loadEmployeeProfileData().then();
                loadUserProfileData().then();
                break;
            case UnderwritingProfileAction.RESET_MFA:
                break;
        }
    };

    useEffect(() => {
        if (abortControllerRef.current) {
            abortControllerRef.current.abort();
        }
        if (!openActionModal) {
            setSubmittingAction(false);
        }
    }, [openActionModal]);

    const value: UnderwritingProfileContextModel = {
        typ,
        gid,
        profile,
        actionsState,
        loadActionsData,
        employeeProfileState,
        loadEmployeeProfileData,
        employerState,
        loadEmployerData,
        directDepositState,
        loadDirectDepositData,
        referralsState,
        loadReferralsData,
        userProfileState,
        loadUserProfileData,
        openActionModal,
        setOpenActionModal,
        submittingAction,
        actionError,
        clearActionError,
        onSubmitAction,
        letterLoanId,
        setLetterLoanId,
        loanAlerts,
        confirmLoanAlert,
        checkIsContactRestricted,
    };

    return (
        <UnderwritingProfileContext.Provider value={value}>{children}</UnderwritingProfileContext.Provider>
    );
};

export function useUnderwritingProfile() {
    const context = useContext(UnderwritingProfileContext);
    if (!context) {
        throw new Error('useUnderwritingProfile must be used within UnderwritingProfileProvider');
    }
    return context;
}
