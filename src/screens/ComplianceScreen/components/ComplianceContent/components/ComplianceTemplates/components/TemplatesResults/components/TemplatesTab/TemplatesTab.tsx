import React, {useRef, useState} from 'react';
import {Chip, Stack} from '@mui/material';
import {useWindowEventObserver} from '@/hooks/useWindowEventObserver';
import {KasStickyHeadBox} from '@/components';
import {useComplianceTemplates} from './../../../../useComplianceTemplates';

export const TemplatesTab = () => {
    const {activeTemplate, groupedTemplateData, setActiveTemplate} = useComplianceTemplates();
    const [isSticky, setIsSticky] = useState(false);
    const stickyRef = useRef<HTMLDivElement | null>(null);

    useWindowEventObserver('scroll', stickyRef, (value: number) => {
        setIsSticky(value <= 0);
    });

    return (
        <KasStickyHeadBox py={1} ref={stickyRef} isSticky={isSticky}>
            <Stack direction='row' spacing={1} useFlexGap flexWrap='wrap'>
                {groupedTemplateData.map(({template, data}) => (
                    <Chip
                        key={template}
                        onClick={() => setActiveTemplate(template)}
                        label={`${template} (${data.length})`}
                        variant='outlined'
                        color={activeTemplate === template ? 'info' : 'default'}
                    />
                ))}
            </Stack>
        </KasStickyHeadBox>
    );
};
