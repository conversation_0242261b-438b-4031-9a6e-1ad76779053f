import React, {createContext, useContext, useEffect, useMemo, useState} from 'react';
import {DataStateInterface} from '@/interfaces';
import {ComplianceTemplateModel, ComplianceTemplateType} from '@/screens/ComplianceScreen/interfaces';
import {useSnackbar} from '@/hooks/useSnackbar';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {uniqElements} from '@/utils/ArrayUtils';

interface ComplianceTemplatesContextModel {
    dataState: DataStateInterface<ComplianceTemplateModel[]>;
    loadData: (searchText: string) => Promise<void>;
    groupedTemplateData: {
        template: ComplianceTemplateType;
        data: ComplianceTemplateModel[];
    }[];
    activeTemplate: ComplianceTemplateType | undefined;
    setActiveTemplate: (value: ComplianceTemplateType) => void;
}

const ComplianceTemplatesContext = createContext<ComplianceTemplatesContextModel | undefined>(undefined);

export const ComplianceTemplatesProvider = ({children}: {children: React.ReactNode}) => {
    const {showMessage} = useSnackbar();
    const [dataState, setDataState] =
        useState<DataStateInterface<ComplianceTemplateModel[]>>(getDefaultState());
    const [activeTemplate, setActiveTemplate] = useState<ComplianceTemplateType | undefined>();

    const groupedTemplateData = useMemo(() => {
        const data = dataState.data || [];
        const uniqTemplates = uniqElements(data, (item) => item.template);

        return uniqTemplates.map((template) => ({
            template,
            data: data.filter((item) => item.template === template),
        }));
    }, [dataState.data]);

    const loadData = async (searchText: string) => {
        const url = `/api/secured/compliance/templates?searchText=${encodeURIComponent(searchText)}`;

        setDataState(getLoadingState(dataState));
        const response = await apiRequest(url);
        setDataState(getLoadedState(response));

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        }
    };

    useEffect(() => {
        if (!!groupedTemplateData.length) {
            setActiveTemplate(groupedTemplateData[0].template);
        }
    }, [groupedTemplateData]);

    const value: ComplianceTemplatesContextModel = {
        dataState,
        loadData,
        groupedTemplateData,
        activeTemplate,
        setActiveTemplate,
    };

    return (
        <ComplianceTemplatesContext.Provider value={value}>{children}</ComplianceTemplatesContext.Provider>
    );
};

export function useComplianceTemplates() {
    const context = useContext(ComplianceTemplatesContext);
    if (!context) {
        throw new Error('useComplianceTemplates must be used within ComplianceTemplatesProvider');
    }
    return context;
}
