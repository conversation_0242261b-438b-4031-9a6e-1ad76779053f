import React from 'react';
import {LoansFeesDetails, LoansFeesHead} from './components';
import {
    ComplianceMoveableItem,
    useComplianceLoans,
} from '@/screens/ComplianceScreen/components/ComplianceContent/components';
import {ComplianceLoansItemModel, ComplianceLoansItemType} from './../../interfaces';

interface ActionsProps {
    item: ComplianceLoansItemModel;
}

export const LoansFees = ({item}: ActionsProps) => {
    const {loansState} = useComplianceLoans();
    const curState = loansState[ComplianceLoansItemType.Fees];

    return (
        <ComplianceMoveableItem
            title={item.title}
            loading={curState.loading}
            loadingError={curState.error}
            loaded={!!curState.data}
            PreviewComponent={<LoansFeesHead />}
            DetailsComponent={<LoansFeesDetails curState={curState} />}
        />
    );
};
