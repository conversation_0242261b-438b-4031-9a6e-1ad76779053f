import {defaultAmountColumn, defaultInfoColumn} from '@/utils/TableUtils';
import {ComplianceLoanFeesModel} from '@/screens/ComplianceScreen/interfaces';

const _defaultInfoColumn = defaultInfoColumn<ComplianceLoanFeesModel>;
const _defaultAmountColumn = defaultAmountColumn<ComplianceLoanFeesModel>;

export const LoanFeesTableColumns = [
    _defaultInfoColumn('loan_id', 'Loan ID'),
    _defaultInfoColumn('first_name', 'First Name'),
    _defaultInfoColumn('last_name', 'Last Name'),
    _defaultInfoColumn('entry_date', 'Entry Date'),
    _defaultInfoColumn('effective_date', 'Effective Date'),
    _defaultInfoColumn('transaction_type', 'Type'),
    _defaultAmountColumn('amount', 'Amount', false),
];
