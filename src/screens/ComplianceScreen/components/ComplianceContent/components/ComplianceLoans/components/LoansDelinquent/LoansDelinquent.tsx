import React from 'react';
import {LoansDelinquentDetails, LoansDelinquentHead} from './components';
import {
    ComplianceMoveableItem,
    useComplianceLoans,
} from '@/screens/ComplianceScreen/components/ComplianceContent/components';
import {ComplianceLoansItemModel, ComplianceLoansItemType} from './../../interfaces';

interface ActionsProps {
    item: ComplianceLoansItemModel;
}

export const LoansDelinquent = ({item}: ActionsProps) => {
    const {loansState} = useComplianceLoans();
    const curState = loansState[ComplianceLoansItemType.Delinquent];

    return (
        <ComplianceMoveableItem
            title={item.title}
            loading={curState.loading}
            loadingError={curState.error}
            loaded={!!curState.data}
            PreviewComponent={<LoansDelinquentHead />}
            DetailsComponent={<LoansDelinquentDetails curState={curState} />}
        />
    );
};
