import React from 'react';
import {ColumnDef} from '@tanstack/react-table';
import {ComplianceLoanFeesModel} from '@/screens/ComplianceScreen/interfaces';
import {LoanFeesTableColumns} from './tables/LoanFeesTableColumns';
import {TableView} from '@/views';
import {DataStateInterface} from '@/interfaces';
import {getCurrencyExcelFormat} from '@/utils/TableUtils';

export const LoansFeesDetails = ({curState}: {curState: DataStateInterface<ComplianceLoanFeesModel[]>}) => {
    const excelColumnFormats = ['amount'].map(getCurrencyExcelFormat);

    return (
        <TableView<ComplianceLoanFeesModel>
            withTableActions={true}
            loading={curState.loading}
            error={curState.error}
            data={curState.data}
            columns={LoanFeesTableColumns as ColumnDef<ComplianceLoanFeesModel, unknown>[]}
            tableName='Compliance Loans Fees'
            sortingColumns={[
                {id: 'loan_id', desc: false},
                {id: 'effective_date', desc: false},
            ]}
            excelColumnFormats={excelColumnFormats}
        />
    );
};
