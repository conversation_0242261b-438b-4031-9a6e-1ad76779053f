import React from 'react';
import {LoansDeniedDetails, LoansDeniedHead} from './components';
import {
    ComplianceMoveableItem,
    useComplianceLoans,
} from '@/screens/ComplianceScreen/components/ComplianceContent/components';
import {ComplianceLoansItemModel, ComplianceLoansItemType} from './../../interfaces';

interface ActionsProps {
    item: ComplianceLoansItemModel;
}

export const LoansDenied = ({item}: ActionsProps) => {
    const {loansState} = useComplianceLoans();
    const curState = loansState[ComplianceLoansItemType.Denied];

    return (
        <ComplianceMoveableItem
            title={item.title}
            loading={curState.loading}
            loadingError={curState.error}
            loaded={!!curState.data}
            PreviewComponent={<LoansDeniedHead />}
            DetailsComponent={<LoansDeniedDetails curState={curState} />}
        />
    );
};
