import React from 'react';
import {LoansClosedDetails, LoansClosedHead} from './components';
import {
    ComplianceMoveableItem,
    useComplianceLoans,
} from '@/screens/ComplianceScreen/components/ComplianceContent/components';
import {ComplianceLoansItemModel, ComplianceLoansItemType} from './../../interfaces';

interface ActionsProps {
    item: ComplianceLoansItemModel;
}

export const LoansClosed = ({item}: ActionsProps) => {
    const {loansState} = useComplianceLoans();
    const curState = loansState[ComplianceLoansItemType.Closed];

    return (
        <ComplianceMoveableItem
            title={item.title}
            loading={curState.loading}
            loadingError={curState.error}
            loaded={!!curState.data}
            PreviewComponent={<LoansClosedHead />}
            DetailsComponent={<LoansClosedDetails curState={curState} />}
        />
    );
};
