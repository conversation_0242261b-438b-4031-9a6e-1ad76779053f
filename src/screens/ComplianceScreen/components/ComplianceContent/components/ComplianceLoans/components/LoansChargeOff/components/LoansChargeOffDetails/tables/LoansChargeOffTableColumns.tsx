import {defaultAmountColumn, defaultInfoColumn} from '@/utils/TableUtils';
import {ComplianceLoanStatisticModel} from '@/screens/ComplianceScreen/interfaces';
import {complianceDownloadLoanActionColumn} from '@/screens/ComplianceScreen/components/ComplianceContent/components/ComplianceLoans/utils';

const _defaultInfoColumn = defaultInfoColumn<ComplianceLoanStatisticModel>;
const _defaultAmountColumn = defaultAmountColumn<ComplianceLoanStatisticModel>;

export const LoansChargeOffTableColumns = [
    _defaultInfoColumn('loan_id', 'Loan ID'),
    _defaultInfoColumn('origination_source', 'LOS'),
    _defaultInfoColumn('first_name', 'First Name'),
    _defaultInfoColumn('last_name', 'Last Name'),
    _defaultInfoColumn('disbursement_date', 'Disbursement Date'),
    _defaultInfoColumn('close_date', 'Close Date'),
    _defaultAmountColumn('amount', 'Amount', false),
    _defaultInfoColumn('interest_rate', 'Rate'),
    _defaultInfoColumn('apr', 'APR'),
    _defaultAmountColumn('origination_fee', 'Fee', false),
    _defaultInfoColumn('chargeoff_date', 'Charge Off Date'),
    _defaultAmountColumn('chargeoff_amount', 'Balance', false),
    complianceDownloadLoanActionColumn,
];
