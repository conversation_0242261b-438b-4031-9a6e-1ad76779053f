import React from 'react';
import {LoansOriginatedDetails, LoansOriginatedHead} from './components';
import {
    ComplianceMoveableItem,
    useComplianceLoans,
} from '@/screens/ComplianceScreen/components/ComplianceContent/components';
import {ComplianceLoansItemModel, ComplianceLoansItemType} from './../../interfaces';

interface ActionsProps {
    item: ComplianceLoansItemModel;
}

export const LoansOriginated = ({item}: ActionsProps) => {
    const {loansState} = useComplianceLoans();
    const curState = loansState[ComplianceLoansItemType.Originated];

    return (
        <ComplianceMoveableItem
            title={item.title}
            loading={curState.loading}
            loadingError={curState.error}
            loaded={!!curState.data}
            PreviewComponent={<LoansOriginatedHead />}
            DetailsComponent={<LoansOriginatedDetails curState={curState} />}
        />
    );
};
