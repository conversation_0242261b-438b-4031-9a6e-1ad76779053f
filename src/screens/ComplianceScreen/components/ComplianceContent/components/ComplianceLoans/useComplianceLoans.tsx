import React, {createContext, useContext, useEffect, useState} from 'react';
import {getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {DataStateInterface} from '@/interfaces';
import {
    ComplianceLoanStatisticModel,
    ComplianceLoanDeniedModel,
    ComplianceLoanFeesModel,
} from '@/screens/ComplianceScreen/interfaces';
import {ComplianceLoansItemModel, ComplianceLoansItemType, ComplianceLoanStateType} from './interfaces';
import {AVAILABLE_LOANS_ITEMS, COMPLIANCE_LOANS_STATE} from './data';
import {Completable} from '@/interfaces';
import {useAppDispatch, useAppSelector} from '@/lib/hooks';
import {selectUserPreferences, updateUserPreferences} from '@/lib/slices/userPreferencesSlice';

type ComplianceDetailsModel =
    | ComplianceLoanDeniedModel
    | ComplianceLoanStatisticModel
    | ComplianceLoanFeesModel;

interface ComplianceLoansContextModel {
    loanItems: ComplianceLoansItemModel[];
    updateLoanItems: (data: ComplianceLoansItemModel[]) => void;
    loansState: ComplianceLoanStateType;
    loadLoans: (
        type: ComplianceLoansItemType,
        params: string,
    ) => Promise<Completable<ComplianceDetailsModel[]>>;
    resetState: (type: ComplianceLoansItemType) => void;
}

const ComplianceLoansContext = createContext<ComplianceLoansContextModel | undefined>(undefined);

export const ComplianceLoansProvider = ({children}: {children: React.ReactNode}) => {
    const dispatch = useAppDispatch();
    const userPreferences = useAppSelector(selectUserPreferences);
    const [loanItems, setLoanItems] = useState<ComplianceLoansItemModel[]>([]);
    const [loansState, setLoansState] = useState(COMPLIANCE_LOANS_STATE);

    const updateLoanItems = (sections: ComplianceLoansItemModel[]) => {
        const compliance = userPreferences.value?.compliance || {};
        setLoanItems(sections);
        dispatch(updateUserPreferences({compliance: {...compliance, loans: {sections}}}));
    };

    const loadLoans = async (
        type: ComplianceLoansItemType,
        params: string,
    ): Promise<Completable<ComplianceDetailsModel[]>> => {
        const url = `/api/secured/compliance/loans/${type}?${params}`;

        setLoansState((prevState) => {
            return {
                ...prevState,
                [type]: (() => {
                    switch (type) {
                        case ComplianceLoansItemType.Denied:
                            return getLoadingState(
                                prevState[type] as DataStateInterface<ComplianceLoanDeniedModel[]>,
                            );
                        case ComplianceLoansItemType.Fees:
                            return getLoadingState(
                                prevState[type] as DataStateInterface<ComplianceLoanFeesModel[]>,
                            );
                        default:
                            return getLoadingState(
                                prevState[type] as DataStateInterface<ComplianceLoanStatisticModel[]>,
                            );
                    }
                })(),
            };
        });

        const response = await apiRequest(url);

        setLoansState((prevState) => ({
            ...prevState,
            [type]: getLoadedState(response),
        }));

        return response;
    };

    const resetState = (type: ComplianceLoansItemType) => {
        setLoansState((prevState) => ({
            ...prevState,
            [type]: COMPLIANCE_LOANS_STATE[type],
        }));
    };

    const setSortedItems = () => {
        const sections = userPreferences.value?.compliance?.loans?.sections;

        if (sections) {
            setLoanItems(sections);
        } else {
            setLoanItems(AVAILABLE_LOANS_ITEMS);
        }
    };

    useEffect(setSortedItems, []);

    const value: ComplianceLoansContextModel = {
        loanItems,
        updateLoanItems,
        loansState,
        loadLoans,
        resetState,
    };

    return <ComplianceLoansContext.Provider value={value}>{children}</ComplianceLoansContext.Provider>;
};

export function useComplianceLoans() {
    const context = useContext(ComplianceLoansContext);
    if (!context) {
        throw new Error('useComplianceLoans must be used within ComplianceLoansProvider');
    }
    return context;
}
