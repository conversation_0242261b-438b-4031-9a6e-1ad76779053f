import {LoanDetailsModel} from '@/interfaces';
import {UnderwritingApplicationTapeModel} from '@/screens/UnderwritingScreen/interfaces';

export enum ConsumersSearchType {
    Loan = 'LOAN',
    Application = 'APPLICATION',
    Employee = 'EMPLOYEE',
}

export type ComplianceConsumerItemModel = {id: string} & (
    | {type: ConsumersSearchType.Loan; data: LoanDetailsModel}
    | {type: ConsumersSearchType.Application; data: UnderwritingApplicationTapeModel}
);

export enum ComplianceLoanAction {
    UNFLAG_LITIGATION = 'UNFLAG_FOR_LITIGATION',
    FLAG_LITIGATION = 'FLAG_FOR_LITIGATION',
    CLEAR_LITIGATION = 'CLEAR_FOR_LITIGATION',
}
