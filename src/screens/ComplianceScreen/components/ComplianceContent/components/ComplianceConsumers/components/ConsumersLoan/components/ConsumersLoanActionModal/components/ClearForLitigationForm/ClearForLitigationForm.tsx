import './styles.scss';

import React from 'react';
import {Grid2, TextField} from '@mui/material';
import { KasModalFooter} from '@/components';
import { useFormik } from 'formik';
import { ClearForLitigationValues, validationSchema} from './schema';
import { useComplianceConsumers } from '../../../../../../useComplianceConsumers';
import { ConsumersSearchType } from '../../../../../../interfaces';
import { useSnackbar } from '@/hooks/useSnackbar';
import { DEFAULT_ERROR_MSG } from '@/constants';

export const ClearForLitigationForm = () => {
    const {
        submittingAction,
        setOpenActionModal,
        onSubmitAction,
        activeItem,
        loadItem
    } = useComplianceConsumers();
    
    const {showMessage} = useSnackbar();

    const onSubmit = async (values: ClearForLitigationValues) => {
        const url = '/api/secured/compliance/consumers/loan/action/flag-litigation';
        const body = JSON.stringify({
            loan_id: activeItem!.id,
            comment: values.comment,
        });

        const response = await onSubmitAction(url, body, 'put');
        if (response.value) {
            loadItem(activeItem!.id, ConsumersSearchType.Loan, true);            
        } 
    };

    const formik = useFormik({
        validateOnMount: true,
        enableReinitialize: true,
        initialValues: {
            comment: '',
        },
        onSubmit,
        validationSchema,
    });

    return (
        <form className='kas-compliance-actions-clear-for-litigation-form' onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} rowSpacing={2}>
                <Grid2 size={12}>
                    <TextField
                        fullWidth
                        size='small'
                        name='comment'
                        disabled={submittingAction}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        label='Comment'
                        variant='outlined'
                        error={!!formik.errors.comment && formik.touched.comment}
                        helperText={formik.touched.comment && formik.errors.comment}
                    />
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        submitText='Save'
                        disabled={!formik.isValid || submittingAction}
                        onCancel={() => setOpenActionModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
     );
};
