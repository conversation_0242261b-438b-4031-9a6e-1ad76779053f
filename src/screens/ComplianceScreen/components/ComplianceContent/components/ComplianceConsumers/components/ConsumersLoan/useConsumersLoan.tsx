import React, {createContext, useContext, useEffect, useMemo, useState} from 'react';
import {LoanDetailsModel} from '@/interfaces';
import {ConsumersLoanItemModel, ConsumersLoanItemType} from './interfaces';
import {KasAddSectionMenuItem, KasDotsMenuItemProps} from '@/components';
import {useAppDispatch, useAppSelector} from '@/lib/hooks';
import {Typography, useTheme} from '@mui/material';
import {selectUserPreferences, updateUserPreferences} from '@/lib/slices/userPreferencesSlice';
import {CONSUMERS_LOAN_ITEMS_CONFIG_KEY, DEFAULT_CONSUMERS_LOAN_ITEMS} from './data';
import {uniqElements} from '@/utils/ArrayUtils';

interface ConsumersLoanContextModel {
    loan: LoanDetailsModel;
    addVisibleItem: (value: ConsumersLoanItemModel) => void;
    activeVisibleItems: ConsumersLoanItemModel[];
    activeHiddenItems: KasAddSectionMenuItem<ConsumersLoanItemModel>[];
    updateVisibleItems: (value: ConsumersLoanItemModel[]) => void;
    createDeleteMenuItem: (itemType: ConsumersLoanItemType) => KasDotsMenuItemProps;
    expandedItems: string[];
    updateExpandedItem: (id: string, value: boolean) => void;
    expandAllItems: () => void;
    collapseAllItems: () => void;
}

const ConsumersLoanContext = createContext<ConsumersLoanContextModel | undefined>(undefined);

interface ConsumersLoanProviderProps {
    children: React.ReactNode;
    loan: LoanDetailsModel;
}

export const ConsumersLoanProvider: React.FC<ConsumersLoanProviderProps> = ({children, loan}) => {
    const theme = useTheme();
    const dispatch = useAppDispatch();
    const userPreferences = useAppSelector(selectUserPreferences);
    const [visibleItems, setVisibleItems] = useState<ConsumersLoanItemModel[]>([]);

    const activeVisibleItems = useMemo(() => {
        return visibleItems.filter((item) => DEFAULT_CONSUMERS_LOAN_ITEMS.some(({id}) => item.id === id));
    }, [visibleItems]);

    const activeHiddenItems = useMemo(() => {
        const defaultItems = DEFAULT_CONSUMERS_LOAN_ITEMS.filter(
            (item) => !visibleItems.find(({id}) => id === item.id),
        );
        const hiddenItems = defaultItems.filter((item) => !visibleItems.find(({id}) => id === item.id));

        return hiddenItems.map((data) => ({title: data.title, data}));
    }, [visibleItems]);

    const addVisibleItem = (value: ConsumersLoanItemModel) => {
        const sections = [...visibleItems, value];

        updateVisibleItems(sections);
    };

    const createDeleteMenuItem = (itemType: ConsumersLoanItemType): KasDotsMenuItemProps => ({
        ContentComponent: (
            <Typography variant='body1' color={theme.palette.error.main}>
                Delete
            </Typography>
        ),
        onClick: () => {
            const newItems = visibleItems.filter((item) => item.type !== itemType);

            updateVisibleItems(newItems);
        },
    });

    const updateVisibleItems = (sections: ConsumersLoanItemModel[]) => {
        const consumers = userPreferences.value?.compliance?.consumers || {};
        setVisibleItems(sections);
        dispatch(updateUserPreferences({compliance: {consumers: {...consumers, loan: {sections}}}}));
    };

    const setSortedItems = () => {
        const sections = userPreferences.value?.compliance?.consumers?.loan?.sections;

        if (sections) {
            setVisibleItems(sections);
        } else {
            setVisibleItems(DEFAULT_CONSUMERS_LOAN_ITEMS);
        }
    };

    const [expandedItems, setExpandedItems] = useState<string[]>(() => {
        const storageValue = localStorage.getItem(CONSUMERS_LOAN_ITEMS_CONFIG_KEY);
        return storageValue ? storageValue.split(',') : [];
    });

    const updateExpandedItem = (id: string, value: boolean) => {
        setExpandedItems((prev) => {
            const newExpandedItems = value ? [...prev, id] : prev.filter((item) => item !== id);

            return uniqElements(newExpandedItems, (item) => item);
        });
    };

    const expandAllItems = () => {
        setExpandedItems(visibleItems.map(({id}) => id));
    };

    const collapseAllItems = () => {
        setExpandedItems([]);
    };

    const updateExpandedItemsConfig = () => {
        if (expandedItems.length > 0) {
            localStorage.setItem(CONSUMERS_LOAN_ITEMS_CONFIG_KEY, expandedItems.join(','));
        } else {
            localStorage.removeItem(CONSUMERS_LOAN_ITEMS_CONFIG_KEY);
        }
    };

    useEffect(updateExpandedItemsConfig, [expandedItems]);

    useEffect(setSortedItems, []);

    const value: ConsumersLoanContextModel = {
        loan,
        addVisibleItem,
        activeVisibleItems,
        activeHiddenItems,
        updateVisibleItems,
        createDeleteMenuItem,
        expandedItems,
        updateExpandedItem,
        expandAllItems,
        collapseAllItems,
    };

    return <ConsumersLoanContext.Provider value={value}>{children}</ConsumersLoanContext.Provider>;
};

export function useConsumersLoan() {
    const context = useContext(ConsumersLoanContext);
    if (!context) {
        throw new Error('useConsumersLoan must be used within ConsumersLoanProvider');
    }
    return context;
}
