import React from 'react';
import {Paper} from '@mui/material';
import {NoResultsView} from '@/views';
import {useComplianceConsumers} from '@/screens/ComplianceScreen/components/ComplianceContent/components';
import {ComplianceConsumerItemModel} from './../../interfaces';
import {ConsumerEmployeeResultsColumns} from './tables/ConsumerEmployeeResultsColumns';
import {ColumnDef} from '@tanstack/react-table';
import Box from '@mui/material/Box';
import {KasDesignedTable} from '@/components';

export const ConsumerEmployeeResults = () => {
    const {searchItems} = useComplianceConsumers();

    if (!searchItems?.length) {
        return (
            <NoResultsView
                text={
                    searchItems
                        ? 'There are no results based on this request'
                        : 'Search result will appear here'
                }
            />
        );
    }

    return (
        <Paper elevation={0}>
            <Box p={2}>
                <KasDesignedTable
                    columns={
                        ConsumerEmployeeResultsColumns as ColumnDef<ComplianceConsumerItemModel, unknown>[]
                    }
                    data={searchItems}
                />
            </Box>
        </Paper>
    );
};
