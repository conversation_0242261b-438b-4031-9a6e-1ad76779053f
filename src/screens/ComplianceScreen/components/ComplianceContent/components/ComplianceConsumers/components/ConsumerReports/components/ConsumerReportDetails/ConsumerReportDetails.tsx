import React, {useMemo} from 'react';
import {useConsumerReports} from './../../useConsumerReports';
import {
    BankReport,
    BankruptcyReport,
    CreditReport,
    DigitalIdentityReport,
    EmploymentReport,
    FraudReport,
    PayrollReport,
    RiskReport,
    SkiptraceReport,
} from './../../components';
import {KasLoadingBackDrop} from '@/components';
import {ConsumersReportItemType} from './../../interfaces';

export type ConsumersReportComponents = Record<ConsumersReportItemType, React.ComponentType>;

export const ConsumerReportDetails = () => {
    const {reportsState} = useConsumerReports();

    const reportComponents: ConsumersReportComponents = {
        [ConsumersReportItemType.Credit]: CreditReport,
        [ConsumersReportItemType.Employment]: EmploymentReport,
        [ConsumersReportItemType.Bank]: BankReport,
        [ConsumersReportItemType.Bankruptcy]: BankruptcyReport,
        [ConsumersReportItemType.Risk]: RiskReport,
        [ConsumersReportItemType.Fraud]: FraudReport,
        [ConsumersReportItemType.Skiptrace]: SkiptraceReport,
        [ConsumersReportItemType.Payroll]: PayrollReport,
        [ConsumersReportItemType.Digital_Identity]: DigitalIdentityReport,
    };

    const sortedReports = useMemo(() => {
        return Object.keys(reportComponents)
            .map((key) => ({
                key,
                Report: reportComponents[key as ConsumersReportItemType],
                hasData: !!reportsState[key as ConsumersReportItemType]?.data,
            }))
            .sort((a, b) => (b.hasData ? 1 : 0) - (a.hasData ? 1 : 0));
    }, [reportsState]);

    const loadingAll = useMemo(() => {
        return (
            Object.values(reportsState).every(({loading}) => loading) &&
            Object.values(reportsState).some(({data}) => !!data)
        );
    }, [reportsState]);

    return (
        <div className='kas-compliance-consumer-report-details' style={{position: 'relative'}}>
            {loadingAll && <KasLoadingBackDrop />}
            {sortedReports.map(({key, Report}) => (
                <Report key={key} />
            ))}
        </div>
    );
};
