import React, {createContext, useContext, useMemo, useState} from 'react';
import {
    UnderwritingVerificationHistoryModel,
    UnderwritingVerificationPersistentHistoryModel,
} from '@/screens/UnderwritingScreen/interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {Completable} from '@/interfaces';
import {DataStateInterface} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';

interface ApplicationVerificationsContextModel {
    verificationHistoryState: DataStateInterface<UnderwritingVerificationHistoryModel[]>;
    loadVerificationHistoryData: () => Promise<void>;
    persistentHistoryState: DataStateInterface<UnderwritingVerificationPersistentHistoryModel[]>;
    loadPersistentHistoryData: () => Promise<void>;
}

const ApplicationVerificationsContext = createContext<ApplicationVerificationsContextModel | undefined>(
    undefined,
);

interface ApplicationVerificationsProviderProps {
    children: React.ReactNode;
    id: number;
}

export const ApplicationVerificationsProvider = ({children, id}: ApplicationVerificationsProviderProps) => {
    const baseUrl = useMemo(() => `/api/secured/underwriting/applications/${id}`, [id]);
    const [persistentHistoryState, setPersistentHistoryState] =
        useState(getDefaultState<UnderwritingVerificationPersistentHistoryModel[]>());
    const [verificationHistoryState, setVerificationHistoryState] =
        useState(getDefaultState<UnderwritingVerificationHistoryModel[]>());

    const loadVerificationHistoryData = async () => {
        const url = `${baseUrl}/verification`;

        setVerificationHistoryState(getLoadingState(verificationHistoryState));

        const response: Completable<UnderwritingVerificationHistoryModel[]> = await apiRequest(url);

        setVerificationHistoryState(getLoadedState(response));
    };

    const loadPersistentHistoryData = async () => {
        const url = `${baseUrl}/persistent`;

        setPersistentHistoryState(getLoadingState(persistentHistoryState));

        const response: Completable<UnderwritingVerificationPersistentHistoryModel[]> = await apiRequest(url);

        setPersistentHistoryState(getLoadedState(response));
    };

    const value: ApplicationVerificationsContextModel = {
        verificationHistoryState,
        loadVerificationHistoryData,
        persistentHistoryState,
        loadPersistentHistoryData,
    };

    return (
        <ApplicationVerificationsContext.Provider value={value}>
            {children}
        </ApplicationVerificationsContext.Provider>
    );
};

export function useApplicationVerifications() {
    const context = useContext(ApplicationVerificationsContext);
    if (!context) {
        throw new Error('useApplicationVerifications must be used within ApplicationVerificationsProvider');
    }
    return context;
}
