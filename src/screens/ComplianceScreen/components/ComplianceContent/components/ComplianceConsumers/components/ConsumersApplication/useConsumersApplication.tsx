import React, {createContext, useContext, useEffect, useMemo, useState} from 'react';
import {UnderwritingApplicationTapeModel} from '@/screens/UnderwritingScreen/interfaces';
import {useAppDispatch, useAppSelector} from '@/lib/hooks';
import {selectUserPreferences, updateUserPreferences} from '@/lib/slices/userPreferencesSlice';
import {KasAddSectionMenuItem, KasDotsMenuItemProps} from '@/components';
import {Typography, useTheme} from '@mui/material';
import {uniqElements} from '@/utils/ArrayUtils';
import {CONSUMERS_APPLICATION_ITEMS_CONFIG_KEY, DEFAULT_CONSUMERS_APPLICATION_ITEMS} from './data';
import {ConsumersApplicationItemModel, ConsumersApplicationItemType} from './interfaces';

interface ConsumersApplicationContextModel {
    application: UnderwritingApplicationTapeModel;
    addVisibleItem: (value: ConsumersApplicationItemModel) => void;
    activeVisibleItems: ConsumersApplicationItemModel[];
    activeHiddenItems: KasAddSectionMenuItem<ConsumersApplicationItemModel>[];
    updateVisibleItems: (value: ConsumersApplicationItemModel[]) => void;
    createDeleteMenuItem: (itemType: ConsumersApplicationItemType) => KasDotsMenuItemProps;
    expandedItems: string[];
    updateExpandedItem: (id: string, value: boolean) => void;
    expandAllItems: () => void;
    collapseAllItems: () => void;
}

const ConsumersApplicationContext = createContext<ConsumersApplicationContextModel | undefined>(undefined);

interface ConsumersApplicationProviderProps {
    children: React.ReactNode;
    application: UnderwritingApplicationTapeModel;
}

export const ConsumersApplicationProvider = ({children, application}: ConsumersApplicationProviderProps) => {
    const theme = useTheme();
    const dispatch = useAppDispatch();
    const userPreferences = useAppSelector(selectUserPreferences);
    const [visibleItems, setVisibleItems] = useState<ConsumersApplicationItemModel[]>([]);

    const activeVisibleItems = useMemo(() => {
        return visibleItems.filter((item) =>
            DEFAULT_CONSUMERS_APPLICATION_ITEMS.some(({id}) => item.id === id),
        );
    }, [visibleItems]);

    const activeHiddenItems = useMemo(() => {
        const defaultItems = DEFAULT_CONSUMERS_APPLICATION_ITEMS.filter(
            (item) => !visibleItems.find(({id}) => id === item.id),
        );
        const hiddenItems = defaultItems.filter((item) => !visibleItems.find(({id}) => id === item.id));

        return hiddenItems.map((data) => ({title: data.title, data}));
    }, [visibleItems]);

    const addVisibleItem = (value: ConsumersApplicationItemModel) => {
        const sections = [...visibleItems, value];

        updateVisibleItems(sections);
    };

    const createDeleteMenuItem = (itemType: ConsumersApplicationItemType): KasDotsMenuItemProps => ({
        ContentComponent: (
            <Typography variant='body1' color={theme.palette.error.main}>
                Delete
            </Typography>
        ),
        onClick: () => {
            const newItems = visibleItems.filter((item) => item.type !== itemType);

            updateVisibleItems(newItems);
        },
    });

    const updateVisibleItems = (sections: ConsumersApplicationItemModel[]) => {
        const consumers = userPreferences.value?.compliance?.consumers || {};
        setVisibleItems(sections);
        dispatch(updateUserPreferences({compliance: {consumers: {...consumers, application: {sections}}}}));
    };

    const setSortedProfileItems = () => {
        const sections = userPreferences.value?.compliance?.consumers?.application?.sections;

        if (sections) {
            setVisibleItems(sections);
        } else {
            setVisibleItems(DEFAULT_CONSUMERS_APPLICATION_ITEMS);
        }
    };

    const [expandedItems, setExpandedItems] = useState<string[]>(() => {
        const storageValue = localStorage.getItem(CONSUMERS_APPLICATION_ITEMS_CONFIG_KEY);
        return storageValue ? storageValue.split(',') : [];
    });

    const updateExpandedItem = (id: string, value: boolean) => {
        setExpandedItems((prev) => {
            const newExpandedItems = value ? [...prev, id] : prev.filter((item) => item !== id);

            return uniqElements(newExpandedItems, (item) => item);
        });
    };

    const expandAllItems = () => {
        setExpandedItems(visibleItems.map(({id}) => id));
    };

    const collapseAllItems = () => {
        setExpandedItems([]);
    };

    const updateExpandedItemsConfig = () => {
        if (expandedItems.length > 0) {
            localStorage.setItem(CONSUMERS_APPLICATION_ITEMS_CONFIG_KEY, expandedItems.join(','));
        } else {
            localStorage.removeItem(CONSUMERS_APPLICATION_ITEMS_CONFIG_KEY);
        }
    };

    useEffect(updateExpandedItemsConfig, [expandedItems]);

    useEffect(setSortedProfileItems, []);

    const value: ConsumersApplicationContextModel = {
        application,
        addVisibleItem,
        activeVisibleItems,
        activeHiddenItems,
        updateVisibleItems,
        createDeleteMenuItem,
        expandedItems,
        updateExpandedItem,
        expandAllItems,
        collapseAllItems,
    };

    return (
        <ConsumersApplicationContext.Provider value={value}>{children}</ConsumersApplicationContext.Provider>
    );
};

export function useConsumersApplication() {
    const context = useContext(ConsumersApplicationContext);
    if (!context) {
        throw new Error('useConsumersApplication must be used within ConsumersApplicationProvider');
    }
    return context;
}
