import {ConsumersApplicationItemModel, ConsumersApplicationItemType} from './interfaces';

export const CONSUMERS_APPLICATION_ITEMS_CONFIG_KEY = 'consumers-application-items-config';

export const AVAILABLE_CONSUMERS_APPLICATION_ITEMS: ConsumersApplicationItemModel[] = [
    {
        id: '1',
        title: 'Leadgen Info',
        type: ConsumersApplicationItemType.Leadgen_Data,
    },
    {
        id: '2',
        title: 'Employee Data',
        type: ConsumersApplicationItemType.Employee_Data,
    },
    {
        id: '3',
        title: 'Loan Terms',
        type: ConsumersApplicationItemType.LoanTerms,
    },
    {
        id: '4',
        title: 'Credit Data',
        type: ConsumersApplicationItemType.Credit_Data,
    },
    {
        id: '5',
        title: 'Tenure Data',
        type: ConsumersApplicationItemType.Tenure_Data,
    },
    {
        id: '6',
        title: 'Loan History',
        type: ConsumersApplicationItemType.Loan_History,
    },
    {
        id: '7',
        title: 'Reports',
        type: ConsumersApplicationItemType.Reports,
    },
    {
        id: '8',
        title: 'Verifications',
        type: ConsumersApplicationItemType.Verifications,
    },
    {
        id: '9',
        title: 'Documents',
        type: ConsumersApplicationItemType.Documents,
    },
    {
        id: '10',
        title: 'Notifications',
        type: ConsumersApplicationItemType.Notifications,
    },
];

export const DEFAULT_CONSUMERS_APPLICATION_ITEMS = AVAILABLE_CONSUMERS_APPLICATION_ITEMS.filter(({type}) =>
    [
        ConsumersApplicationItemType.Employee_Data,
        ConsumersApplicationItemType.LoanTerms,
        ConsumersApplicationItemType.Credit_Data,
        ConsumersApplicationItemType.Leadgen_Data,
        ConsumersApplicationItemType.Tenure_Data,
        ConsumersApplicationItemType.Loan_History,
        ConsumersApplicationItemType.Reports,
        ConsumersApplicationItemType.Verifications,
        ConsumersApplicationItemType.Documents,
        ConsumersApplicationItemType.Notifications,
    ].includes(type),
);
