import {ConsumersLoanItemModel, ConsumersLoanItemType} from './interfaces';

export const CONSUMERS_LOAN_ITEMS_CONFIG_KEY = 'consumers-loan-items-config';

export const AVAILABLE_CONSUMERS_LOAN_ITEMS: ConsumersLoanItemModel[] = [
    {
        id: '1',
        title: 'Personal Details',
        type: ConsumersLoanItemType.Personal_Details,
    },
    {
        id: '2',
        title: 'Credit Reports',
        type: ConsumersLoanItemType.Credit_Reports,
    },
    {
        id: '3',
        title: 'Transaction History',
        type: ConsumersLoanItemType.Transaction_History,
    },
    {
        id: '4',
        title: 'Document History',
        type: ConsumersLoanItemType.Document_History,
    },
    {
        id: '5',
        title: 'Reports',
        type: ConsumersLoanItemType.Reports,
    },
    {
        id: '6',
        title: 'AML Report',
        type: ConsumersLoanItemType.AML_Report,
    },
];

export const DEFAULT_CONSUMERS_LOAN_ITEMS = AVAILABLE_CONSUMERS_LOAN_ITEMS.filter(({type}) =>
    [
        ConsumersLoanItemType.Personal_Details,
        ConsumersLoanItemType.Credit_Reports,
        ConsumersLoanItemType.Transaction_History,
        ConsumersLoanItemType.Document_History,
        ConsumersLoanItemType.Reports,
        ConsumersLoanItemType.AML_Report,
    ].includes(type),
);
