import React, {useEffect} from 'react';
import {ApplicationVerificationHistory} from './components';
import {ConsumersApplicationItem} from './../../components';
import {useApplicationVerifications} from './useApplicationVerifications';
import {ConsumersApplicationItemModel} from './../../../../interfaces';

export const ApplicationVerifications = ({item}: {item: ConsumersApplicationItemModel}) => {
    const {
        verificationHistoryState,
        loadVerificationHistoryData,
        persistentHistoryState,
        loadPersistentHistoryData,
    } = useApplicationVerifications();

    const onRefreshHandler = () => {
        if (verificationHistoryState.data || verificationHistoryState.error) {
            loadVerificationHistoryData().then();
        }
        if (persistentHistoryState.data || persistentHistoryState.error) {
            loadPersistentHistoryData().then();
        }
    };

    useEffect(() => {
        loadVerificationHistoryData().then();
        loadPersistentHistoryData().then();
    }, []);

    return (
        <ConsumersApplicationItem
            onRefresh={onRefreshHandler}
            item={item}
            loading={verificationHistoryState.loading || persistentHistoryState.loading}
            DetailsComponent={<ApplicationVerificationHistory />}
        />
    );
};
