import React from 'react';
import {Button, Grid2, Paper, Stack, Typography} from '@mui/material';
import {useConsumersApplication} from '../../useConsumersApplication';
import {ApplicationGeneralInfo} from '@/views/application';
import {KasAddSectionMenu} from '@/components';
import {DeleteOutline, UnfoldLess, UnfoldMore} from '@mui/icons-material';

export const ConsumersApplicationInfoCard = () => {
    const {
        application,
        addVisibleItem,
        activeHiddenItems,
        expandedItems,
        collapseAllItems,
        expandAllItems,
        activeVisibleItems,
        updateVisibleItems,
    } = useConsumersApplication();

    const onRemoveAll = () => {
        updateVisibleItems([]);
    };

    return (
        <Paper elevation={0}>
            <Grid2 container rowSpacing={3} px={5} py={2}>
                <Grid2 size={12}>
                    <Stack flexDirection='row' alignItems='center' justifyContent='space-between' rowGap={2}>
                        <Typography variant='h3'>Application #{application.applicationId}</Typography>
                        <Stack direction='row' spacing={1}>
                            <Button
                                variant='outlined'
                                color='error'
                                size='small'
                                title='Remove All Sections'
                                disabled={!activeVisibleItems.length}
                                onClick={onRemoveAll}>
                                <DeleteOutline fontSize='small' />
                            </Button>
                            <Button
                                variant='outlined'
                                size='small'
                                title='Hide All Sections'
                                disabled={!expandedItems.length}
                                onClick={collapseAllItems}>
                                <UnfoldLess fontSize='small' />
                            </Button>
                            <Button
                                variant='outlined'
                                size='small'
                                title='Show All Sections'
                                disabled={expandedItems.length === activeVisibleItems.length}
                                onClick={expandAllItems}>
                                <UnfoldMore fontSize='small' />
                            </Button>
                            <KasAddSectionMenu items={activeHiddenItems} onAddItem={addVisibleItem} />
                        </Stack>
                    </Stack>
                </Grid2>
                <Grid2 size={12}>
                    <ApplicationGeneralInfo data={application} />
                </Grid2>
            </Grid2>
        </Paper>
    );
};
