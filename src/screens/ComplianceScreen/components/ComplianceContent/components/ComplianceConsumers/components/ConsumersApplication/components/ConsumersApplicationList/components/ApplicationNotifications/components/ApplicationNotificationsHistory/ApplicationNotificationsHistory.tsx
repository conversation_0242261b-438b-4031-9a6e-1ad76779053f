import React from 'react';
import {TableView} from '@/views';
import {ColumnDef} from '@tanstack/react-table';
import {useApplicationNotifications} from '../../useApplicationNotifications';
import {NotificationsTableColumns} from './tables';
import {NotificationModel} from '@/interfaces';

export const ApplicationNotificationsHistory = () => {
    const {notificationsState, loadNotificationsData} = useApplicationNotifications();

    return (
        <TableView<NotificationModel>
            withTableActions
            loading={notificationsState.loading}
            error={notificationsState.error}
            data={notificationsState.data}
            columns={NotificationsTableColumns as ColumnDef<NotificationModel, unknown>[]}
            onRetry={loadNotificationsData}
            tableName='Consumer Application Notifications'
        />
    );
};
