import React, {PropsWithChildren, useEffect} from 'react';
import {Grid2, Typography} from '@mui/material';
import {KasLoadingError, Kas<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, KasS<PERSON>, KasSwitchWhen} from '@/components';
import {useConsumerReports} from './../../useConsumerReports';
import {ConsumersReportItemType} from './../../interfaces';
import {LoadingReport} from './components';

interface ConsumerReportProps extends PropsWithChildren {
    title?: string;
    type: ConsumersReportItemType;
}

export const ConsumerReport = ({title, type, children}: ConsumerReportProps) => {
    const {reportsState, loadReport} = useConsumerReports();
    const {data, loading, error} = reportsState[type];

    useEffect(() => {
        if (!data && !loading) {
            loadReport(type).then();
        }
    }, []);

    return (
        <Grid2 container mb={2} spacing={1}>
            {title && (
                <Grid2 size={12}>
                    <Typography variant='subtitle1'>{title}</Typography>
                </Grid2>
            )}
            <Grid2 size={12}>
                <KasSwitch>
                    <KasSwitchWhen condition={!data && loading}>
                        <LoadingReport />
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={!!error}>
                        <KasLoadingError view='contained' error={error} onTryAgain={() => loadReport(type)} />
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={!data}>
                        <KasNoResults text='No records found' p={2} bgcolor='var(--color-grey)' />
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={!!data}>{children}</KasSwitchWhen>
                </KasSwitch>
            </Grid2>
        </Grid2>
    );
};
