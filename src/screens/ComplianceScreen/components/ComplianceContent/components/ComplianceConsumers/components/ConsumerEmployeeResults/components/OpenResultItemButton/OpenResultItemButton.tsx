import React from 'react';
import {CallMade} from '@mui/icons-material';
import {ActionCell} from '@/components/table/cells';
import {ComplianceConsumerItemModel} from './../../../../interfaces';
import {useComplianceConsumers} from '@/screens/ComplianceScreen/components/ComplianceContent/components';

export const OpenResultItemButton = ({data}: {data: ComplianceConsumerItemModel}) => {
    const {loadItem} = useComplianceConsumers();
    const onClickHandler = async () => {
        await loadItem(data.id, data.type);
    };

    return (
        <ActionCell Icon={<CallMade color='primary' titleAccess='Show Details' />} onClick={onClickHandler} />
    );
};
