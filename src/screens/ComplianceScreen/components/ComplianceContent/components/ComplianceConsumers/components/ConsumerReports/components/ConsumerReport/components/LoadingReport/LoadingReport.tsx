import React from 'react';
import {Grid2, Paper, Skeleton, Stack} from '@mui/material';

export const LoadingReport = () => {
    return (
        <Paper elevation={0}>
            <Stack direction='row' spacing={2} p={1.5}>
                <Grid2 container mb={1} spacing={1}>
                    <Grid2 size={12}>
                        <Skeleton variant='rounded' animation='wave' width={70} height={18} />
                    </Grid2>
                    <Grid2 size={12}>
                        <Skeleton variant='rounded' animation='wave' width={120} height={20} />
                    </Grid2>
                </Grid2>
                <Grid2 container mb={1} spacing={1}>
                    <Grid2 size={12}>
                        <Skeleton variant='rounded' animation='wave' width={70} height={18} />
                    </Grid2>
                    <Grid2 size={12}>
                        <Skeleton variant='rounded' animation='wave' width={120} height={20} />
                    </Grid2>
                </Grid2>
                <Grid2 container mb={1} spacing={1}>
                    <Grid2 size={12}>
                        <Skeleton variant='rounded' animation='wave' width={70} height={18} />
                    </Grid2>
                    <Grid2 size={12}>
                        <Skeleton variant='rounded' animation='wave' width={120} height={20} />
                    </Grid2>
                </Grid2>
            </Stack>
        </Paper>
    );
};
