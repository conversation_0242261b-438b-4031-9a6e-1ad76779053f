import React from 'react';
import {KasFlaggedIcon, KasInfo} from '@/components';
import {useConsumerReports} from './../../useConsumerReports';
import {ConsumersReportItemType} from './../../interfaces';
import {ConsumerReport, ConsumerReportCard} from './../../components';

export const FraudReport = () => {
    const {reportsState} = useConsumerReports();
    const {data} = reportsState[ConsumersReportItemType.Fraud];

    return (
        <ConsumerReport title='Fraud Report' type={ConsumersReportItemType.Fraud}>
            <ConsumerReportCard>
                <KasInfo label='ID'>{data?.gid}</KasInfo>
                <KasInfo label='Provider'>{data?.reporting_agency}</KasInfo>
                <KasInfo label='Date'>{data?.report_date}</KasInfo>
                <KasInfo label='Override'>
                    <KasFlaggedIcon flagged={!!data?.override} />
                </KasInfo>
                <KasInfo label='Override By'>{data?.override_user_name}</KasInfo>
                <KasInfo label='Comment'>{data?.comments ? data.comments.join(', ') : null}</KasInfo>
            </ConsumerReportCard>
        </ConsumerReport>
    );
};
