import React, {createContext, useContext, useMemo, useState} from 'react';
import {apiRequest} from '@/utils/AxiosUtils';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {ReportsStateType, ConsumersReportItemType, ConsumersReportPath} from './interfaces';
import {ConsumersReportType} from '@/app/api/secured/compliance/consumers/report/interfaces';

interface ConsumerReportsContextModel {
    reportsState: ReportsStateType;
    loadReport: (report: ConsumersReportItemType) => Promise<void>;
    loadAllReports: () => void;
}

const ConsumerReportsContext = createContext<ConsumerReportsContextModel | undefined>(undefined);

const DEFAULT_REPORTS_STATE: ReportsStateType = Object.entries(ConsumersReportItemType).reduce(
    (acc, [_, type]) => ({
        ...acc,
        [type]: getDefaultState(),
    }),
    {} as ReportsStateType,
);

const REPORT_PATH: ConsumersReportPath = {
    [ConsumersReportItemType.Credit]: 'credit',
    [ConsumersReportItemType.Risk]: 'risk',
    [ConsumersReportItemType.Fraud]: 'fraud',
    [ConsumersReportItemType.Employment]: 'employment',
    [ConsumersReportItemType.Bankruptcy]: 'bankruptcy',
    [ConsumersReportItemType.Skiptrace]: 'skiptrace',
    [ConsumersReportItemType.Digital_Identity]: 'bank/verification',
    [ConsumersReportItemType.Bank]: 'bank',
    [ConsumersReportItemType.Payroll]: 'payroll',
};

interface ConsumerReportsProviderProps {
    children: React.ReactNode;
    id: number;
    type: ConsumersReportType;
}

export const ConsumerReportsProvider = ({children, id, type}: ConsumerReportsProviderProps) => {
    const baseUrl = useMemo(() => `/api/secured/compliance/consumers/report/${type}/${id}`, [id, type]);
    const [reportsState, setReportsState] = useState(DEFAULT_REPORTS_STATE);

    const loadReport = async (report: ConsumersReportItemType) => {
        const url = `${baseUrl}/${REPORT_PATH[report]}`;

        setReportsState((prevState) => ({
            ...prevState,
            [report]: getLoadingState(prevState[report]),
        }));

        const response = await apiRequest(url);

        setReportsState((prevState) => ({
            ...prevState,
            [report]: getLoadedState(response),
        }));
    };

    const loadAllReports = () => {
        const reportTypes = Object.values(ConsumersReportItemType);

        for (const report of reportTypes) {
            loadReport(report).then();
        }
    };

    const value: ConsumerReportsContextModel = {
        reportsState,
        loadReport,
        loadAllReports,
    };

    return <ConsumerReportsContext.Provider value={value}>{children}</ConsumerReportsContext.Provider>;
};

export function useConsumerReports() {
    const context = useContext(ConsumerReportsContext);
    if (!context) {
        throw new Error('useConsumerReports must be used within ConsumerReportsProvider');
    }
    return context;
}
