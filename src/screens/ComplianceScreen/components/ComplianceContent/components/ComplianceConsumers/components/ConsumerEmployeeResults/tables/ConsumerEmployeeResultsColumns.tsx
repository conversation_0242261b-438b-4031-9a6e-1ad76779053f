import {CellContext, createColumnHelper} from '@tanstack/react-table';
import {ComplianceConsumerItemModel, ConsumersSearchType} from './../../../interfaces';
import {OpenResultItemButton} from './../components';

const columnHelper = createColumnHelper<ComplianceConsumerItemModel>();

export const ConsumerEmployeeResultsColumns = [
    columnHelper.accessor('type', {
        id: 'type',
        header: 'Type',
        cell: (props: CellContext<ComplianceConsumerItemModel, string>) =>
            props.getValue() === ConsumersSearchType.Loan ? 'Loan' : 'Application',
    }),
    columnHelper.accessor('id', {
        id: 'id',
        header: 'ID',
        cell: (props: CellContext<ComplianceConsumerItemModel, string>) => props.getValue(),
    }),
    {
        id: 'data',
        header: 'Action',
        cell: (props: CellContext<ComplianceConsumerItemModel, number>) => (
            <OpenResultItemButton data={props.row.original} />
        ),
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
];
