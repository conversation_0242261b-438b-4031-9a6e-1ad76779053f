import React from 'react';
import {useConsumerReports} from './../../useConsumerReports';
import {ConsumersReportItemType} from './../../interfaces';
import {ConsumerReport} from './../../components';
import {Stack} from '@mui/material';
import {PayrollReportView} from '@/views';

export const PayrollReport = () => {
    const {reportsState} = useConsumerReports();
    const {data} = reportsState[ConsumersReportItemType.Payroll];

    return (
        <ConsumerReport title='Payroll Report' type={ConsumersReportItemType.Payroll}>
            <Stack spacing={1}>
                {data?.map((report, i) => <PayrollReportView key={i} report={report} />)}
            </Stack>
        </ConsumerReport>
    );
};
