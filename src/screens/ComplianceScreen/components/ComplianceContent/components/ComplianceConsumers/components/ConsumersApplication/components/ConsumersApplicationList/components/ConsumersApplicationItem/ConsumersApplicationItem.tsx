import './styles.scss';

import React, {ReactNode, useEffect, useMemo, useState} from 'react';
import {Accordion, AccordionDetails, AccordionSummary, Grid2, Stack} from '@mui/material';
import {
    KasDotsMenu,
    KasDotsMenuItemProps,
    KasDragTitle,
    KasErrorBoundary,
    KasExpandIcon,
    KasLoadingStatusIcon,
} from '@/components';
import {useSortableList} from '@/views';
import Box from '@mui/material/Box';
import {ConsumersApplicationItemModel} from '../../../../interfaces';
import {useConsumersApplication} from '../../../../useConsumersApplication';
import IconButton from '@mui/material/IconButton';
import {Refresh} from '@mui/icons-material';

interface ConsumersApplicationItemProps {
    item: ConsumersApplicationItemModel;
    loading?: boolean;
    PreviewComponent?: ReactNode;
    DetailsComponent: ReactNode;
    menuItems?: KasDotsMenuItemProps[];
    onRefresh?: () => void;
}

export const ConsumersApplicationItem = ({
    item,
    loading = false,
    PreviewComponent,
    DetailsComponent,
    menuItems = [],
    onRefresh,
}: ConsumersApplicationItemProps) => {
    const {expandedItems, updateExpandedItem, createDeleteMenuItem} = useConsumersApplication();
    const [isAccordionOpen, setIsAccordionOpen] = useState(false);
    const {attributes, listeners, setActivatorNodeRef} = useSortableList();

    const expandedItem = useMemo(() => {
        return expandedItems.includes(item.id);
    }, [expandedItems, item]);

    const handleAccordionChange = () => {
        const value = !isAccordionOpen;

        setIsAccordionOpen(value);
        updateExpandedItem(item.id, value);
    };

    useEffect(() => {
        setIsAccordionOpen(expandedItem);
    }, [expandedItem]);

    return (
        <div className='kas-consumers-application-item'>
            <Accordion expanded={isAccordionOpen} disableGutters elevation={0}>
                <AccordionSummary
                    component='div'
                    expandIcon={<KasExpandIcon expanded={true} onClick={handleAccordionChange} />}
                    aria-controls={item.id}
                    id={item.id}
                    onDoubleClick={handleAccordionChange}>
                    <Box sx={{flexGrow: 1}}>
                        <KasErrorBoundary>
                            <Grid2 container alignItems='center'>
                                <Grid2 size={2}>
                                    <KasDragTitle
                                        attributes={attributes}
                                        listeners={listeners}
                                        setActivatorNodeRef={setActivatorNodeRef}>
                                        {item.title}
                                    </KasDragTitle>
                                </Grid2>
                                <Grid2 size={9}>
                                    <div
                                        className={`kas-consumers-application-item__preview ${isAccordionOpen ? 'hidden' : ''}`}>
                                        {PreviewComponent}
                                    </div>
                                </Grid2>
                                <Grid2 size={1}>
                                    <Stack direction='row' justifyContent='flex-end'>
                                        <KasLoadingStatusIcon loading={loading} loadingError={undefined} />
                                        {!loading && onRefresh && (
                                            <IconButton title={'Refresh'} onClick={onRefresh}>
                                                <Refresh />
                                            </IconButton>
                                        )}
                                        <KasDotsMenu
                                            menuItems={[...menuItems, createDeleteMenuItem(item.type)]}
                                        />
                                    </Stack>
                                </Grid2>
                            </Grid2>
                        </KasErrorBoundary>
                    </Box>
                </AccordionSummary>
                <AccordionDetails>
                    <Box px={5} pb={3}>
                        <KasErrorBoundary>{DetailsComponent}</KasErrorBoundary>
                    </Box>
                </AccordionDetails>
            </Accordion>
        </div>
    );
};
