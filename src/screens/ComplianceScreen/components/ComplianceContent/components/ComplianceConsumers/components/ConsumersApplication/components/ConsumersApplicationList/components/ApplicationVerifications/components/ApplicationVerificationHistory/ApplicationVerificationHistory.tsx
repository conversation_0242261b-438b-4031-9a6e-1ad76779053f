import React from 'react';
import {KasLoading<PERSON><PERSON><PERSON>, KasSwitch, KasSwitchWhen} from '@/components';
import {UnderwritingVerificationHistoryModel} from '@/screens/UnderwritingScreen/interfaces';
import {PersistentInfo, PersistentInfoLoading} from './components';
import {TableView} from '@/views';
import {ColumnDef} from '@tanstack/react-table';
import {useApplicationVerifications} from './../../useApplicationVerifications';
import {VerificationHistoryTableColumns} from './tables';
import Box from '@mui/material/Box';

export const ApplicationVerificationHistory = () => {
    const {
        loadVerificationHistoryData,
        verificationHistoryState,
        loadPersistentHistoryData,
        persistentHistoryState,
    } = useApplicationVerifications();

    return (
        <>
            <KasSwitch>
                <KasSwitchWhen condition={persistentHistoryState.loading}>
                    <PersistentInfoLoading />
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!persistentHistoryState.error}>
                    <Box mb={2}>
                        <KasLoadingError
                            error={persistentHistoryState.error}
                            onTryAgain={loadPersistentHistoryData}
                        />
                    </Box>
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!persistentHistoryState.data}>
                    <PersistentInfo persistent={persistentHistoryState.data || []} />
                </KasSwitchWhen>
            </KasSwitch>
            <TableView<UnderwritingVerificationHistoryModel>
                loading={verificationHistoryState.loading}
                error={verificationHistoryState.error}
                data={verificationHistoryState.data}
                columns={
                    VerificationHistoryTableColumns as ColumnDef<
                        UnderwritingVerificationHistoryModel,
                        unknown
                    >[]
                }
                onRetry={loadVerificationHistoryData}
                tableName='Consumer Application Verifications'
            />
        </>
    );
};
