import React, {useState} from 'react';
import {InfoOutlined} from '@mui/icons-material';
import {ActionCell} from '@/components/table/cells';
import {KasModal} from '@/components';
import {DocumentMetadataView} from '@/views';
import {MetadataModel} from '@/interfaces';

interface DocumentMetadataProps {
    gid: number;
    metadata: MetadataModel;
}

export const DocumentMetadataCell = ({gid, metadata}: DocumentMetadataProps) => {
    const [openModal, setOpenModal] = useState(false);

    if (!metadata.exif_data) {
        return null;
    }

    return (
        <>
            <ActionCell
                Icon={<InfoOutlined color='primary' titleAccess='See Metadata' />}
                onClick={() => setOpenModal(true)}
            />
            <KasModal title={`Document: #${gid}`} open={openModal} onClose={() => setOpenModal(false)}>
                <DocumentMetadataView metadata={metadata} />
            </KasModal>
        </>
    );
};
