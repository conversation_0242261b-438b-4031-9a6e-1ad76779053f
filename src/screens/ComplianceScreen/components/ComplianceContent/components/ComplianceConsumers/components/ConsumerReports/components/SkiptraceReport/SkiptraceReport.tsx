import React from 'react';
import {KasInfo} from '@/components';
import {useConsumerReports} from './../../useConsumerReports';
import {ConsumersReportItemType} from './../../interfaces';
import {ConsumerReport, ConsumerReportCard} from './../../components';

export const SkiptraceReport = () => {
    const {reportsState} = useConsumerReports();
    const {data} = reportsState[ConsumersReportItemType.Skiptrace];

    return (
        <ConsumerReport title='Skiptrace Report' type={ConsumersReportItemType.Skiptrace}>
            <ConsumerReportCard>
                <KasInfo label='ID'>{data?.gid}</KasInfo>
                <KasInfo label='Agency'>{data?.agency}</KasInfo>
                <KasInfo label='Type'>{data?.product}</KasInfo>
                <KasInfo label='Search'>{data?.search_params}</KasInfo>
                <KasInfo label='Report Date'>{data?.report_date}</KasInfo>
            </ConsumerReportCard>
        </ConsumerReport>
    );
};
