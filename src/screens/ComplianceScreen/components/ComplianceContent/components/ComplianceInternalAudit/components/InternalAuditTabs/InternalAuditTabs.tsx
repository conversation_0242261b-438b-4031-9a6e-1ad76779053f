import React, {useRef, useState} from 'react';
import {Chip, Stack} from '@mui/material';
import {useWindowEventObserver} from '@/hooks/useWindowEventObserver';
import {KasStickyHeadBox} from '@/components';
import {useComplianceInternalAudit} from './../../useComplianceInternalAudit';
import {InternalAuditTab} from './../../interfaces';

export const InternalAuditTabs = () => {
    const {activeTab, changeActiveTab} = useComplianceInternalAudit();
    const [isSticky, setIsSticky] = useState(false);
    const stickyRef = useRef<HTMLDivElement | null>(null);

    useWindowEventObserver('scroll', stickyRef, (value: number) => {
        setIsSticky(value <= 0);
    });

    return (
        <KasStickyHeadBox py={1} ref={stickyRef} isSticky={isSticky}>
            <Stack direction='row' spacing={1} useFlexGap flexWrap='wrap'>
                {Object.entries(InternalAuditTab).map(([key, value]) => (
                    <Chip
                        key={key}
                        onClick={() => changeActiveTab(value)}
                        label={value}
                        variant='outlined'
                        color={activeTab === value ? 'info' : 'default'}
                    />
                ))}
            </Stack>
        </KasStickyHeadBox>
    );
};
