import {SelectModel} from '@/interfaces';

export const LANG_OPTIONS: SelectModel<string>[] = [
    {
        id: 'EN',
        value: 'EN',
        label: 'English',
    },
    {
        id: 'ES',
        value: 'ES',
        label: 'Spanish',
    },
];

export const ANY_OPTION: SelectModel<string> = {
    id: 'ANY',
    value: 'Any',
    label: 'Any',
};

export const CUSTOMER_TYPE_LABELS: Record<string, string> = {
    fedgov: 'Federal Government',
    retiree: 'Retiree',
    deposit: 'Deposit',
};
