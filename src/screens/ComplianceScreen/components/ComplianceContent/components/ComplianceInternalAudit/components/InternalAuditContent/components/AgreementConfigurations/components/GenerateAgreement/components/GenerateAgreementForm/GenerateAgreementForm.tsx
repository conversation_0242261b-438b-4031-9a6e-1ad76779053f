import React, {useEffect, useMemo, useState} from 'react';
import {Button, Grid2} from '@mui/material';
import {GenerateAgreementFormValues, validationSchema} from './schema';
import {useFormik} from 'formik';
import {KasAutocompleteField} from '@/components';
import {SelectModel} from '@/interfaces';
import {uniqElements} from '@/utils/ArrayUtils';
import {LOS_OPTIONS, STATES} from '@/components/static-selects/data';
import {ANY_OPTION, CUSTOMER_TYPE_LABELS, LANG_OPTIONS} from './data';
import {useGenerateAgreement} from './../../useGenerateAgreement';

export const GenerateAgreementForm = () => {
    const {generateAgreement, templateData, createSelectOption, agreementTypeOptions} =
        useGenerateAgreement();
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async (values: GenerateAgreementFormValues) => {
        setSubmitting(true);
        const los = values.los === 'Any' ? null : values.los;
        const state = values.state === 'Any' ? null : values.state;
        const params = {
            agreementType: values.agreementType,
            lang: values.lang,
            ...(los && {los}),
            ...(state && {state}),
            ...(values.customerType && values.customerType !== 'Any' && {[values.customerType]: 'true'}),
        };

        await generateAgreement(params);
        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            agreementType: '',
            los: '',
            state: '',
            lang: '',
            customerType: '',
        },
        onSubmit,
        validationSchema,
    });

    const availableLosOptions = useMemo(() => {
        const filteredData = templateData.filter(
            (item) => `${item.variant}-${item.type}` === formik.values.agreementType,
        );
        const uniqueLosValues = uniqElements(filteredData, (item) => item.los);

        return uniqueLosValues.map((los) => createSelectOption(los, LOS_OPTIONS));
    }, [templateData, formik.values.agreementType]);

    const availableStateOptions = useMemo(() => {
        const filteredData = templateData.filter(
            (item) =>
                `${item.variant}-${item.type}` === formik.values.agreementType &&
                item.los === formik.values.los,
        );
        const uniqueStateValues = uniqElements(filteredData, (item) => item.state);

        return uniqueStateValues.map((state) => {
            if (state === null) return ANY_OPTION;

            const defaultState = STATES.find(({id}) => id === state);
            return defaultState
                ? {
                      id: defaultState.id,
                      value: defaultState.id,
                      label: defaultState.text,
                  }
                : {
                      id: state,
                      value: state,
                      label: state,
                  };
        });
    }, [templateData, formik.values.agreementType, formik.values.los]);

    const availableLangOptions = useMemo(() => {
        const filteredData = templateData.filter(
            (item) =>
                `${item.variant}-${item.type}` === formik.values.agreementType &&
                item.los === formik.values.los &&
                item.state === formik.values.state,
        );
        const uniqueLangValues = uniqElements(filteredData, (item) => item.lang_cd || 'EN');

        return uniqueLangValues.map((lang) => createSelectOption(lang, LANG_OPTIONS));
    }, [templateData, formik.values.agreementType, formik.values.los, formik.values.state]);

    const customerTypeOptions = useMemo(() => {
        const filteredData = templateData.filter(
            (item) =>
                `${item.variant}-${item.type}` === formik.values.agreementType &&
                item.los === formik.values.los &&
                item.state === formik.values.state &&
                item.lang_cd === formik.values.lang,
        );

        const customerTypes = new Set<string>();
        let hasAnyType = false;

        filteredData.forEach((item) => {
            const {fedgov, retiree, deposit} = item;

            if (fedgov === true) customerTypes.add('fedgov');
            if (retiree === true) customerTypes.add('retiree');
            if (deposit === true) customerTypes.add('deposit');

            if (fedgov === null && retiree === null && deposit === null) {
                hasAnyType = true;
            }
        });

        const options: SelectModel<string>[] = Array.from(customerTypes).map((type) => ({
            id: type,
            value: type,
            label: CUSTOMER_TYPE_LABELS[type] || type.charAt(0).toUpperCase() + type.slice(1),
        }));

        if (hasAnyType) {
            options.unshift(ANY_OPTION);
        }

        return options;
    }, [
        templateData,
        formik.values.agreementType,
        formik.values.los,
        formik.values.state,
        formik.values.lang,
    ]);

    const useAutoReset = (
        field: keyof GenerateAgreementFormValues,
        fields: Array<keyof GenerateAgreementFormValues>,
    ) => {
        useEffect(() => {
            fields.forEach((field) => {
                formik.setFieldValue(field, '');
            });
        }, [formik.values[field]]);
    };

    useAutoReset('agreementType', ['los', 'state', 'lang', 'customerType']);
    useAutoReset('los', ['state', 'lang', 'customerType']);
    useAutoReset('state', ['lang', 'customerType']);
    useAutoReset('lang', ['customerType']);

    const useAutoSelect = (
        options: SelectModel<string>[],
        field: keyof GenerateAgreementFormValues,
        condition: boolean,
    ) => {
        useEffect(() => {
            if (options.length === 1 && !formik.values[field] && condition) {
                formik.setFieldValue(field, options[0].value);
            }
        }, [options, formik.values[field], condition]);
    };

    useAutoSelect(availableLosOptions, 'los', !!formik.values.agreementType);
    useAutoSelect(availableStateOptions, 'state', !!formik.values.los);
    useAutoSelect(availableLangOptions, 'lang', !!formik.values.state);
    useAutoSelect(customerTypeOptions, 'customerType', !!formik.values.lang);

    const formFields = [
        {name: 'agreementType', label: 'Agreement Type', condition: true, options: agreementTypeOptions},
        {name: 'los', label: 'LOS', condition: !!formik.values.agreementType, options: availableLosOptions},
        {name: 'state', label: 'State', condition: !!formik.values.los, options: availableStateOptions},
        {name: 'lang', label: 'Language', condition: !!formik.values.state, options: availableLangOptions},
        {
            name: 'customerType',
            label: 'Customer Type',
            condition: !!formik.values.lang,
            options: customerTypeOptions,
        },
    ];

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2}>
                {formFields.map(
                    ({name, label, condition, options}) =>
                        condition && (
                            <Grid2 size={3} key={name}>
                                <KasAutocompleteField
                                    name={name}
                                    formik={formik}
                                    options={options}
                                    label={label}
                                    disabled={submitting || options.length === 1}
                                    showValidation={false}
                                />
                            </Grid2>
                        ),
                )}
                <Grid2 size={3}>
                    <Button
                        variant='contained'
                        fullWidth
                        size='small'
                        type='submit'
                        loading={submitting}
                        disabled={!formik.isValid || submitting}>
                        Generate
                    </Button>
                </Grid2>
            </Grid2>
        </form>
    );
};
