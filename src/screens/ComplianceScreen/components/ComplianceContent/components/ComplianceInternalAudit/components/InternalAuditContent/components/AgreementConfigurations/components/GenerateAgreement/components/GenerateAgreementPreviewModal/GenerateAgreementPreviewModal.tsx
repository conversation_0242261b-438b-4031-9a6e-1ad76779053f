import React, {useState} from 'react';
import {Button, Grid2} from '@mui/material';
import {KasModal} from '@/components';
import {LetterPreview} from '@/views';
import {useGenerateAgreement} from './../../useGenerateAgreement';
import {useDownloadBlob} from '@/hooks/useDownloadBlob';

export const GenerateAgreementPreviewModal = () => {
    const [loadingFile, setLoadingFile] = useState(false);
    const {getBlob} = useDownloadBlob();
    const {openModal, setOpenModal} = useGenerateAgreement();

    const onClickHandler = async () => {
        const params = JSON.stringify({
            path: `/secured/compliance/config/agreement/template/download`,
            params: openModal?.props.searchParams,
        });

        setLoadingFile(true);
        await getBlob(params);
        setLoadingFile(false);
    };

    const onClose = () => setOpenModal(null);

    return (
        <KasModal title='Agreement Preview' open={!!openModal} onClose={onClose}>
            <Grid2 container spacing={2}>
                <Grid2 size={12}>
                    <LetterPreview html={openModal?.props.html} />
                </Grid2>
                <Grid2 container size={12} justifyContent='flex-end' spacing={2}>
                    <Grid2 size={3}>
                        <Button variant='outlined' fullWidth size='small' onClick={onClose}>
                            Cancel
                        </Button>
                    </Grid2>
                    <Grid2 size={3}>
                        <Button
                            variant='contained'
                            fullWidth
                            size='small'
                            loading={loadingFile}
                            disabled={loadingFile}
                            onClick={onClickHandler}>
                            Download
                        </Button>
                    </Grid2>
                </Grid2>
            </Grid2>
        </KasModal>
    );
};
