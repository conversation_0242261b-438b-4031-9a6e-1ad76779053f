import React, {createContext, useContext, useMemo, useState} from 'react';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {ComplianceAgreementModel} from '@/screens/ComplianceScreen/interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {useSnackbar} from '@/hooks/useSnackbar';
import {DataStateInterface, SelectModel} from '@/interfaces';
import {
    GenerateAgreementModal,
    GenerateAgreementModalProps,
    GenerateAgreementPreviewParams,
} from './interfaces';
import {ANY_OPTION} from './data';
import {uniqElements} from '@/utils/ArrayUtils';

interface GenerateAgreementContextModel {
    dataState: DataStateInterface<ComplianceAgreementModel[]>;
    loadData: () => Promise<void>;
    openModal: GenerateAgreementModalProps | null;
    setOpenModal: (value: GenerateAgreementModalProps | null) => void;
    generateAgreement: (searchParams: GenerateAgreementPreviewParams) => Promise<void>;
    templateData: ComplianceAgreementModel[];
    createSelectOption: (value: string | null, fallbackOptions: SelectModel<string>[]) => SelectModel<string>;
    agreementTypeOptions: SelectModel<string>[];
}

const GenerateAgreementContext = createContext<GenerateAgreementContextModel | undefined>(undefined);

interface GenerateAgreementProviderProps {
    children: React.ReactNode;
}

export const GenerateAgreementProvider = ({children}: GenerateAgreementProviderProps) => {
    const {showMessage} = useSnackbar();
    const [openModal, setOpenModal] = useState<GenerateAgreementModalProps | null>(null);
    const [dataState, setDataState] =
        useState<DataStateInterface<ComplianceAgreementModel[]>>(getDefaultState());

    const loadData = async () => {
        const url = `/api/secured/compliance/internal-audit/agreement?all=true`;

        setDataState(getLoadingState(dataState));
        const response = await apiRequest(url);
        setDataState(getLoadedState(response));
    };

    const generateAgreement = async (searchParams: GenerateAgreementPreviewParams) => {
        const params = new URLSearchParams(searchParams).toString();
        const url = `/api/secured/compliance/internal-audit/agreement/template?${params}`;

        const response = await apiRequest(url);

        if (response.value) {
            setOpenModal({
                type: GenerateAgreementModal.Generated_Agreement,
                props: {html: response.value, searchParams},
            });
        } else {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        }
    };

    const templateData: ComplianceAgreementModel[] = useMemo(() => {
        return (dataState.data || []).map((item) => ({
            ...item,
            los: item.los || 'Any',
            state: item.state || 'Any',
        }));
    }, [dataState.data]);

    const agreementTypeOptions = useMemo(() => {
        const combinations = uniqElements(templateData, (item) => `${item.variant}-${item.type}`);
        return combinations.map((value) => ({
            id: value,
            value,
            label: value.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase()),
        }));
    }, [templateData]);

    const createSelectOption = (value: string | null, fallbackOptions: SelectModel<string>[]) => {
        if (value === null) return ANY_OPTION;

        const found = fallbackOptions.find((option) => option.value === value);
        return (
            found || {
                id: value,
                value,
                label: value,
            }
        );
    };

    const value: GenerateAgreementContextModel = {
        dataState,
        loadData,
        openModal,
        setOpenModal,
        generateAgreement,
        templateData,
        createSelectOption,
        agreementTypeOptions,
    };

    return <GenerateAgreementContext.Provider value={value}>{children}</GenerateAgreementContext.Provider>;
};

export function useGenerateAgreement() {
    const context = useContext(GenerateAgreementContext);
    if (!context) {
        throw new Error('useGenerateAgreement must be used within GenerateAgreementProvider');
    }
    return context;
}
