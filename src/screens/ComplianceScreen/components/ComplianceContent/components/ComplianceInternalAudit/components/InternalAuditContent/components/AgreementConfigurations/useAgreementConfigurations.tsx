import {useEffect, useState} from 'react';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {ComplianceAgreementModel} from '@/screens/ComplianceScreen/interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {useSnackbar} from '@/hooks/useSnackbar';
import {DataStateInterface} from '@/interfaces';
import {InternalAuditTab} from './../../../../interfaces';

interface AgreementConfigurationsModel {
    dataState: DataStateInterface<ComplianceAgreementModel[]>;
    loadData: () => Promise<void>;
}

export function useAgreementConfigurations(activeTab: InternalAuditTab): AgreementConfigurationsModel {
    const {showMessage} = useSnackbar();
    const [dataState, setDataState] =
        useState<DataStateInterface<ComplianceAgreementModel[]>>(getDefaultState());

    const loadData = async () => {
        const url = `/api/secured/compliance/internal-audit/agreement`;

        setDataState(getLoadingState(dataState));
        const response = await apiRequest(url);
        setDataState(getLoadedState(response));

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        }
    };

    useEffect(() => {
        if (activeTab === InternalAuditTab.Agreements && !dataState.data && !dataState.loading) {
            loadData().then();
        }
    }, [activeTab, dataState]);

    return {
        dataState,
        loadData,
    };
}
