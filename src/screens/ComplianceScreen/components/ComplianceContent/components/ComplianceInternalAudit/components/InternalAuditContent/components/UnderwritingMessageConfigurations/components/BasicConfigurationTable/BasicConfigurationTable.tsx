import React from 'react';
import {Grid2, Paper, Typography} from '@mui/material';
import {PaginationState} from '@tanstack/react-table';
import {useTable} from '@/hooks/useTable';
import {KasTableContent, KasTablePagination, KasTableSearch} from '@/components/table';
import {KasTableProps} from '@/components/KasTable';

interface BasicConfigurationTableProps<T> extends KasTableProps<T> {
    title: string;
}

export const BasicConfigurationTable = <T,>({
    title,
    data,
    columns,
    sortingColumns,
}: BasicConfigurationTableProps<T>) => {
    const {table, setPagination} = useTable<T>(data || [], columns, sortingColumns);

    const handleChange = (value: Partial<PaginationState>) => {
        setPagination((prevState) => ({...prevState, ...value}));
    };

    return (
        <Grid2 container columnSpacing={1}>
            <Grid2 size={12}>
                <Paper elevation={0}>
                    <Grid2 container alignItems='center' p={2} spacing={2}>
                        <Grid2 size={9}>
                            <Typography variant='h6'>{title}</Typography>
                        </Grid2>
                        <Grid2 size={3}>
                            <KasTableSearch table={table} />
                        </Grid2>
                    </Grid2>
                </Paper>
            </Grid2>
            <Grid2 size={12}>
                <div className='kas-designed-table'>
                    <KasTableContent table={table} />
                </div>
            </Grid2>
            <Grid2 size={12}>
                <KasTablePagination table={table} onChange={handleChange} />
            </Grid2>
        </Grid2>
    );
};
