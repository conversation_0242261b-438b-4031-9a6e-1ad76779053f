import {ColumnDef, createColumnHelper} from '@tanstack/react-table';
import {ComplianceUnderwritingMessageModel} from '@/screens/ComplianceScreen/interfaces';
import {parseSafeHtml} from '@/utils/ParseSafeHtml';

const columnHelper = createColumnHelper<ComplianceUnderwritingMessageModel>();

export const UnderwritingMessageColumns = [
    columnHelper.accessor('code', {
        id: 'code',
        header: 'Type',
        cell: (props) => <span style={{whiteSpace: 'nowrap'}}>{props.getValue()}</span>,
    }),
    columnHelper.accessor('value', {
        id: 'value',
        header: 'Message',
        cell: (props) => parseSafeHtml(props.getValue()),
    }),
    columnHelper.accessor('last_update_time', {
        id: 'last_update_time',
        header: 'Last Updated',
        cell: (props) => <span style={{whiteSpace: 'nowrap'}}>{props.getValue()}</span>,
    }),
] as ColumnDef<ComplianceUnderwritingMessageModel, unknown>[];
