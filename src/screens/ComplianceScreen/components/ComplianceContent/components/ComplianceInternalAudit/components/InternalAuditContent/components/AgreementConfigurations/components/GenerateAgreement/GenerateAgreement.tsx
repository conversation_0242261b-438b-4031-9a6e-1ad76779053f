import React, {useEffect} from 'react';
import {Grid2, Paper, Skeleton, Typography} from '@mui/material';
import {KasSwitch, KasSwitchWhen} from '@/components';
import {ErrorView} from '@/views';
import {GenerateAgreementForm, GenerateAgreementPreviewModal} from './components';
import {useGenerateAgreement} from './useGenerateAgreement';

export const GenerateAgreement = () => {
    const {dataState, loadData} = useGenerateAgreement();

    useEffect(() => {
        loadData().then();
    }, []);

    return (
        <Paper elevation={0}>
            <Grid2 container p={2} spacing={2}>
                <Grid2 size={3}>
                    <Typography variant='h6' py={1}>
                        Generate Agreement
                    </Typography>
                </Grid2>
                <Grid2 size={9}>
                    <KasSwitch>
                        <KasSwitchWhen condition={dataState.loading}>
                            <Grid2 container spacing={2}>
                                <Grid2 size={3}>
                                    <Skeleton variant='rounded' animation='wave' height={40} />
                                </Grid2>
                                <Grid2 size={3}>
                                    <Skeleton variant='rounded' animation='wave' height={40} />
                                </Grid2>
                            </Grid2>
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={!!dataState.error}>
                            <ErrorView error={dataState.error} onTryAgain={loadData} />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={!!dataState.data}>
                            <GenerateAgreementForm />
                        </KasSwitchWhen>
                    </KasSwitch>
                </Grid2>
            </Grid2>
            <GenerateAgreementPreviewModal />
        </Paper>
    );
};
