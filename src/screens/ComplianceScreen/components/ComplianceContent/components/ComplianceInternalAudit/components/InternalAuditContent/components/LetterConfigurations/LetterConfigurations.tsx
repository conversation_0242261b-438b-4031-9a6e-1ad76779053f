import React from 'react';
import {Kas<PERSON>oading, KasLoadingB<PERSON><PERSON>rop, KasSwitch, KasSwitchWhen} from '@/components';
import {ErrorView} from '@/views';
import {Stack} from '@mui/material';
import {useLetterConfigurations} from './useLetterConfigurations';
import {InternalAuditTab} from './../../../../interfaces';
import {LetterConfigurationsView} from '@/views/compliance';

export const LetterConfigurations = ({activeTab}: {activeTab: InternalAuditTab}) => {
    const {dataState, loadData} = useLetterConfigurations(activeTab);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={!dataState.data && dataState.loading}>
                <Stack p={14} alignItems='center'>
                    <KasLoading size={100} />
                </Stack>
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!dataState.error}>
                <ErrorView error={dataState.error} onTryAgain={loadData} />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!dataState.data}>
                {dataState.loading && <KasLoadingBackDrop />}
                <LetterConfigurationsView data={dataState.data || []} />
            </KasSwitchWhen>
        </KasSwitch>
    );
};
