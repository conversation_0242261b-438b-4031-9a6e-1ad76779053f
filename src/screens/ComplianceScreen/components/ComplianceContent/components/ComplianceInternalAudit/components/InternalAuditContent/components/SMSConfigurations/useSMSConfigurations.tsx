import {useEffect, useState} from 'react';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {ComplianceSMSModel} from '@/screens/ComplianceScreen/interfaces';
import {apiRequest} from '@/utils/AxiosUtils';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {useSnackbar} from '@/hooks/useSnackbar';
import {DataStateInterface} from '@/interfaces';
import {InternalAuditTab} from './../../../../interfaces';

interface SMSConfigurationsModel {
    dataState: DataStateInterface<ComplianceSMSModel[]>;
    loadData: () => Promise<void>;
}

export function useSMSConfigurations(activeTab: InternalAuditTab): SMSConfigurationsModel {
    const {showMessage} = useSnackbar();
    const [dataState, setDataState] = useState<DataStateInterface<ComplianceSMSModel[]>>(getDefaultState());

    const loadData = async () => {
        const url = `/api/secured/compliance/internal-audit/sms`;

        setDataState(getLoadingState(dataState));
        const response = await apiRequest(url);
        setDataState(getLoadedState(response));

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        }
    };

    useEffect(() => {
        if (activeTab === InternalAuditTab.SMS && !dataState.data && !dataState.loading) {
            loadData().then();
        }
    }, [activeTab, dataState]);

    return {
        dataState,
        loadData,
    };
}
