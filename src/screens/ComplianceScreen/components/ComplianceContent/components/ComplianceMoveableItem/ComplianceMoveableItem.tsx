import './styles.scss';

import React, {ReactNode, useEffect, useMemo, useState} from 'react';
import {Accordion, AccordionDetails, AccordionSummary, Divider, Stack, Grid2} from '@mui/material';
import {Refresh} from '@mui/icons-material';
import IconButton from '@mui/material/IconButton';
import {
    KasDotsMenu,
    KasDotsMenuItemProps,
    KasDragTitle,
    KasErrorBoundary,
    KasExpandIcon,
    KasLoadingStatusIcon,
} from '@/components';
import Box from '@mui/material/Box';
import {useSortableList} from '@/views';

interface ComplianceMoveableItemProps<T> {
    title: string;
    loading: boolean;
    loadingError: string;
    loaded: boolean;
    PreviewComponent?: ReactNode;
    DetailsComponent: ReactNode;
    menuItems?: KasDotsMenuItemProps[];
    onRefresh?: () => void;
}

export const ComplianceMoveableItem = <T,>({
    title,
    loading,
    loadingError,
    loaded,
    PreviewComponent,
    DetailsComponent,
    menuItems = [],
    onRefresh,
}: ComplianceMoveableItemProps<T>) => {
    const {attributes, listeners, setActivatorNodeRef} = useSortableList();
    const [isAccordionOpen, setIsAccordionOpen] = useState(false);
    const [showStatus, setShowStatus] = useState(true);

    const className = useMemo(() => {
        if (!isAccordionOpen && showStatus) {
            if (loadingError) {
                return 'error';
            }

            if (loaded && !loading) {
                return 'success';
            }
        }

        return '';
    }, [showStatus, isAccordionOpen, loaded, loading, loadingError]);

    const handleAccordionChange = () => {
        setIsAccordionOpen(!isAccordionOpen);
        setShowStatus(false);
    };

    useEffect(() => {
        if (!loaded && isAccordionOpen) {
            setIsAccordionOpen(false);
        }
    }, [loaded, isAccordionOpen]);

    return (
        <div className='kas-compliance-moveable-item'>
            <Accordion expanded={isAccordionOpen} disableGutters elevation={0}>
                <AccordionSummary
                    component='div'
                    className={className}
                    expandIcon={<KasExpandIcon expanded={loaded} onClick={handleAccordionChange} />}>
                    <Box sx={{flexGrow: 1}}>
                        <KasErrorBoundary>
                            <Grid2 container alignItems='center'>
                                <Grid2 size={2} pr={1}>
                                    <KasDragTitle
                                        attributes={attributes}
                                        listeners={listeners}
                                        setActivatorNodeRef={setActivatorNodeRef}>
                                        {title}
                                    </KasDragTitle>
                                </Grid2>
                                <Grid2 size={9}>{PreviewComponent}</Grid2>
                                <Grid2 size={1}>
                                    <Stack direction='row' justifyContent='flex-end'>
                                        <KasLoadingStatusIcon
                                            loading={loading}
                                            loadingError={loadingError}
                                            success={loaded && !loading}
                                        />
                                        {!loading && onRefresh && (
                                            <IconButton title='Refresh' onClick={onRefresh}>
                                                <Refresh />
                                            </IconButton>
                                        )}
                                        <KasDotsMenu
                                            disabled={!!loadingError || loading}
                                            menuItems={menuItems}
                                        />
                                    </Stack>
                                </Grid2>
                            </Grid2>
                        </KasErrorBoundary>
                    </Box>
                </AccordionSummary>
                <AccordionDetails>
                    <Divider />
                    <Box p={2}>
                        <KasErrorBoundary>{DetailsComponent}</KasErrorBoundary>
                    </Box>
                </AccordionDetails>
            </Accordion>
        </div>
    );
};
