import React from 'react';
import { ComplianceScraModel } from '@/screens/ComplianceScreen/interfaces';
import { TableView } from '@/views';
import { useComplianceScra } from '../../useComplianceScra';
import { ScraTableColumns } from './tables/ComplianceScraColumns';
import { ColumnDef } from '@tanstack/react-table';
import { getCurrencyExcelFormat } from '@/utils/TableUtils';

export const ComplianceScraDetails = () => {

    const excelColumnFormats = ['orig_interest', 'scra_interest', 'scra_credit'].map(getCurrencyExcelFormat);

    const {scraItems} = useComplianceScra();

    return (
        <TableView<ComplianceScraModel>
            withTableActions={true}
            loading={scraItems.pending}
            error={scraItems.error}
            data={scraItems.value || null}
            columns={ScraTableColumns as ColumnDef<ComplianceScraModel, unknown>[]}
            tableName='Compliance SCRA Loan'
            sortingColumns={[{id: 'loan_id', desc: false}]}
            excelColumnFormats={excelColumnFormats}
        />
    );
}