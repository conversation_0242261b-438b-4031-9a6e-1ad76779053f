import React from 'react';
import { CellContext, createColumnHelper } from '@tanstack/react-table';
import { defaultAmountColumn, defaultInfoColumn } from '@/utils/TableUtils';
import { ComplianceScraModel } from '@/screens/ComplianceScreen/interfaces';
import { ActionCell } from '@/components/table/cells';
import ScraCreditApplyButton from '../components/ScraCreditApplyButton';

const columnHelper = createColumnHelper<ComplianceScraModel>();

const _defaultInfoColumn = defaultInfoColumn<ComplianceScraModel>;
const _defaultAmountColumn = defaultAmountColumn<ComplianceScraModel>;

export const ScraTableColumns = [
    _defaultInfoColumn('loan_id', 'Loan ID'),
    _defaultInfoColumn('scra_days', 'Days'),
    _defaultAmountColumn('scra_installment', 'SCRA Installment'),
    _defaultAmountColumn('loan_installment', 'Regular Installment'),
    _defaultAmountColumn('orig_interest', 'Original Interest'),
    _defaultAmountColumn('scra_interest', 'SCRA Interest'),
    _defaultAmountColumn('scra_credit', 'SCRA Credit'),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<ComplianceScraModel, number>) => {
            const {applied, scra_credit, loan_id} = props.row.original;
            if(!applied && scra_credit > 0) {
                return (
                    <ScraCreditApplyButton loanId={loan_id} />
                );
            }
            return;
        },
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
];
