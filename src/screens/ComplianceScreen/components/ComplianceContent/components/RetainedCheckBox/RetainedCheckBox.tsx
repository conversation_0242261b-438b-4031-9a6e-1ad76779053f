import React from 'react';
import {Checkbox, FormControlLabel, SxProps, Theme} from '@mui/material';
import {FormikValues} from 'formik';

interface RetainedCheckBox {
    formik: FormikValues;
    disabled?: boolean;
    sx?: SxProps<Theme>;
}

export const RetainedCheckBox = ({formik, disabled = false, sx = {}}: RetainedCheckBox) => {
    return (
        <FormControlLabel
            disabled={disabled}
            label='Bank Retained'
            control={
                <Checkbox
                    size='small'
                    name='retained'
                    sx={sx}
                    onChange={formik.handleChange}
                    checked={formik.values.retained}
                />
            }
        />
    );
};
