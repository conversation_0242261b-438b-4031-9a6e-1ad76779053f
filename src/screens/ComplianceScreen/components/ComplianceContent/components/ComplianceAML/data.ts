import {SelectModel} from '@/interfaces';
import {AmlResultType} from '@/screens/ComplianceScreen/interfaces';
import {ComplianceAMLItemModel, ComplianceAMLItemType} from './interfaces';

export const AML_ITEMS_CONFIG_KEY = 'compliance-aml-items-config';

export const RESULT_OPTIONS: SelectModel<AmlResultType>[] = [
    {
        id: 'DELINQUENT_PAYMENT',
        value: 'DELINQUENT_PAYMENT',
        label: 'Delinquent Payment',
    },
    {
        id: 'PAYOFF_QUOTED',
        value: 'PAYOFF_QUOTED',
        label: 'Payoff Quoted',
    },
    {
        id: 'ALTERNATIVE_REPAYMENT_SCHEDULE',
        value: 'ALTERNATIVE_REPAYMENT_SCHEDULE',
        label: 'Alternative Repayment Schedule',
    },
    {
        id: 'ESCALATED',
        value: 'ESCALATED',
        label: 'Escalated',
    },
    {
        id: 'OTHER',
        value: 'OTHER',
        label: 'Other',
    },
];

export const AVAILABLE_AML_ITEMS: ComplianceAMLItemModel[] = [
    {
        id: '1',
        title: 'Transaction Monitoring',
        type: ComplianceAMLItemType.Transactions,
    },
    {
        id: '2',
        title: 'Reports',
        type: ComplianceAMLItemType.Reports,
    },
];
