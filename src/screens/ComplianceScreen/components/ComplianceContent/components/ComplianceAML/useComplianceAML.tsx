import React, {createContext, useContext, useEffect, useState} from 'react';
import {ComplianceAMLItemModel} from './interfaces';
import {AML_ITEMS_CONFIG_KEY, AVAILABLE_AML_ITEMS} from './data';
import {useAppDispatch, useAppSelector} from '@/lib/hooks';
import {selectUserPreferences, updateUserPreferences} from '@/lib/slices/userPreferencesSlice';
import {uniqElements} from '@/utils/ArrayUtils';

interface ComplianceAMLContextModel {
    amlItems: ComplianceAMLItemModel[];
    updateAmlItems: (data: ComplianceAMLItemModel[]) => void;
    expandedItems: string[];
    updateExpandedItem: (id: string, value: boolean) => void;
}

const ComplianceAMLContext = createContext<ComplianceAMLContextModel | undefined>(undefined);

export const ComplianceAMLProvider = ({children}: {children: React.ReactNode}) => {
    const dispatch = useAppDispatch();
    const userPreferences = useAppSelector(selectUserPreferences);
    const [amlItems, setAmlItems] = useState<ComplianceAMLItemModel[]>([]);

    const updateAmlItems = (sections: ComplianceAMLItemModel[]) => {
        const compliance = userPreferences.value?.compliance || {};
        setAmlItems(sections);
        dispatch(updateUserPreferences({compliance: {...compliance, aml: {sections}}}));
    };

    const [expandedItems, setExpandedItems] = useState<string[]>(() => {
        const storageValue = localStorage.getItem(AML_ITEMS_CONFIG_KEY);
        return storageValue ? storageValue.split(',') : [];
    });

    const updateExpandedItem = (id: string, value: boolean) => {
        setExpandedItems((prev) => {
            const newExpandedItems = value ? [...prev, id] : prev.filter((item) => item !== id);

            return uniqElements(newExpandedItems, (item) => item);
        });
    };

    const updateExpandedItemsConfig = () => {
        if (expandedItems.length > 0) {
            localStorage.setItem(AML_ITEMS_CONFIG_KEY, expandedItems.join(','));
        } else {
            localStorage.removeItem(AML_ITEMS_CONFIG_KEY);
        }
    };

    useEffect(updateExpandedItemsConfig, [expandedItems]);

    const setSortedItems = () => {
        const sections = userPreferences.value?.compliance?.aml?.sections;

        if (sections) {
            setAmlItems(sections);
        } else {
            setAmlItems(AVAILABLE_AML_ITEMS);
        }
    };

    useEffect(setSortedItems, []);

    const value: ComplianceAMLContextModel = {
        amlItems,
        updateAmlItems,
        expandedItems,
        updateExpandedItem,
    };

    return <ComplianceAMLContext.Provider value={value}>{children}</ComplianceAMLContext.Provider>;
};

export function useComplianceAML() {
    const context = useContext(ComplianceAMLContext);
    if (!context) {
        throw new Error('useComplianceAML must be used within ComplianceAMLProvider');
    }
    return context;
}
