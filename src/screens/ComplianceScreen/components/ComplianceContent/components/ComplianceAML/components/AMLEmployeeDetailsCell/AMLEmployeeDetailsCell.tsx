import React from 'react';
import {GlobalModal, KasLink, useGlobalModal} from '@/components';

interface AMLEmployeeDetailsCellProps {
    id: number;
    children: React.ReactNode;
}

export const AMLEmployeeDetailsCell = ({children, id}: AMLEmployeeDetailsCellProps) => {
    const {showGlobalModal} = useGlobalModal();

    return (
        <KasLink
            title='Show Details'
            onClick={() =>
                showGlobalModal({
                    type: GlobalModal.AML_Report,
                    props: {
                        id,
                        type: 'employee',
                    },
                })
            }>
            {children}
        </KasLink>
    );
};
