import React, {useState} from 'react';
import {KasSelect} from '@/components';
import MenuItem from '@mui/material/MenuItem';
import {apiRequest} from '@/utils/AxiosUtils';
import {useSnackbar} from '@/hooks/useSnackbar';
import {useComplianceAMLMonitoring} from '../../../../useComplianceAMLMonitoring';
import {SelectChangeEvent} from '@mui/material';
import {GlobalModal, useGlobalModal} from '@/components';
import {KasCommentFormValues} from '@/components/KasGlobalModals/components/KasCommentForm/schema';
import {RESULT_OPTIONS} from './../../../../../../data';
import {AMLResultCell} from './../../../../../../components';
import {AmlResultType} from '@/screens/ComplianceScreen/interfaces';
interface ComplianceAmlResultsProps {
    queue_id: number;
    result: AmlResultType | null;
}

export const ComplianceAmlResults = ({queue_id, result}: ComplianceAmlResultsProps) => {
    const {showMessage} = useSnackbar();
    const [submitting, setSubmitting] = useState(false);
    const {clearedItems, updateItemResult, setItemUpdating} = useComplianceAMLMonitoring();
    const [displayResult, setDisplayResult] = useState<string | null>(result || null);
    const isCleared = clearedItems.has(queue_id);
    const {showGlobalModal, hideGlobalModal} = useGlobalModal();

    const updateResult = async (newValue: AmlResultType, comment?: string) => {
        setSubmitting(true);
        setItemUpdating(queue_id, true);
        try {
            setDisplayResult(newValue);
            const body = JSON.stringify({result: newValue, comment: comment});

            const response = await apiRequest(`/api/secured/compliance/aml/${queue_id}`, {
                method: 'PUT',
                body,
            });

            if (response.value && !response.error) {
                updateItemResult(queue_id, newValue, comment);
                setSubmitting(false);
                setItemUpdating(queue_id, false);
                showMessage('Result updated successfully', 'success');
                return true;
            } else {
                throw new Error(response.error || 'Failed to update result');
            }
        } catch (error) {
            showMessage('Failed to update result', 'error');
            setDisplayResult(result || null);
        }
    };

    const handleResultChange = async (event: SelectChangeEvent) => {
        const newValue = event.target.value as AmlResultType;

        if (newValue === 'OTHER') {
            // Show comment modal for "Other" selection
            showGlobalModal({
                type: GlobalModal.Comment,
                props: {
                    title: 'Please provide detail comment',
                    onSubmit: async (values: KasCommentFormValues) => {
                        const success = await updateResult(newValue, values.comment);
                        if (success) {
                            hideGlobalModal();
                        }
                    },
                },
            });
            return;
        }

        await updateResult(newValue);
    };

    if (isCleared) {
        return <AMLResultCell result={result} />;
    }

    return (
        <KasSelect
            disabled={submitting}
            data-testid='result-dropdown'
            value={displayResult || ''}
            onChange={handleResultChange}>
            <MenuItem key='none' value=''>
                <em>~none~</em>
            </MenuItem>
            {RESULT_OPTIONS.map((option) => (
                <MenuItem key={option.id} value={option.value}>
                    {option.label}
                </MenuItem>
            ))}
        </KasSelect>
    );
};
