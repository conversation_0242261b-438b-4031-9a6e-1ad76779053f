import {SelectModel} from '@/interfaces';
import {AMLFlagType, AmlResult, ComplianceAMLParamsModel} from '@/screens/ComplianceScreen/interfaces';
import {capitalizeWords} from '@/utils/TextUtils';

export const RESULT_OPTIONS: SelectModel<string>[] = Object.entries(AmlResult).map(([key, value]) => ({
    id: key,
    value,
    label: capitalizeWords(value.replaceAll('_', ' ')),
}));

export const FLAG_TYPE_OPTIONS: SelectModel<AMLFlagType>[] = Object.entries(AMLFlagType).map(
    ([key, value]) => ({
        id: key,
        value,
        label: capitalizeWords(value.replaceAll('_', ' ')),
    }),
);

export const DEFAULT_FILTER_PARAMS: ComplianceAMLParamsModel = {
    startDate: null,
    endDate: null,
    flagTypes: [],
    useEntryDate: false,
};
