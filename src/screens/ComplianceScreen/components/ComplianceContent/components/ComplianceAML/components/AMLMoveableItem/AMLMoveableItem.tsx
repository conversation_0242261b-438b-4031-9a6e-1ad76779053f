import './styles.scss';

import React, {ReactNode, useEffect, useMemo, useState} from 'react';
import {Accordion, AccordionDetails, AccordionSummary, Divider, Grid2, Stack} from '@mui/material';
import {
    KasDotsMenu,
    KasDotsMenuItemProps,
    KasDragTitle,
    KasErrorBoundary,
    KasExpandIcon,
    KasLoadingStatusIcon,
} from '@/components';
import {useSortableList} from '@/views';
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import {Refresh} from '@mui/icons-material';
import {ComplianceAMLItemModel} from './../../interfaces';
import {useComplianceAML} from './../../useComplianceAML';

interface AMLMoveableItemProps {
    item: ComplianceAMLItemModel;
    titleSize?: number;
    loading: boolean;
    loadingError: string;
    loaded: boolean;
    PreviewComponent?: ReactNode;
    DetailsComponent: ReactNode;
    menuItems?: KasDotsMenuItemProps[];
    onRefresh?: () => void;
}

export const AMLMoveableItem = ({
    item,
    titleSize = 2,
    loading = false,
    loadingError,
    loaded,
    PreviewComponent,
    DetailsComponent,
    menuItems = [],
    onRefresh,
}: AMLMoveableItemProps) => {
    const {expandedItems, updateExpandedItem} = useComplianceAML();
    const [isAccordionOpen, setIsAccordionOpen] = useState(false);
    const {attributes, listeners, setActivatorNodeRef} = useSortableList();

    const expandedItem = useMemo(() => {
        return expandedItems.includes(item.id);
    }, [expandedItems, item]);

    const handleAccordionChange = () => {
        const value = !isAccordionOpen;

        setIsAccordionOpen(value);
        updateExpandedItem(item.id, value);
    };

    useEffect(() => {
        setIsAccordionOpen(expandedItem);
    }, [expandedItem]);

    return (
        <div className='kas-aml-moveable-item'>
            <Accordion expanded={isAccordionOpen} disableGutters elevation={0}>
                <AccordionSummary
                    component='div'
                    expandIcon={<KasExpandIcon expanded={true} onClick={handleAccordionChange} />}
                    aria-controls={item.id}
                    id={item.id}
                    onDoubleClick={handleAccordionChange}>
                    <Box sx={{flexGrow: 1}}>
                        <KasErrorBoundary>
                            <Grid2 container alignItems='center'>
                                <Grid2 size={titleSize} pr={1}>
                                    <KasDragTitle
                                        attributes={attributes}
                                        listeners={listeners}
                                        setActivatorNodeRef={setActivatorNodeRef}>
                                        {item.title}
                                    </KasDragTitle>
                                </Grid2>
                                <Grid2 size={11 - titleSize}>{PreviewComponent}</Grid2>
                                <Grid2 size={1}>
                                    <Stack direction='row' justifyContent='flex-end'>
                                        <KasLoadingStatusIcon
                                            loading={loading}
                                            loadingError={loadingError}
                                            success={loaded && !loading}
                                        />
                                        {!loading && onRefresh && (
                                            <IconButton title='Refresh' onClick={onRefresh}>
                                                <Refresh />
                                            </IconButton>
                                        )}
                                        <KasDotsMenu
                                            disabled={!!loadingError || loading}
                                            menuItems={menuItems}
                                        />
                                    </Stack>
                                </Grid2>
                            </Grid2>
                        </KasErrorBoundary>
                    </Box>
                </AccordionSummary>
                <AccordionDetails>
                    <Stack pb={2} px={5} spacing={2}>
                        <Divider />
                        <KasErrorBoundary>{DetailsComponent}</KasErrorBoundary>
                    </Stack>
                </AccordionDetails>
            </Accordion>
        </div>
    );
};
