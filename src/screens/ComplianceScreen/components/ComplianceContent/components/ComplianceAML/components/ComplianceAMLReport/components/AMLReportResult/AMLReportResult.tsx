import React from 'react';
import {ColumnDef} from '@tanstack/react-table';
import {ComplianceAMLReportsModel} from '@/screens/ComplianceScreen/interfaces';
import {AMLTableColumns} from './tables';
import {TableView} from '@/views';
import {useComplianceAMLReports} from './../../useComplianceAMLReports';

export const AMLReportResult = () => {
    const {amlReportState} = useComplianceAMLReports();

    return (
        <TableView<ComplianceAMLReportsModel>
            withTableActions={true}
            loading={amlReportState.loading}
            error={amlReportState.error}
            data={amlReportState.data}
            columns={AMLTableColumns as ColumnDef<ComplianceAMLReportsModel, unknown>[]}
            tableName='Compliance AML Review'
        />
    );
};
