import React, {createContext, useContext, useRef, useState} from 'react';
import {Completable, DataStateInterface} from '@/interfaces';
import {useSnackbar} from '@/hooks/useSnackbar';
import {apiRequest} from '@/utils/AxiosUtils';
import {ComplianceAmlModel, AmlResultType} from '@/screens/ComplianceScreen/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';

interface ComplianceAmlParams {}

interface ComplianceAmlContextModel {
    amlItemsState: DataStateInterface<ComplianceAmlModel[]>;
    clearedItems: Set<number>;
    updatingItems: Set<number>;
    loadAmlItems: (params: ComplianceAmlParams) => Promise<Completable<ComplianceAmlModel[]>>;
    refreshAmlItems: () => Promise<void>;
    addClearedItem: (queueId: number) => void;
    removeClearedItem: (queueId: number) => void;
    updateItemResult: (queueId: number, result: AmlResultType, comment?: string) => void;
    setItemUpdating: (queueId: number, updating: boolean) => void;
}

const ComplianceAmlContext = createContext<ComplianceAmlContextModel | undefined>(undefined);

export const ComplianceAMLProvider = ({children}: {children: React.ReactNode}) => {
    const {showMessage} = useSnackbar();
    const [amlItemsState, setAmlItemsState] = useState(getDefaultState<ComplianceAmlModel[]>);
    const [clearedItems, setClearedItems] = useState<Set<number>>(new Set());
    const [updatingItems, setUpdatingItems] = useState<Set<number>>(new Set());
    const paramsCache = useRef<ComplianceAmlParams>();

    const loadAmlItems = async (params: ComplianceAmlParams): Promise<Completable<ComplianceAmlModel[]>> => {
        const _params = new URLSearchParams({}).toString(); // for future use of params

        // Reset cleared items when search is called
        setClearedItems(new Set());
        const url = `/api/secured/compliance/aml?${_params}`;

        setAmlItemsState(getLoadingState(amlItemsState));
        const response = await apiRequest(url);
        setAmlItemsState(getLoadedState(response));

        // Add items that are already cleared to the cleared items set
        if (response.value) {
            const alreadyClearedItems = response.value
                .filter((item: ComplianceAmlModel) => item.cleared_time)
                .map((item: ComplianceAmlModel) => item.queue_id);
            if (alreadyClearedItems.length > 0) {
                setClearedItems(new Set(alreadyClearedItems));
            } else {
                setClearedItems(new Set());
            }
        }

        if (response.error) {
            showMessage(response.error);
        }

        paramsCache.current = params;

        return response;
    };

    const refreshAmlItems = async () => {
        if (paramsCache.current) {
            await loadAmlItems(paramsCache.current);
        }
    };

    const addClearedItem = (queueId: number) => {
        setClearedItems((prev) => new Set([...Array.from(prev), queueId]));
    };

    const removeClearedItem = (queueId: number) => {
        setClearedItems((prev) => new Set([...Array.from(prev).filter((id) => id !== queueId)]));
    };

    const updateItemResult = (queueId: number, result: AmlResultType, comment?: string) => {
        setAmlItemsState((prevState) => {
            if (!prevState.data) return prevState;

            const updatedData = prevState.data.map((item) =>
                item.queue_id === queueId ? {...item, result, ...(comment && {comment})} : item,
            );

            return {...prevState, data: updatedData};
        });
    };

    const setItemUpdating = (queueId: number, updating: boolean) => {
        setUpdatingItems((prev) => {
            const newSet = new Set(prev);
            if (updating) {
                newSet.add(queueId);
            } else {
                newSet.delete(queueId);
            }
            return newSet;
        });
    };

    const value: ComplianceAmlContextModel = {
        amlItemsState,
        clearedItems,
        updatingItems,
        loadAmlItems,
        refreshAmlItems,
        addClearedItem,
        removeClearedItem,
        updateItemResult,
        setItemUpdating,
    };

    return <ComplianceAmlContext.Provider value={value}>{children}</ComplianceAmlContext.Provider>;
};

export const useComplianceAMLMonitoring = () => {
    const context = useContext(ComplianceAmlContext);
    if (!context) {
        throw new Error('useComplianceAml must be used within a ComplianceAMLProvider');
    }
    return context;
};
