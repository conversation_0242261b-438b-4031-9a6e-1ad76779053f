import {ComplianceAmlModel} from '@/screens/ComplianceScreen/interfaces';
import {KasSharedLink} from '@/components';
import {UNDERWRITING_EMPL_HASH} from '@/constants';
import React from 'react';
import {createColumnHelper} from '@tanstack/react-table';
import {AMLEmployeeDetailsCell} from './components';
import {accessorAMLFlagTypeCellFn, AMLFlagTypeCell} from '@/components/table/cells';

export const columnHelper = createColumnHelper<ComplianceAmlModel>();

export const loanColumn = columnHelper.accessor('loan_id', {
    id: 'loan_id',
    header: 'Loan ID',
    cell: (props) => (
        <KasSharedLink
            href={`/secured/underwriting#${UNDERWRITING_EMPL_HASH}:${props.row.original.employee_id}`}>
            {props.getValue()}
        </KasSharedLink>
    ),
    meta: {
        exportHTML: (cell) => cell.getValue(),
    },
});

export const prevCountColumn = columnHelper.accessor('prev_queue_count', {
    id: 'prev_queue_count',
    header: 'Previous Queue Count',
    cell: (props) => {
        const id = props.row.original.employee_id;
        const count = props.getValue();

        return id && count ? <AMLEmployeeDetailsCell id={id}>{count}</AMLEmployeeDetailsCell> : null;
    },
    meta: {
        exportHTML: (cell) => cell.getValue(),
    },
});

export const commentColumn = columnHelper.accessor((data) => data.comment || '', {
    id: 'comment',
    header: 'Comment',
    cell: (props) => props.getValue(),
    meta: {
        exportHTML: (cell) => cell.getValue(),
    },
});

export const flagTypeColumn = columnHelper.accessor((data) => accessorAMLFlagTypeCellFn(data.flag_types), {
    id: 'flag_types',
    header: 'Flag Types',
    cell: (props) => <AMLFlagTypeCell flagTypes={props.row.original.flag_types} />,
    meta: {
        exportHTML: (cell) => cell.getValue(),
    },
});
