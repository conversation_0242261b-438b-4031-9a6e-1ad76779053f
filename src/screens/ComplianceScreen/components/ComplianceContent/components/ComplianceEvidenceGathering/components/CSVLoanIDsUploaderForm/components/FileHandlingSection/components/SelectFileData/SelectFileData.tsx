import React, {useEffect, useState} from 'react';
import <PERSON>, {ParseResult} from 'papaparse';
import {useSnackbar} from '@/hooks/useSnackbar';
import {KasLoading, KasNoResults} from '@/components';
import {Autocomplete, TextField} from '@mui/material';
import {uniqElements} from '@/utils/ArrayUtils';

interface SelectFileDataProps {
    file: File;
    onSelect: (value: string[]) => void;
}

export const SelectFileData = ({file, onSelect}: SelectFileDataProps) => {
    const {showMessage} = useSnackbar();
    const [isParsing, setIsParsing] = useState(false);
    const [headers, setHeaders] = useState<string[]>([]);
    const [columnData, setColumnData] = useState<string[]>([]);
    const [csvData, setCsvData] = useState<Record<string, string>[]>([]);

    const setParseResult = (results: ParseResult<unknown>) => {
        const data = results.data as Record<string, string>[];
        const headers = Object.keys(data[0] || {});

        setHeaders(headers);
        setCsvData(data);
        setColumnData([]);
        setIsParsing(false);
    };

    const handleHeaderSelect = (header: string | null) => {
        if (header) {
            const data = csvData
                .map((row) => row[header])
                .filter((item) => item != null && !Number.isNaN(item));

            setColumnData(data);
        } else {
            setColumnData([]);
        }
    };

    useEffect(() => {
        const uniqueColumnData = uniqElements(
            columnData.filter((item) => item),
            (item) => item,
        );

        onSelect(uniqueColumnData);
    }, [columnData]);

    useEffect(() => {
        setIsParsing(true);
        Papa.parse(file, {
            header: true,
            skipEmptyLines: true,
            worker: true,
            complete: setParseResult,
            error: (error) => {
                showMessage(`Failed to parse the CSV file. ${error.message}`, 'error');
                setIsParsing(false);
            },
        });
    }, [file]);

    if (isParsing) {
        return <KasLoading size={40} />;
    }

    return (
        <>
            {headers.length > 0 ? (
                <Autocomplete
                    size='small'
                    options={headers}
                    onChange={(_, newValue) => {
                        handleHeaderSelect(newValue);
                    }}
                    renderInput={(params) => (
                        <TextField {...params} variant='outlined' label='Select Header' />
                    )}
                />
            ) : (
                <KasNoResults text='No data found' p={2} bgcolor='var(--color-grey)' />
            )}
        </>
    );
};
