import React from 'react';
import {Grid2, Typography} from '@mui/material';
import {EvidenceTypes, LoanIDsField} from './components';
import {useComplianceEvidenceGathering} from '@/screens/ComplianceScreen/components/ComplianceContent/components';
import {KasDownloadButton} from '@/components';

export const EvidenceGatheringForm = () => {
    const {formik, submitting} = useComplianceEvidenceGathering();

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} alignItems='center'>
                <Grid2 size={2.5}>
                    <Typography variant='subtitle1'>Evidence Gathering Report</Typography>
                </Grid2>
                <Grid2 size={2.5}>
                    <EvidenceTypes />
                </Grid2>
                <Grid2 size={5}>
                    <LoanIDsField />
                </Grid2>
                <Grid2 size={2}>
                    <KasDownloadButton submitting={submitting} disabled={!formik.isValid || submitting} />
                </Grid2>
            </Grid2>
        </form>
    );
};
