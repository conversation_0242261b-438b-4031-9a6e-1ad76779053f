import React from 'react';
import {Divider, Paper} from '@mui/material';
import Box from '@mui/material/Box';
import {ComplianceReportsHead, ComplianceReportsResult} from './components';
import {useComplianceReports} from './../../components';
import {KasLoadingBackDrop, KasSwitch, KasSwitchWhen} from '@/components';
import {NoResultsView} from '@/views';

export const ComplianceReports = () => {
    const {reportState} = useComplianceReports();

    return (
        <Paper elevation={0}>
            <Box p={2}>
                <ComplianceReportsHead />
                <Divider />
                <Box position='relative' pt={2}>
                    {reportState.loading && <KasLoadingBackDrop />}
                    <KasSwitch>
                        <KasSwitchWhen condition={!reportState.data}>
                            <Box pt={2}>
                                <NoResultsView text='Search result will appear here' />
                            </Box>
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={!!reportState.data}>
                            {!!reportState.data && <ComplianceReportsResult data={reportState.data} />}
                        </KasSwitchWhen>
                    </KasSwitch>
                </Box>
            </Box>
        </Paper>
    );
};
