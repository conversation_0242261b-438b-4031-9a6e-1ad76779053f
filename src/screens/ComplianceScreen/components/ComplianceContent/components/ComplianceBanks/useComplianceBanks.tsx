import React, {createContext, useContext, useEffect, useState} from 'react';
import {getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {DataStateInterface} from '@/interfaces';
import {
    ComplianceLoanStatisticModel,
    ComplianceLoanDeniedModel,
    ComplianceLoanFeesModel,
    ComplianceBankOFACModel,
    ComplianceBankFraudModel,
    ComplianceBankFinCenModel,
} from '@/screens/ComplianceScreen/interfaces';
import {ComplianceBanksItemModel, ComplianceBanksItemType, ComplianceBankStateType} from './interfaces';
import {AVAILABLE_BANKS_ITEMS, COMPLIANCE_BANKS_STATE} from './data';
import {Completable} from '@/interfaces';
import {selectUserPreferences, updateUserPreferences} from '@/lib/slices/userPreferencesSlice';
import {useAppDispatch, useAppSelector} from '@/lib/hooks';

type ComplianceDetailsModel =
    | ComplianceLoanDeniedModel
    | ComplianceLoanStatisticModel
    | ComplianceLoanFeesModel;

interface ComplianceBanksContextModel {
    bankItems: ComplianceBanksItemModel[];
    updateBankItems: (data: ComplianceBanksItemModel[]) => void;
    banksState: ComplianceBankStateType;
    loadBanks: (
        type: ComplianceBanksItemType,
        params: string,
    ) => Promise<Completable<ComplianceDetailsModel[]>>;
}

const ComplianceBanksContext = createContext<ComplianceBanksContextModel | undefined>(undefined);

export const ComplianceBanksProvider = ({children}: {children: React.ReactNode}) => {
    const dispatch = useAppDispatch();
    const userPreferences = useAppSelector(selectUserPreferences);
    const [bankItems, setBankItems] = useState<ComplianceBanksItemModel[]>([]);
    const [banksState, setBanksState] = useState(COMPLIANCE_BANKS_STATE);

    const updateBankItems = (sections: ComplianceBanksItemModel[]) => {
        const compliance = userPreferences.value?.compliance || {};
        setBankItems(sections);
        dispatch(updateUserPreferences({compliance: {...compliance, banks: {sections}}}));
    };

    const loadBanks = async (
        type: ComplianceBanksItemType,
        params: string,
    ): Promise<Completable<ComplianceDetailsModel[]>> => {
        const url = `/api/secured/compliance/banks/${type}?${params}`;

        setBanksState((prevState) => {
            return {
                ...prevState,
                [type]: (() => {
                    switch (type) {
                        case ComplianceBanksItemType.Fraud:
                            return getLoadingState(
                                prevState[type] as DataStateInterface<ComplianceBankFraudModel[]>,
                            );
                        case ComplianceBanksItemType.OFAC:
                            return getLoadingState(
                                prevState[type] as DataStateInterface<ComplianceBankOFACModel[]>,
                            );
                        case ComplianceBanksItemType.FinCen:
                        default:
                            return getLoadingState(
                                prevState[type] as DataStateInterface<ComplianceBankFinCenModel[]>,
                            );
                    }
                })(),
            };
        });

        const response = await apiRequest(url);

        setBanksState((prevState) => ({
            ...prevState,
            [type]: getLoadedState(response),
        }));

        return response;
    };

    const setSortedItems = () => {
        const sections = userPreferences.value?.compliance?.banks?.sections;

        if (sections) {
            setBankItems(sections);
        } else {
            setBankItems(AVAILABLE_BANKS_ITEMS);
        }
    };

    useEffect(setSortedItems, []);

    const value: ComplianceBanksContextModel = {
        bankItems,
        updateBankItems,
        banksState,
        loadBanks,
    };

    return <ComplianceBanksContext.Provider value={value}>{children}</ComplianceBanksContext.Provider>;
};

export function useComplianceBanks() {
    const context = useContext(ComplianceBanksContext);
    if (!context) {
        throw new Error('useComplianceBanks must be used within ComplianceBanksProvider');
    }
    return context;
}
