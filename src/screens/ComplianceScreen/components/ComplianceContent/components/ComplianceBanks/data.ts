import {ComplianceBanksItemModel, ComplianceBanksItemType, ComplianceBankStateType} from './interfaces';
import {getDefaultState} from '@/utils/DataStateUtils';
import {ComplianceLoanStatisticModel} from '@/screens/ComplianceScreen/interfaces';

export const AVAILABLE_BANKS_ITEMS: ComplianceBanksItemModel[] = [
    {
        id: '1',
        title: 'Fraud',
        type: ComplianceBanksItemType.Fraud,
    },
    {
        id: '2',
        title: 'OFAC',
        type: ComplianceBanksItemType.OFAC,
    },
    {
        id: '3',
        title: 'FinCen',
        type: ComplianceBanksItemType.FinCen,
    },
];

export const COMPLIANCE_BANKS_STATE: ComplianceBankStateType = Object.entries(ComplianceBanksItemType).reduce(
    (acc, [_, type]) => ({
        ...acc,
        [type]: () => getDefaultState<ComplianceLoanStatisticModel[]>(),
    }),
    {} as ComplianceBankStateType,
);
