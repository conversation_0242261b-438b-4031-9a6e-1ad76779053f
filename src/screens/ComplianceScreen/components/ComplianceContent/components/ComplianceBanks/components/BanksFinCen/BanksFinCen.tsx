import React from 'react';
import {BanksFinCenDetails, BanksFinCenHead} from './components';
import {
    ComplianceMoveableItem,
    useComplianceBanks,
} from '@/screens/ComplianceScreen/components/ComplianceContent/components';
import {ComplianceBanksItemModel, ComplianceBanksItemType} from './../../interfaces';

export const BanksFinCen = ({item}: {item: ComplianceBanksItemModel}) => {
    const {banksState} = useComplianceBanks();
    const curState = banksState[ComplianceBanksItemType.FinCen];

    return (
        <ComplianceMoveableItem
            title={item.title}
            loading={curState.loading}
            loadingError={curState.error}
            loaded={!!curState.data}
            PreviewComponent={<BanksFinCenHead />}
            DetailsComponent={<BanksFinCenDetails curState={curState} />}
        />
    );
};
