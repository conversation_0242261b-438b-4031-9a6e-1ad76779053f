import React from 'react';
import {BanksFraudDetails, BanksFraudHead} from './components';
import {
    ComplianceMoveableItem,
    useComplianceBanks,
} from '@/screens/ComplianceScreen/components/ComplianceContent/components';
import {ComplianceBanksItemModel, ComplianceBanksItemType} from './../../interfaces';

export const BanksFraud = ({item}: {item: ComplianceBanksItemModel}) => {
    const {banksState} = useComplianceBanks();
    const curState = banksState[ComplianceBanksItemType.Fraud];

    return (
        <ComplianceMoveableItem
            title={item.title}
            loading={curState.loading}
            loadingError={curState.error}
            loaded={!!curState.data}
            PreviewComponent={<BanksFraudHead />}
            DetailsComponent={<BanksFraudDetails curState={curState} />}
        />
    );
};
