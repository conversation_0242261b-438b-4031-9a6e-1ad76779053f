import React, {useState} from 'react';
import {Button, Grid2, MenuItem} from '@mui/material';
import {KasLOSMultipleSelect, KasMultipleStatesSelect, KasSelect} from '@/components';
import {useFormik} from 'formik';
import {BanksOFACValues, validationSchema} from './schema';
import {useComplianceBanks} from '@/screens/ComplianceScreen/components/ComplianceContent/components';
import {ComplianceBanksItemType} from './../../../../interfaces';
import {BANK_OFAC_TYPES, BankOFACType} from './data';
import {SelectChangeEvent} from '@mui/material/Select/SelectInput';

export const BanksOFACHead = () => {
    const {loadBanks} = useComplianceBanks();
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async (values: BanksOFACValues) => {
        const params = new URLSearchParams({
            type: values.type,
            ...(values.state?.length && {state: values.state.join(',')}),
            ...(values.los?.length && {los: values.los.join(',')}),
        }).toString();

        setSubmitting(true);
        await loadBanks(ComplianceBanksItemType.OFAC, params);
        setSubmitting(false);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            type: BankOFACType.Employee,
            state: [] as string[],
            los: [] as string[],
        },
        onSubmit,
        validationSchema,
    });

    const handleChangeDateType = (event: SelectChangeEvent) => {
        const newValue = event.target.value as string;

        formik.resetForm();
        formik.setFieldValue('type', newValue);
    };

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2}>
                <Grid2 size={3}>
                    <KasSelect
                        value={formik.values.type}
                        disabled={submitting}
                        label='Type'
                        onChange={handleChangeDateType}>
                        {BANK_OFAC_TYPES.map(({id, value, label}) => (
                            <MenuItem key={id} value={value}>
                                {label}
                            </MenuItem>
                        ))}
                    </KasSelect>
                </Grid2>
                {formik.values.type === BankOFACType.Employee && (
                    <>
                        <Grid2 size={3}>
                            <KasMultipleStatesSelect formik={formik} name='state' disabled={submitting} />
                        </Grid2>
                        <Grid2 size={3}>
                            <KasLOSMultipleSelect formik={formik} disabled={submitting} />
                        </Grid2>
                    </>
                )}
                <Grid2 size={2}>
                    <Button
                        fullWidth
                        variant='contained'
                        type='submit'
                        disabled={!formik.isValid || submitting}>
                        Search
                    </Button>
                </Grid2>
            </Grid2>
        </form>
    );
};
