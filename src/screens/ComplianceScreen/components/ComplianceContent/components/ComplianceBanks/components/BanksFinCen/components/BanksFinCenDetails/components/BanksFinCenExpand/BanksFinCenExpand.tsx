import React from 'react';
import {Paper, Stack} from '@mui/material';
import {KasInfo} from '@/components';
import {ComplianceBankFinCenModel} from '@/screens/ComplianceScreen/interfaces';
import Box from '@mui/material/Box';

export const BanksFinCenExpand = ({data}: {data: ComplianceBankFinCenModel}) => {
    return (
        <Stack spacing={1} py={1}>
            <Paper elevation={0}>
                <Box p={2}>
                    <KasInfo label='Matched On:' isInline>
                        <pre>{JSON.stringify(data.codes)}</pre>
                    </KasInfo>
                </Box>
            </Paper>
            <Paper elevation={0}>
                <Box p={2}>
                    <KasInfo label='Matches:' isInline>
                        <pre>{JSON.stringify(data.matches, null, 2)}</pre>
                    </KasInfo>
                </Box>
            </Paper>
        </Stack>
    );
};
