import './styles.scss';

import React from 'react';
import {useComplianceBanks} from './useComplianceBanks';
import {ComplianceBanksItemModel, ComplianceBanksItemType} from './interfaces';
import {BanksFinCen, BanksFraud, BanksOFAC} from './components';
import {SortableList} from '@/views';

export const ComplianceBanks = () => {
    const {bankItems, updateBankItems} = useComplianceBanks();

    const renderComplianceBankItem = (item: ComplianceBanksItemModel) => {
        switch (item.type) {
            case ComplianceBanksItemType.Fraud:
                return <BanksFraud key={item.type} item={item} />;
            case ComplianceBanksItemType.OFAC:
                return <BanksOFAC key={item.type} item={item} />;
            case ComplianceBanksItemType.FinCen:
                return <BanksFinCen key={item.type} item={item} />;
            default:
                return null;
        }
    };

    return (
        <div className='kas-compliance-banks-list'>
            <SortableList<ComplianceBanksItemModel>
                items={bankItems}
                updateItems={updateBankItems}
                renderItem={renderComplianceBankItem}
            />
        </div>
    );
};
